import React from 'react';
import { useF<PERSON>, Head, Link } from '@inertiajs/react';

export default function MemberRegisterForm() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        email: '',
        phone: '',
        gender: 'male',
        land_size: '',
        address: '',
        membership_date: '',
        identification_number: '',
    });

    function handleSubmit(e) {
        e.preventDefault();
        post('/members/register');
    }

    return (
        <>
            <Head title="Member Registration" />
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-gray-50 flex items-center justify-center p-4">
                <div className="w-full max-w-3xl bg-white rounded-xl shadow-lg overflow-hidden">
                    {/* Form Header */}
                    <div className="bg-green-600 py-6 px-8 text-center">
                        <h1 className="text-3xl font-bold text-white mb-1"><PERSON><PERSON><PERSON><PERSON>je</h1>
                        <p className="text-green-100 font-medium">Agricultural Cooperative</p>
                    </div>

                    {/* Form Content */}
                    <div className="p-8">
                        <h2 className="text-2xl font-bold text-center mb-8 text-gray-800">Become a Member</h2>
                        
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Name Field */}
                                <div className="space-y-2">
                                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                                        Full Name <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 p-3 border ${errors.name ? 'border-red-500' : ''}`}
                                        required
                                    />
                                    {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                                </div>

                                {/* Email Field */}
                                <div className="space-y-2">
                                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                                        Email <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        id="email"
                                        type="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 p-3 border ${errors.email ? 'border-red-500' : ''}`}
                                        required
                                    />
                                    {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Phone Field */}
                                <div className="space-y-2">
                                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                                        Phone Number <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        id="phone"
                                        type="tel"
                                        value={data.phone}
                                        onChange={(e) => setData('phone', e.target.value)}
                                        className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 p-3 border ${errors.phone ? 'border-red-500' : ''}`}
                                        required
                                    />
                                    {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
                                </div>

                                {/* Gender Field */}
                                <div className="space-y-2">
                                    <label htmlFor="gender" className="block text-sm font-medium text-gray-700">
                                        Gender <span className="text-red-500">*</span>
                                    </label>
                                    <select
                                        id="gender"
                                        value={data.gender}
                                        onChange={(e) => setData('gender', e.target.value)}
                                        className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 p-3 border ${errors.gender ? 'border-red-500' : ''}`}
                                        required
                                    >
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                        <option value="other">Other</option>
                                    </select>
                                    {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender}</p>}
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Land Size Field */}
                                <div className="space-y-2">
                                    <label htmlFor="land_size" className="block text-sm font-medium text-gray-700">
                                        Land Size (Hectares) <span className="text-red-500">*</span>
                                    </label>
                                    <div className="relative">
                                        <input
                                            id="land_size"
                                            type="number"
                                            step="0.1"
                                            min="0"
                                            value={data.land_size}
                                            onChange={(e) => setData('land_size', e.target.value)}
                                            className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 p-3 border ${errors.land_size ? 'border-red-500' : ''}`}
                                            required
                                        />
                                        <span className="absolute right-3 top-3 text-gray-500">ha</span>
                                    </div>
                                    {errors.land_size && <p className="text-red-500 text-sm mt-1">{errors.land_size}</p>}
                                </div>

                                {/* Membership Date */}
                                <div className="space-y-2">
                                    <label htmlFor="membership_date" className="block text-sm font-medium text-gray-700">
                                        Membership Date <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        id="membership_date"
                                        type="date"
                                        value={data.membership_date}
                                        onChange={(e) => setData('membership_date', e.target.value)}
                                        className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 p-3 border ${errors.membership_date ? 'border-red-500' : ''}`}
                                        required
                                    />
                                    {errors.membership_date && <p className="text-red-500 text-sm mt-1">{errors.membership_date}</p>}
                                </div>
                            </div>

                            {/* Address Field */}
                            <div className="space-y-2">
                                <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                                    Address <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    id="address"
                                    value={data.address}
                                    onChange={(e) => setData('address', e.target.value)}
                                    className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 p-3 border ${errors.address ? 'border-red-500' : ''}`}
                                    rows="3"
                                    required
                                ></textarea>
                                {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
                            </div>

                            {/* ID Number Field */}
                            <div className="space-y-2">
                                <label htmlFor="identification_number" className="block text-sm font-medium text-gray-700">
                                    Identification Number <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="identification_number"
                                    type="text"
                                    value={data.identification_number}
                                    onChange={(e) => setData('identification_number', e.target.value)}
                                    className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 p-3 border ${errors.identification_number ? 'border-red-500' : ''}`}
                                    required
                                />
                                {errors.identification_number && <p className="text-red-500 text-sm mt-1">{errors.identification_number}</p>}
                            </div>

                            {/* Form Footer */}
                            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-gray-200">
                                <Link href="/" className="text-green-600 hover:text-green-800 text-sm font-medium flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                                    </svg>
                                    Back to Home
                                </Link>
                                <button
                                    type="submit"
                                    disabled={processing}
                                    className="w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-lg shadow-md hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-75 flex items-center justify-center"
                                >
                                    {processing ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Processing...
                                        </>
                                    ) : 'Register Now'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    );
}