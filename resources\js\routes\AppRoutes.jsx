import React from 'react';
import { usePage } from '@inertiajs/inertia-react';

// Import your page components
import Home from '../pages/Home';
import Login from '../pages/Login';
import Register from '../pages/Register';
import NotFound from '../pages/NotFound';
import Loans from '../pages/Loans';
import Members from '../pages/Members';
import Savings from '../pages/Savings';
import Contributions from '../pages/Contributions';
import Reports from '../pages/Reports';
import Settings from '../pages/Settings';
import AdminDashboard from '../pages/Dashboard/AdminDashboard';
import StaffDashboard from '../pages/Dashboard/StaffDashboard';
import MemberDashboard from '../pages/Dashboard/MemberDashboard';

const AppRoutes = () => {
  const { component } = usePage();
  
  // Map of component names to actual components
  const components = {
    'Home': Home,
    'Login': Login,
    'Register': Register,
    'NotFound': NotFound,
    'Loans': Loans,
    'Members': Members,
    'Savings': Savings,
    'Contributions': Contributions,
    'Reports': Reports,
    'Settings': Settings,
    'Dashboard/AdminDashboard': AdminDashboard,
    'Dashboard/StaffDashboard': StaffDashboard,
    'Dashboard/MemberDashboard': MemberDashboard,
  };

  // Get the appropriate component based on the name from Inertia
  const Component = components[component] || NotFound;

  return (
    <div className="min-h-screen px-4 py-6">
      <Component />
    </div>
  );
};

export default AppRoutes;
