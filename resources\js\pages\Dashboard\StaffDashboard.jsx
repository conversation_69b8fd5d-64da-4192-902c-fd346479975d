import React from 'react';
import { motion } from 'framer-motion';
import { Head, Link } from '@inertiajs/react';
import { router } from '@inertiajs/react';
import StaffLayout from '@Layouts/StaffLayout';

const StaffDashboard = ({ auth }) => {
  const handleLogout = () => {
    router.post('/logout');
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  const cardHover = {
    scale: 1.03,
    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
    transition: {
      type: 'spring',
      stiffness: 300
    }
  };

  return (
    <StaffLayout user={auth.user} header="Staff Dashboard">
      <Head>
        <title>Staff Dashboard | Siyamphanje Cooperative</title>
      </Head>

      
    </StaffLayout>
  );
};

export default StaffDashboard;