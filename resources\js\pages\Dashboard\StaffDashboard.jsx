import React from 'react';
import { motion } from 'framer-motion';
import { Head, <PERSON> } from '@inertiajs/react';
import { router } from '@inertiajs/react';
import StaffLayout from '@Layouts/StaffLayout';

const StaffDashboard = ({ auth }) => {
  const handleLogout = () => {
    router.post('/logout');
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  const cardHover = {
    scale: 1.03,
    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
    transition: {
      type: 'spring',
      stiffness: 300
    }
  };

  return (
    <StaffLayout user={auth.user} header="Staff Dashboard">
      <Head>
        <title>Staff Dashboard | Siyamphanje Cooperative</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
        {/* Dashboard Header */}
        {/* <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-4 mb-6"
        >
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-2xl font-bold text-green-800">Staff Dashboard</h1>
              <p className="text-sm text-gray-600">Welcome, {auth.user.name}</p>
            </div>
            <div className="flex items-center space-x-4 mt-3 sm:mt-0">
              <div className="bg-gray-100 px-3 py-1 rounded-full text-sm font-medium">
                Staff ID: {auth.user.staff_id || '*********'}
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-1 text-red-600 hover:text-red-800 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span className="hidden sm:inline">Logout</span>
              </button>
            </div>
          </div>
        </motion.div> */}

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Quick Actions Sidebar */}
          <motion.nav 
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="w-full lg:w-64 bg-white p-6 rounded-xl shadow-sm"
          >
            <h2 className="text-xl font-semibold text-green-800 mb-6 pb-2 border-b border-gray-200">Quick Actions</h2>
            <motion.ul 
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-2"
            >
              <motion.li 
                variants={itemVariants}
                whileHover={{ x: 5 }}
                className="px-4 py-3 rounded-lg bg-green-800 text-white font-medium cursor-pointer"
              >
                Member Registration
              </motion.li>
              <motion.li 
                variants={itemVariants}
                whileHover={{ x: 5 }}
                className="px-4 py-3 rounded-lg hover:bg-green-50 text-gray-700 font-medium cursor-pointer transition-colors"
              >
                Savings Collection
              </motion.li>
              <motion.li 
                variants={itemVariants}
                whileHover={{ x: 5 }}
                className="px-4 py-3 rounded-lg hover:bg-green-50 text-gray-700 font-medium cursor-pointer transition-colors"
              >
                Loan Processing
              </motion.li>
              <motion.li 
                variants={itemVariants}
                whileHover={{ x: 5 }}
                className="px-4 py-3 rounded-lg hover:bg-green-50 text-gray-700 font-medium cursor-pointer transition-colors"
              >
                Contribution Tracking
              </motion.li>
              <motion.li 
                variants={itemVariants}
                whileHover={{ x: 5 }}
                className="px-4 py-3 rounded-lg hover:bg-green-50 text-gray-700 font-medium cursor-pointer transition-colors"
              >
                Crop Records
              </motion.li>
              <motion.li 
                variants={itemVariants}
                whileHover={{ x: 5 }}
                className="px-4 py-3 rounded-lg hover:bg-green-50 text-gray-700 font-medium cursor-pointer transition-colors"
              >
                Market Updates
              </motion.li>
            </motion.ul>
          </motion.nav>

          {/* Main Content Area */}
          <motion.main 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="flex-1"
          >
            {/* Stats Grid */}
            <motion.div 
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-8"
            >
              <motion.div 
                variants={itemVariants}
                whileHover={cardHover}
                className="bg-white p-6 rounded-xl shadow-sm border-t-4 border-green-600"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-gray-500 text-sm font-medium">Members Today</h3>
                    <p className="text-2xl font-bold text-gray-800 mt-1">24</p>
                  </div>
                  <div className="text-green-100 bg-green-600 rounded-full p-3">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v1h8v-1zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-1a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v1h-3zM4.75 12.094A5.973 5.973 0 004 15v1H1v-1a3 3 0 013.75-2.906z" />
                    </svg>
                  </div>
                </div>
              </motion.div>
              
              <motion.div 
                variants={itemVariants}
                whileHover={cardHover}
                className="bg-white p-6 rounded-xl shadow-sm border-t-4 border-blue-600"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-gray-500 text-sm font-medium">Daily Savings</h3>
                    <p className="text-2xl font-bold text-gray-800 mt-1">K 850,000</p>
                  </div>
                  <div className="text-blue-100 bg-blue-600 rounded-full p-3">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </motion.div>
              
              <motion.div 
                variants={itemVariants}
                whileHover={cardHover}
                className="bg-white p-6 rounded-xl shadow-sm border-t-4 border-amber-600"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-gray-500 text-sm font-medium">Pending Loans</h3>
                    <p className="text-2xl font-bold text-gray-800 mt-1">7</p>
                  </div>
                  <div className="text-amber-100 bg-amber-600 rounded-full p-3">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </motion.div>
              
              <motion.div 
                variants={itemVariants}
                whileHover={cardHover}
                className="bg-white p-6 rounded-xl shadow-sm border-t-4 border-emerald-600"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-gray-500 text-sm font-medium">Crop Updates</h3>
                    <p className="text-2xl font-bold text-gray-800 mt-1">12</p>
                  </div>
                  <div className="text-emerald-100 bg-emerald-600 rounded-full p-3">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Divider */}
            <div className="h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent my-8"></div>

            {/* Activity Section */}
            <motion.section
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              <h2 className="text-2xl font-semibold text-green-800 mb-6">Today's Activities</h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 mb-8">
                <motion.div
                  whileHover={cardHover}
                  className="bg-white p-6 rounded-xl shadow-sm border-l-4 border-red-500"
                >
                  <h3 className="text-xl font-semibold text-green-800 mb-3">Loan Approvals</h3>
                  <p className="text-gray-600 mb-4">3 loans awaiting approval</p>
                  <motion.button
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    className="bg-green-800 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  >
                    Review Now
                  </motion.button>
                </motion.div>
                
                <motion.div
                  whileHover={cardHover}
                  className="bg-white p-6 rounded-xl shadow-sm border-l-4 border-green-500"
                >
                  <h3 className="text-xl font-semibold text-green-800 mb-3">Member Meetings</h3>
                  <p className="text-gray-600 mb-4">2 scheduled meetings today</p>
                  <motion.button
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    className="bg-green-800 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  >
                    View Schedule
                  </motion.button>
                </motion.div>
                
                <motion.div
                  whileHover={cardHover}
                  className="bg-white p-6 rounded-xl shadow-sm border-l-4 border-blue-500"
                >
                  <h3 className="text-xl font-semibold text-green-800 mb-3">Crop Reports</h3>
                  <p className="text-gray-600 mb-4">5 farmers submitted reports</p>
                  <motion.button
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    className="bg-green-800 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  >
                    View Reports
                  </motion.button>
                </motion.div>
                
                <motion.div
                  whileHover={cardHover}
                  className="bg-white p-6 rounded-xl shadow-sm border-l-4 border-amber-500"
                >
                  <h3 className="text-xl font-semibold text-green-800 mb-3">Market Prices</h3>
                  <p className="text-gray-600 mb-4">Update today's prices</p>
                  <motion.button
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    className="bg-green-800 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  >
                    Update Prices
                  </motion.button>
                </motion.div>
              </div>
            </motion.section>

            {/* Quick Links */}
            <motion.section
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-semibold text-green-800 mb-6">Quick Links</h2>
              
              <div className="flex flex-wrap gap-4">
                <motion.button
                  whileHover={{ y: -3, boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)' }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-white border border-gray-200 px-6 py-3 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all"
                >
                  Member Directory
                </motion.button>
                
                <motion.button
                  whileHover={{ y: -3, boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)' }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-white border border-gray-200 px-6 py-3 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all"
                >
                  Savings Ledger
                </motion.button>
                
                <motion.button
                  whileHover={{ y: -3, boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)' }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-white border border-gray-200 px-6 py-3 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all"
                >
                  Loan Calculator
                </motion.button>
                
                <motion.button
                  whileHover={{ y: -3, boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)' }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-white border border-gray-200 px-6 py-3 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all"
                >
                  Training Materials
                </motion.button>
              </div>
            </motion.section>
          </motion.main>
        </div>
      </div>
    </StaffLayout>
  );
};

export default StaffDashboard;