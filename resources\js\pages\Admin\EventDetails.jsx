import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function AdminEventDetails({ auth, event }) {
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    const { data: editData, setData: setEditData, put, processing: editProcessing, errors: editErrors } = useForm({
        title: event.title,
        description: event.description,
        type: event.type,
        event_date: event.event_date,
        start_time: event.start_time ? event.start_time.substring(0, 5) : '',
        end_time: event.end_time ? event.end_time.substring(0, 5) : '',
        location: event.location,
        agenda: event.agenda || '',
        max_attendees: event.max_attendees || '',
        is_public: event.is_public,
    });

    const { delete: deleteEvent, processing: deleteProcessing } = useForm();

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-ZM', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
    };

    const formatTime = (timeString) => {
        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-ZM', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getTypeColor = (type) => {
        const colors = {
            meeting: 'bg-blue-100 text-blue-800',
            training: 'bg-green-100 text-green-800',
            field_day: 'bg-yellow-100 text-yellow-800',
            social: 'bg-purple-100 text-purple-800',
            market_day: 'bg-orange-100 text-orange-800',
            other: 'bg-gray-100 text-gray-800'
        };
        return colors[type] || colors.other;
    };

    const getAttendanceStatusColor = (status) => {
        const colors = {
            attending: 'bg-green-100 text-green-800',
            not_attending: 'bg-red-100 text-red-800',
            maybe: 'bg-yellow-100 text-yellow-800',
            pending: 'bg-gray-100 text-gray-800'
        };
        return colors[status] || colors.pending;
    };

    const handleUpdate = (e) => {
        e.preventDefault();
        put(`/admin/events/${event.id}`, {
            onSuccess: () => {
                setShowEditModal(false);
            }
        });
    };

    const handleDelete = () => {
        deleteEvent(`/admin/events/${event.id}`, {
            onSuccess: () => {
                // Redirect will be handled by the controller
            }
        });
    };

    return (
        <AdminLayout auth={auth}>
            <Head title={`${event.title} - Event Details`} />
            
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-4xl mx-auto">
                    {/* Breadcrumb */}
                    <nav className="flex mb-6" aria-label="Breadcrumb">
                        <ol className="inline-flex items-center space-x-1 md:space-x-3">
                            <li className="inline-flex items-center">
                                <Link href="/dashboard/admin" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                    <svg className="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                    </svg>
                                    Dashboard
                                </Link>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <Link href="/admin/events" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                                        Events
                                    </Link>
                                </div>
                            </li>
                            <li aria-current="page">
                                <div className="flex items-center">
                                    <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">Event Details</span>
                                </div>
                            </li>
                        </ol>
                    </nav>

                    {/* Event Header */}
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
                        <div className="bg-gradient-to-r from-green-600 to-green-700 px-8 py-6 text-white">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                    <div className="p-3 bg-white bg-opacity-20 rounded-lg">
                                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h1 className="text-3xl font-bold">{event.title}</h1>
                                        <div className="flex items-center space-x-4 mt-2">
                                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white`}>
                                                {event.type.replace('_', ' ').toUpperCase()}
                                            </span>
                                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${event.is_public ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                                {event.is_public ? 'PUBLIC' : 'PRIVATE'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex space-x-3">
                                    <button
                                        onClick={() => setShowEditModal(true)}
                                        className="bg-white bg-opacity-20 text-white px-4 py-2 rounded-md hover:bg-opacity-30 transition-colors"
                                    >
                                        Edit Event
                                    </button>
                                    <button
                                        onClick={() => setShowDeleteModal(true)}
                                        className="bg-red-600 bg-opacity-80 text-white px-4 py-2 rounded-md hover:bg-opacity-100 transition-colors"
                                    >
                                        Delete Event
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="p-8">
                            {/* Event Details */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                                <div className="space-y-4">
                                    <div className="flex items-center space-x-3">
                                        <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                        </svg>
                                        <div>
                                            <p className="text-sm text-gray-500">Date</p>
                                            <p className="font-semibold text-gray-900">{formatDate(event.event_date)}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-3">
                                        <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                        </svg>
                                        <div>
                                            <p className="text-sm text-gray-500">Time</p>
                                            <p className="font-semibold text-gray-900">
                                                {formatTime(event.start_time)}
                                                {event.end_time && ` - ${formatTime(event.end_time)}`}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-3">
                                        <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                        </svg>
                                        <div>
                                            <p className="text-sm text-gray-500">Location</p>
                                            <p className="font-semibold text-gray-900">{event.location}</p>
                                        </div>
                                    </div>

                                    {event.max_attendees && (
                                        <div className="flex items-center space-x-3">
                                            <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                                            </svg>
                                            <div>
                                                <p className="text-sm text-gray-500">Capacity</p>
                                                <p className="font-semibold text-gray-900">{event.attendees.length} / {event.max_attendees} attendees</p>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <p className="text-sm text-gray-500 mb-2">Created by</p>
                                        <p className="font-semibold text-gray-900">{event.created_by}</p>
                                    </div>

                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <h4 className="text-sm font-medium text-blue-900 mb-2">Event Statistics</h4>
                                        <div className="space-y-1 text-sm text-blue-700">
                                            <p>Total Responses: {event.attendees.length}</p>
                                            <p>Attending: {event.attendees.filter(a => a.response_status === 'attending').length}</p>
                                            <p>Not Attending: {event.attendees.filter(a => a.response_status === 'not_attending').length}</p>
                                            <p>Maybe: {event.attendees.filter(a => a.response_status === 'maybe').length}</p>
                                            <p>Pending: {event.attendees.filter(a => a.response_status === 'pending').length}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Description */}
                            <div className="mb-8">
                                <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                                <div className="prose prose-sm max-w-none text-gray-700">
                                    <p>{event.description}</p>
                                </div>
                            </div>

                            {/* Agenda */}
                            {event.agenda && (
                                <div className="mb-8">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Agenda</h3>
                                    <div className="prose prose-sm max-w-none text-gray-700">
                                        <p className="whitespace-pre-line">{event.agenda}</p>
                                    </div>
                                </div>
                            )}

                            {/* Attendees */}
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    Attendees ({event.attendees.length})
                                </h3>
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Response</th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attended</th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comments</th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {event.attendees.map((attendee, index) => (
                                                <tr key={index} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                                <span className="text-green-600 font-medium text-sm">
                                                                    {attendee.farmer_name ? attendee.farmer_name.charAt(0).toUpperCase() : 'M'}
                                                                </span>
                                                            </div>
                                                            <div className="ml-3">
                                                                <p className="font-medium text-gray-900">{attendee.farmer_name || 'Member'}</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getAttendanceStatusColor(attendee.response_status)}`}>
                                                            {attendee.response_status.replace('_', ' ').toUpperCase()}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {attendee.attended ? (
                                                            <span className="text-green-600">✓ Yes</span>
                                                        ) : (
                                                            <span className="text-gray-400">- No</span>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 text-sm text-gray-900">
                                                        {attendee.comments || '-'}
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="mt-8 flex space-x-4">
                                <Link
                                    href="/admin/events"
                                    className="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors"
                                >
                                    Back to Events
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Edit Event Modal */}
            {showEditModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Edit Event</h3>

                        <form onSubmit={handleUpdate}>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Title</label>
                                    <input
                                        type="text"
                                        value={editData.title}
                                        onChange={(e) => setEditData('title', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        required
                                    />
                                    {editErrors.title && <p className="mt-1 text-sm text-red-600">{editErrors.title}</p>}
                                </div>

                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                    <textarea
                                        value={editData.description}
                                        onChange={(e) => setEditData('description', e.target.value)}
                                        rows={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        required
                                    />
                                    {editErrors.description && <p className="mt-1 text-sm text-red-600">{editErrors.description}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Type</label>
                                    <select
                                        value={editData.type}
                                        onChange={(e) => setEditData('type', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    >
                                        <option value="training">Training</option>
                                        <option value="field_day">Field Day</option>
                                        <option value="social">Social Event</option>
                                        <option value="market_day">Market Day</option>
                                        <option value="other">Other</option>
                                    </select>
                                    {editErrors.type && <p className="mt-1 text-sm text-red-600">{editErrors.type}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Date</label>
                                    <input
                                        type="date"
                                        value={editData.event_date}
                                        onChange={(e) => setEditData('event_date', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        required
                                    />
                                    {editErrors.event_date && <p className="mt-1 text-sm text-red-600">{editErrors.event_date}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                                    <input
                                        type="time"
                                        value={editData.start_time}
                                        onChange={(e) => setEditData('start_time', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        required
                                    />
                                    {editErrors.start_time && <p className="mt-1 text-sm text-red-600">{editErrors.start_time}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">End Time (Optional)</label>
                                    <input
                                        type="time"
                                        value={editData.end_time}
                                        onChange={(e) => setEditData('end_time', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    />
                                    {editErrors.end_time && <p className="mt-1 text-sm text-red-600">{editErrors.end_time}</p>}
                                </div>

                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                    <input
                                        type="text"
                                        value={editData.location}
                                        onChange={(e) => setEditData('location', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        required
                                    />
                                    {editErrors.location && <p className="mt-1 text-sm text-red-600">{editErrors.location}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Max Attendees (Optional)</label>
                                    <input
                                        type="number"
                                        value={editData.max_attendees}
                                        onChange={(e) => setEditData('max_attendees', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        min="1"
                                    />
                                    {editErrors.max_attendees && <p className="mt-1 text-sm text-red-600">{editErrors.max_attendees}</p>}
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        checked={editData.is_public}
                                        onChange={(e) => setEditData('is_public', e.target.checked)}
                                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                    />
                                    <label className="ml-2 block text-sm text-gray-900">
                                        Public Event (visible to all members)
                                    </label>
                                </div>

                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Agenda (Optional)</label>
                                    <textarea
                                        value={editData.agenda}
                                        onChange={(e) => setEditData('agenda', e.target.value)}
                                        rows={4}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        placeholder="Event agenda or additional details..."
                                    />
                                    {editErrors.agenda && <p className="mt-1 text-sm text-red-600">{editErrors.agenda}</p>}
                                </div>
                            </div>

                            <div className="flex space-x-3">
                                <button
                                    type="submit"
                                    disabled={editProcessing}
                                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                                >
                                    {editProcessing ? 'Updating...' : 'Update Event'}
                                </button>
                                <button
                                    type="button"
                                    onClick={() => setShowEditModal(false)}
                                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            {/* Delete Confirmation Modal */}
            {showDeleteModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg max-w-md w-full p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Event</h3>
                        <p className="text-gray-600 mb-6">
                            Are you sure you want to delete "{event.title}"? This action cannot be undone and will remove all attendee data.
                        </p>

                        <div className="flex space-x-3">
                            <button
                                onClick={handleDelete}
                                disabled={deleteProcessing}
                                className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                            >
                                {deleteProcessing ? 'Deleting...' : 'Delete Event'}
                            </button>
                            <button
                                onClick={() => setShowDeleteModal(false)}
                                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </AdminLayout>
    );
}
