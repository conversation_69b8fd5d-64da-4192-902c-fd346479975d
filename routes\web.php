<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MemberController;
use App\Http\Controllers\MemberApplicationController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\LoanController;
use App\Http\Controllers\ContributionController;
use App\Http\Controllers\SavingsController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\EquipmentController;
use App\Http\Controllers\EquipmentRequestController;
use App\Http\Controllers\PasswordResetController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\TransportController;
use App\Http\Controllers\CropController;
use App\Http\Controllers\MeetingController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\MembershipRegistrationController;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Home');
})->name('home');

// Membership registration routes (public)
Route::get('/membership/register', [MembershipRegistrationController::class, 'showRegistrationForm'])->name('membership.register');
Route::post('/membership/register', [MembershipRegistrationController::class, 'register'])->name('membership.register.submit');
Route::get('/membership/success', [MembershipRegistrationController::class, 'success'])->name('membership.success');
Route::get('/membership/check-availability', [MembershipRegistrationController::class, 'checkAvailability'])->name('membership.check-availability');

// Debug route to test registration process
Route::get('/debug-registration', function () {
    return 'Registration route is working. Controller: ' . \App\Http\Controllers\MembershipRegistrationController::class;
});

// Debug route to check roles
Route::get('/debug-roles', function () {
    $roles = \App\Models\Role::all();
    return response()->json($roles);
});

// Fix user roles (run this once to fix existing users)
Route::get('/fix-user-roles', function () {
    $memberRole = \App\Models\Role::where('name', 'member')->first();
    $staffRole = \App\Models\Role::where('name', 'staff')->first();

    if (!$memberRole) {
        return 'Member role not found!';
    }

    // Find users who should be members but have wrong role
    // You can identify them by email or other criteria
    $usersToFix = \App\Models\User::where('role_id', $staffRole->id)
        ->whereHas('farmer') // Users who have farmer profiles should be members
        ->get();

    $fixed = 0;
    foreach ($usersToFix as $user) {
        $user->update(['role_id' => $memberRole->id]);
        $fixed++;
    }

    return "Fixed {$fixed} users. Member role ID: {$memberRole->id}, Staff role ID: {$staffRole->id}";
});

// Debug route to test the full registration process
Route::post('/debug-membership-register', function (\Illuminate\Http\Request $request) {
    Log::info('Debug registration started', $request->all());

    try {
        $controller = new \App\Http\Controllers\MembershipRegistrationController();
        $result = $controller->register($request);
        Log::info('Debug registration completed successfully');
        return $result;
    } catch (\Exception $e) {
        Log::error('Debug registration failed: ' . $e->getMessage());
        return response()->json(['error' => $e->getMessage()], 500);
    }
});

// Test email route (remove this after testing)
Route::get('/test-email', function () {
    try {
        Mail::raw('This is a test email from Siyamphanje Cooperative', function ($message) {
            $message->to('<EMAIL>')
                ->subject('Test Email - Siyamphanje Cooperative');
        });
        return 'Test email sent successfully!';
    } catch (\Exception $e) {
        return 'Email failed: ' . $e->getMessage();
    }
});

// Test membership email template
Route::get('/test-membership-email', function () {
    try {
        $testUser = (object) [
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ];
        $testPassword = 'TestUser123!';

        Mail::send('emails.welcome-member', [
            'user' => $testUser,
            'password' => $testPassword,
            'loginUrl' => route('login'),
        ], function ($message) use ($testUser) {
            $message->to($testUser->email, $testUser->name)
                ->subject('Welcome to Siyamphanje Agricultural Cooperative!');
        });

        return 'Membership welcome email sent successfully!';
    } catch (\Exception $e) {
        return 'Membership email failed: ' . $e->getMessage();
    }
});






// Route::middleware(['auth'])->group(function () {
//     // Main dashboard route
//     Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
//     // Member registration routes
//     Route::middleware(['auth'])->group(function () {
//         // Self-registration
//         Route::get('/members/register', [MemberController::class, 'create'])->name('members.register');
//         Route::post('/members', [MemberController::class, 'store'])->name('members.store');

//         // Admin management

//     });

//     // Other routes using direct URLs
//     Route::get('/members', [MemberController::class, 'index'])->name('members');
//     Route::get('/savings', [SavingsController::class, 'index'])->name('savings');
//     Route::get('/contributions', [ContributionController::class, 'index'])->name('contributions');
//     Route::get('/loans', [LoanController::class, 'index'])->name('loans');
//     Route::get('/reports', [ReportController::class, 'index'])->name('reports');
//     Route::get('/settings', [SettingsController::class, 'index'])->name('settings');

//     // Action routes
//     Route::post('/members', [MemberController::class, 'store']);
//     Route::post('/reports/generate', [ReportController::class, 'generate']);
//     Route::post('/announcements/send', [AnnouncementController::class, 'send']);
//     Route::post('/loans/process', [LoanController::class, 'process']);
// });


Route::get('/members/create', [MemberController::class, 'create'])->name('members.create');

Route::get('/login', function () {
    return Inertia::render('Login');
})->name('login')->middleware('guest');

// Route::post('/login', [AuthController::class, 'login'])->name('login.attempt')->middleware('guest');
// Route::prefix('admin')->middleware(['role:admin'])->group(function () {
//     Route::get('/members', [MemberController::class, 'index'])->name('admin.members.index');
//     Route::get('/members/create', [MemberController::class, 'create'])->name('admin.members.create');
//     Route::post('/members', [MemberController::class, 'store'])->name('admin.members.store');
//     Route::get('/members/{member}/edit', [MemberController::class, 'edit'])->name('admin.members.edit');
//     Route::put('/members/{member}', [MemberController::class, 'update'])->name('admin.members.update');
// });
// Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// routes for login
// routes/web.php


// Authentication routes
Route::middleware('guest')->group(function () {
    // Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.attempt');

    // Password Reset routes
    Route::get('/forgot-password', [PasswordResetController::class, 'showForgotPasswordForm'])->name('password.request');
    Route::post('/password/email', [PasswordResetController::class, 'sendResetLink'])->name('password.email');
    Route::get('/password/reset/{token}', [PasswordResetController::class, 'showResetForm'])->name('password.reset');
    Route::post('/password/reset', [PasswordResetController::class, 'resetPassword'])->name('password.update');
});

// Logout route (needs to be accessible to authenticated users)
Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Protected routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Main dashboard route - redirects based on user role
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Dashboard routes with role protection
    Route::get('/dashboard/admin', [DashboardController::class, 'adminDashboard'])->name('dashboard.admin')->middleware('admin');
    Route::get('/dashboard/staff', [DashboardController::class, 'staffDashboard'])->name('dashboard.staff')->middleware('role:staff,admin');
    Route::get('/dashboard/member', [DashboardController::class, 'memberDashboard'])->name('dashboard.member')->middleware('role:member');
    Route::get('/loans/apply', function () {
        return Inertia::render('LoanApplication');
    })->name('loans.apply');
    Route::post('/loans', [LoanController::class, 'store'])->name('loans.store');
    Route::get('/savings/deposit', function () {
        return Inertia::render('DepositForm');
    })->name('savings.deposit');
    Route::post('/savings', [SavingsController::class, 'store'])->name('savings.store');
    Route::get('/crops/register', function () {
        return Inertia::render('CropRegistrationForm', [
            'crops' => \App\Models\Crop::all(),
        ]);
    })->name('crops.register');
    // Route::post('/crops', [CropController::class, 'store'])->name('crops.store');

    // Equipment requests
    Route::get('/equipment/available', [EquipmentController::class, 'getAvailable'])->name('equipment.available');
    Route::get('/equipment/request', [EquipmentRequestController::class, 'index'])->name('equipment.request');
    Route::post('/equipment/request', [EquipmentRequestController::class, 'store'])->name('equipment.request.store');
    Route::get('/equipment/request/{id}', [EquipmentRequestController::class, 'show'])->name('equipment.request.show');

    // Quick Action routes for members
    Route::get('/savings/deposit', [SavingsController::class, 'showDepositForm'])->name('savings.deposit');
    Route::post('/savings/deposit', [SavingsController::class, 'deposit'])->name('savings.deposit.store');
    Route::get('/loans/apply', [LoanController::class, 'create'])->name('loans.apply');
    Route::post('/loans/apply', [LoanController::class, 'store'])->name('loans.apply.store');
    Route::get('/crops/register', [CropController::class, 'create'])->name('crops.register');
    Route::post('/crops/register', [CropController::class, 'store'])->name('crops.register.store');
    Route::get('/member/reports', [ReportController::class, 'memberIndex'])->name('member.reports.index');

    // Password management for members
    Route::get('/password/change', [PasswordResetController::class, 'showChangePasswordForm'])->name('password.change');
    Route::post('/password/change', [PasswordResetController::class, 'changePassword'])->name('password.change.update');

    // Document routes for members
    Route::get('/documents', [DocumentController::class, 'index'])->name('documents.index');
    Route::get('/documents/download/{id}', [DocumentController::class, 'download'])->name('documents.download');

    // Member transport routes
    Route::middleware(['role:member'])->prefix('member')->group(function () {
        Route::get('/transport', [\App\Http\Controllers\Member\TransportController::class, 'index'])->name('member.transport.index');
        Route::get('/transport/fees', [\App\Http\Controllers\Member\TransportController::class, 'fees'])->name('member.transport.fees');
        Route::get('/transport/download-report', [\App\Http\Controllers\Member\TransportController::class, 'downloadReport'])->name('member.transport.download-report');
        Route::get('/transport/request', [\App\Http\Controllers\Member\TransportController::class, 'requestTransport'])->name('member.transport.request');
        Route::post('/transport/request', [\App\Http\Controllers\Member\TransportController::class, 'storeRequest'])->name('member.transport.request.store');
    });

    // Admin routes - ALL PROTECTED WITH ADMIN MIDDLEWARE
    Route::middleware(['admin'])->prefix('admin')->group(function () {
        // Member management
        Route::get('/members', [MemberController::class, 'index'])->name('admin.members');
        Route::get('/members/create', [MemberController::class, 'create'])->name('admin.members.create');
        Route::post('/members', [MemberController::class, 'store'])->name('admin.members.store');
        Route::get('/members/{id}', [MemberController::class, 'show'])->name('admin.members.show');
        Route::get('/members/{id}/edit', [MemberController::class, 'edit'])->name('admin.members.edit');
        Route::put('/members/{id}', [MemberController::class, 'update'])->name('admin.members.update');
        Route::post('/members/{id}/toggle-status', [MemberController::class, 'toggleStatus'])->name('admin.members.toggle-status');
        Route::delete('/members/{id}', [MemberController::class, 'destroy'])->name('admin.members.destroy');

        // Reports management
        Route::get('/reports', [ReportController::class, 'index'])->name('admin.reports');
        Route::post('/reports/generate', [ReportController::class, 'generateReport'])->name('admin.reports.generate');

        // Loan management
        Route::get('/loans', [LoanController::class, 'adminIndex'])->name('admin.loans.index');
        Route::get('/loans/create', [LoanController::class, 'create'])->name('admin.loans.create');
        Route::post('/loans', [LoanController::class, 'adminStore'])->name('admin.loans.store');
        Route::get('/loans/{id}', [LoanController::class, 'show'])->name('admin.loans.show');
        Route::post('/loans/{id}/approve', [LoanController::class, 'approve'])->name('admin.loans.approve');
        Route::post('/loans/{id}/reject', [LoanController::class, 'reject'])->name('admin.loans.reject');
        Route::post('/loans/{id}/disburse', [LoanController::class, 'disburse'])->name('admin.loans.disburse');
        Route::post('/loans/{id}/repayment', [LoanController::class, 'recordRepayment'])->name('admin.loans.repayment');
    });

    // Additional admin routes - ALL PROTECTED WITH ADMIN MIDDLEWARE
    Route::middleware(['admin'])->prefix('admin')->group(function () {
        // Contribution management
        Route::get('/contributions', [ContributionController::class, 'adminIndex'])->name('admin.contributions.index');
        Route::get('/contributions/create', [ContributionController::class, 'create'])->name('admin.contributions.create');
        Route::post('/contributions', [ContributionController::class, 'store'])->name('admin.contributions.store');
        Route::get('/contributions/{id}', [ContributionController::class, 'show'])->name('admin.contributions.show');
        Route::get('/contributions/{id}/edit', [ContributionController::class, 'edit'])->name('admin.contributions.edit');
        Route::put('/contributions/{id}', [ContributionController::class, 'update'])->name('admin.contributions.update');
        Route::delete('/contributions/{id}', [ContributionController::class, 'destroy'])->name('admin.contributions.destroy');

        // Equipment inventory management
        Route::get('/equipment', [EquipmentController::class, 'adminIndex'])->name('admin.equipment');
        Route::post('/equipment', [EquipmentController::class, 'store'])->name('admin.equipment.store');
        Route::put('/equipment/{equipment}', [EquipmentController::class, 'update'])->name('admin.equipment.update');
        Route::delete('/equipment/{equipment}', [EquipmentController::class, 'destroy'])->name('admin.equipment.destroy');
        Route::put('/equipment/{equipment}/status', [EquipmentController::class, 'updateStatus'])->name('admin.equipment.status');
        Route::put('/equipment/{equipment}/availability', [EquipmentController::class, 'updateAvailability'])->name('admin.equipment.availability');

        // Document management
        Route::get('/documents', [DocumentController::class, 'adminIndex'])->name('admin.documents');
        Route::post('/documents', [DocumentController::class, 'store'])->name('admin.documents.store');
        Route::put('/documents/{id}', [DocumentController::class, 'update'])->name('admin.documents.update');
        Route::delete('/documents/{id}', [DocumentController::class, 'destroy'])->name('admin.documents.destroy');

        // Equipment requests management
        Route::get('/equipment-requests', [EquipmentRequestController::class, 'adminIndex'])->name('admin.equipment.requests');
        Route::post('/equipment-requests/{id}/approve', [EquipmentRequestController::class, 'approve'])->name('admin.equipment.approve');
        Route::post('/equipment-requests/{id}/reject', [EquipmentRequestController::class, 'reject'])->name('admin.equipment.reject');
        Route::post('/equipment-requests/{id}/mark-paid', [EquipmentRequestController::class, 'markPaid'])->name('admin.equipment.mark-paid');

        // Transport management
        Route::get('/transport', [TransportController::class, 'index'])->name('admin.transport.index');
        Route::get('/transport/create', [TransportController::class, 'create'])->name('admin.transport.create');
        Route::post('/transport', [TransportController::class, 'store'])->name('admin.transport.store');
        Route::get('/transport/show/{id}', [TransportController::class, 'show'])->name('admin.transport.show');
        Route::put('/transport/{id}/status', [TransportController::class, 'updateStatus'])->name('admin.transport.status');

        // Vehicle management
        Route::get('/transport/vehicles', [TransportController::class, 'vehicles'])->name('admin.transport.vehicles');
        Route::post('/transport/vehicles', [TransportController::class, 'storeVehicle'])->name('admin.transport.vehicles.store');
        Route::get('/transport/vehicles/{id}', [TransportController::class, 'showVehicle'])->name('admin.transport.vehicles.show');
        Route::put('/transport/vehicles/{id}', [TransportController::class, 'updateVehicle'])->name('admin.transport.vehicles.update');
        Route::put('/transport/vehicles/{id}/status', [TransportController::class, 'updateVehicleStatus'])->name('admin.transport.vehicles.status');

        // Transport rates management
        Route::get('/transport/rates', [TransportController::class, 'rates'])->name('admin.transport.rates');
        Route::post('/transport/rates', [TransportController::class, 'storeRate'])->name('admin.transport.rates.store');
        Route::get('/transport/rates/{id}', [TransportController::class, 'showRate'])->name('admin.transport.rates.show');
        Route::put('/transport/rates/{id}', [TransportController::class, 'updateRate'])->name('admin.transport.rates.update');
        Route::put('/transport/rates/{id}/toggle', [TransportController::class, 'toggleRate'])->name('admin.transport.rates.toggle');

        // Transport management
        Route::get('/transport', [TransportController::class, 'index'])->name('admin.transport.index');
        Route::get('/transport/create', [TransportController::class, 'create'])->name('admin.transport.create');
        Route::post('/transport', [TransportController::class, 'store'])->name('admin.transport.store');
        Route::get('/transport/{id}', [TransportController::class, 'show'])->name('admin.transport.show');
        Route::put('/transport/{id}/status', [TransportController::class, 'updateStatus'])->name('admin.transport.status');

        // Vehicle management
        Route::get('/transport/vehicles', [TransportController::class, 'vehicles'])->name('admin.transport.vehicles');
        Route::post('/transport/vehicles', [TransportController::class, 'storeVehicle'])->name('admin.transport.vehicles.store');

        // Transport rates management
        Route::get('/transport/rates', [TransportController::class, 'rates'])->name('admin.transport.rates');
        Route::post('/transport/rates', [TransportController::class, 'storeRate'])->name('admin.transport.rates.store');

        // Settings management
        Route::get('/settings', [SettingsController::class, 'index'])->name('admin.settings');
        Route::post('/settings', [SettingsController::class, 'update'])->name('admin.settings.update');
        Route::post('/settings/cooperative', [SettingsController::class, 'updateCooperativeSettings'])->name('admin.settings.cooperative');
        Route::post('/settings/financial', [SettingsController::class, 'updateFinancialSettings'])->name('admin.settings.financial');
        Route::post('/settings/system', [SettingsController::class, 'updateSystemSettings'])->name('admin.settings.system');
        Route::post('/settings/notifications', [SettingsController::class, 'updateNotificationSettings'])->name('admin.settings.notifications');
    });

    // Event & Meeting Management Routes - Admin routes protected
    Route::middleware(['admin'])->prefix('admin')->group(function () {
        // Event management
        Route::get('/events', [App\Http\Controllers\EventController::class, 'adminIndex'])->name('admin.events.index');
        Route::post('/events', [App\Http\Controllers\EventController::class, 'store'])->name('admin.events.store');
        Route::get('/events/{id}', [App\Http\Controllers\EventController::class, 'adminShow'])->name('admin.events.show');
        Route::put('/events/{id}', [App\Http\Controllers\EventController::class, 'update'])->name('admin.events.update');
        Route::delete('/events/{id}', [App\Http\Controllers\EventController::class, 'destroy'])->name('admin.events.destroy');

        // Meeting management
        Route::get('/meetings', [MeetingController::class, 'adminIndex'])->name('admin.meetings.index');
        Route::post('/meetings', [MeetingController::class, 'store'])->name('admin.meetings.store');
        Route::get('/meetings/{id}', [MeetingController::class, 'show'])->name('admin.meetings.show');
        Route::put('/meetings/{id}', [MeetingController::class, 'update'])->name('admin.meetings.update');
        Route::delete('/meetings/{id}', [MeetingController::class, 'destroy'])->name('admin.meetings.destroy');

        // Email notifications management
        Route::get('/emails', [App\Http\Controllers\EmailNotificationController::class, 'index'])->name('admin.emails.index');
        Route::post('/emails/send', [App\Http\Controllers\EmailNotificationController::class, 'sendEmail'])->name('admin.emails.send');
        Route::post('/emails/bulk', [App\Http\Controllers\EmailNotificationController::class, 'sendBulkEmail'])->name('admin.emails.bulk');
        Route::post('/emails/meeting-notification', [App\Http\Controllers\EmailNotificationController::class, 'sendMeetingNotification'])->name('admin.emails.meeting-notification');
        Route::post('/emails/test', [App\Http\Controllers\EmailNotificationController::class, 'testEmail'])->name('admin.emails.test');

        // SMS management (keeping for reference)
        Route::get('/sms', [App\Http\Controllers\SmsController::class, 'index'])->name('admin.sms.index');
        Route::post('/sms/send', [App\Http\Controllers\SmsController::class, 'sendSms'])->name('admin.sms.send');
        Route::post('/sms/bulk', [App\Http\Controllers\SmsController::class, 'sendBulkSms'])->name('admin.sms.bulk');
        Route::post('/sms/meeting-reminder', [App\Http\Controllers\SmsController::class, 'sendMeetingReminder'])->name('admin.sms.meeting-reminder');
        Route::get('/sms/{id}', [App\Http\Controllers\SmsController::class, 'show'])->name('admin.sms.show');
        Route::post('/sms/{id}/resend', [App\Http\Controllers\SmsController::class, 'resend'])->name('admin.sms.resend');
        Route::get('/sms/export', [App\Http\Controllers\SmsController::class, 'export'])->name('admin.sms.export');
    });

    // Member meeting routes
    Route::get('/meetings', [MeetingController::class, 'memberIndex'])->name('meetings.index');
    Route::get('/meetings/{id}', [MeetingController::class, 'memberShow'])->name('meetings.show');
    Route::post('/meetings/{meetingId}/attendance', [MeetingController::class, 'updateAttendance'])->name('meetings.attendance');

    // Notification Management Routes - Admin routes protected
    Route::middleware(['admin'])->prefix('admin')->group(function () {
        Route::get('/notifications', [NotificationController::class, 'adminIndex'])->name('admin.notifications.index');
        Route::post('/notifications', [NotificationController::class, 'store'])->name('admin.notifications.store');
    });

    // Member notification routes
    Route::get('/notifications', [NotificationController::class, 'memberIndex'])->name('notifications.index');
    Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::delete('/notifications/{id}', [NotificationController::class, 'destroy'])->name('notifications.destroy');
    Route::get('/notifications/unread-count', [NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');

    // Debug route (remove in production)
    Route::get('/debug/user-farmer', function() {
        $user = auth()->user();
        $farmer = $user->farmer;

        return response()->json([
            'user_id' => $user->id,
            'user_email' => $user->email,
            'farmer' => $farmer ? [
                'id' => $farmer->id,
                'name' => $farmer->name,
                'status' => $farmer->status,
            ] : null,
            'meetings_count' => \App\Models\Meeting::count(),
            'notifications_count' => \App\Models\Notification::where('user_id', $user->id)->count(),
        ]);
    });

    // Member Directory
    Route::get('/member-directory', [App\Http\Controllers\MemberDirectoryController::class, 'index'])->name('member-directory.index');
    Route::get('/member-directory/{id}', [App\Http\Controllers\MemberDirectoryController::class, 'show'])->name('member-directory.show');
    Route::get('/member-directory/export/csv', [App\Http\Controllers\MemberDirectoryController::class, 'export'])->name('member-directory.export');

    // Document Library (routes already defined above, removing duplicates)

    // Event Calendar
    Route::get('/events', [App\Http\Controllers\EventController::class, 'index'])->name('events.index');
    Route::get('/events/{id}', [App\Http\Controllers\EventController::class, 'show'])->name('events.show');
    Route::post('/events', [App\Http\Controllers\EventController::class, 'store'])->name('events.store');
    Route::post('/events/{id}/attendance', [App\Http\Controllers\EventController::class, 'updateAttendance'])->name('events.attendance');

    // Inventory Management
    Route::get('/inventory', [App\Http\Controllers\InventoryController::class, 'index'])->name('inventory.index');
    Route::post('/inventory', [App\Http\Controllers\InventoryController::class, 'store'])->name('inventory.store');
    Route::put('/inventory/{id}', [App\Http\Controllers\InventoryController::class, 'update'])->name('inventory.update');
    Route::delete('/inventory/{id}', [App\Http\Controllers\InventoryController::class, 'destroy'])->name('inventory.destroy');
    Route::get('/inventory/alerts', [App\Http\Controllers\InventoryController::class, 'lowStockAlerts'])->name('inventory.alerts');

    // Feedback System
    Route::get('/feedback', [App\Http\Controllers\FeedbackController::class, 'index'])->name('feedback.index');
    Route::get('/feedback/{id}', [App\Http\Controllers\FeedbackController::class, 'show'])->name('feedback.show');
    Route::post('/feedback', [App\Http\Controllers\FeedbackController::class, 'store'])->name('feedback.store');
    Route::get('/admin/feedback', [App\Http\Controllers\FeedbackController::class, 'adminIndex'])->name('admin.feedback.index')->middleware('admin');
    Route::put('/admin/feedback/{id}', [App\Http\Controllers\FeedbackController::class, 'update'])->name('admin.feedback.update')->middleware('admin');
    Route::delete('/admin/feedback/{id}', [App\Http\Controllers\FeedbackController::class, 'destroy'])->name('admin.feedback.destroy')->middleware('admin');

    // Admin member application routes
    Route::get('/admin/applications', [MemberApplicationController::class, 'adminIndex'])->name('admin.applications.index')->middleware('admin');
    Route::get('/admin/applications/{id}', [MemberApplicationController::class, 'show'])->name('admin.applications.show')->middleware('admin');
    Route::post('/admin/applications/{id}/approve', [MemberApplicationController::class, 'approve'])->name('admin.applications.approve')->middleware('admin');
    Route::post('/admin/applications/{id}/reject', [MemberApplicationController::class, 'reject'])->name('admin.applications.reject')->middleware('admin');
    Route::delete('/admin/applications/{id}', [MemberApplicationController::class, 'destroy'])->name('admin.applications.destroy')->middleware('admin');

    // Profile routes
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password.update');

    // TEST ROUTE - Transport without admin middleware
    Route::get('/test-transport', [TransportController::class, 'index'])->name('test.transport');

    // TEST ROUTE - Check documents in database
    Route::get('/test-documents', function() {
        $allDocs = \App\Models\Document::all(['id', 'title', 'access_level', 'is_active']);
        $memberDocs = \App\Models\Document::whereIn('access_level', ['public', 'member'])->where('is_active', true)->get(['id', 'title', 'access_level', 'is_active']);

        return response()->json([
            'total_documents' => $allDocs->count(),
            'all_documents' => $allDocs,
            'member_visible_documents' => $memberDocs->count(),
            'member_documents' => $memberDocs,
            'user_role' => auth()->user()->role->name ?? 'not logged in'
        ]);
    })->name('test.documents');
    Route::delete('/profile', [App\Http\Controllers\ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/profile/data', [App\Http\Controllers\ProfileController::class, 'show'])->name('profile.show');

    // Logout
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
});

// Public member application routes
Route::get('/members/register', [MemberController::class, 'registerForm'])->name('members.register');
Route::post('/members/register', [MemberApplicationController::class, 'store'])->name('members.application.store');
// Member registration routes
// Route::middleware(['auth'])->group(function () {
//     // Self-registration
//     Route::get('/members/register', [MemberController::class, 'create'])->name('members.register');
//     Route::post('/members', [MemberController::class, 'store'])->name('members.store');
    
//     // Admin management
//     Route::prefix('admin')->middleware(['role:admin'])->group(function () {
//         Route::get('/members', [MemberController::class, 'index'])->name('admin.members.index');
//         Route::get('/members/create', [MemberController::class, 'create'])->name('admin.members.create');
//         Route::post('/members', [MemberController::class, 'store'])->name('admin.members.store');
//         Route::get('/members/{member}/edit', [MemberController::class, 'edit'])->name('admin.members.edit');
//         Route::put('/members/{member}', [MemberController::class, 'update'])->name('admin.members.update');
//     });
// });