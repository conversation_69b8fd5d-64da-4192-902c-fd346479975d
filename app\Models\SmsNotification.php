<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SmsNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'phone_number',
        'message',
        'type',
        'status',
        'provider',
        'provider_message_id',
        'provider_response',
        'error_message',
        'sent_by',
        'recipient_user_id',
        'sent_at',
        'delivered_at',
        'cost',
        'metadata',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'metadata' => 'array',
        'cost' => 'decimal:4',
    ];

    /**
     * Get the user who sent this SMS.
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sent_by');
    }

    /**
     * Get the recipient user.
     */
    public function recipient()
    {
        return $this->belongsTo(User::class, 'recipient_user_id');
    }

    /**
     * Scope for pending SMS.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for sent SMS.
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope for failed SMS.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for delivered SMS.
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope for SMS by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for SMS sent today.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', Carbon::today());
    }

    /**
     * Scope for SMS sent this month.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year);
    }

    /**
     * Mark SMS as sent.
     */
    public function markAsSent($providerMessageId = null, $providerResponse = null)
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'provider_message_id' => $providerMessageId,
            'provider_response' => $providerResponse,
        ]);
    }

    /**
     * Mark SMS as failed.
     */
    public function markAsFailed($errorMessage = null, $providerResponse = null)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'provider_response' => $providerResponse,
        ]);
    }

    /**
     * Mark SMS as delivered.
     */
    public function markAsDelivered()
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Get formatted phone number for display.
     */
    public function getFormattedPhoneAttribute()
    {
        // Format Malawian phone numbers
        $phone = $this->phone_number;

        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Add country code if not present
        if (strlen($phone) === 9 && (substr($phone, 0, 1) === '8' || substr($phone, 0, 1) === '9')) {
            $phone = '265' . $phone;
        } elseif (strlen($phone) === 10 && substr($phone, 0, 2) === '08') {
            $phone = '265' . substr($phone, 1);
        } elseif (strlen($phone) === 10 && substr($phone, 0, 2) === '09') {
            $phone = '265' . substr($phone, 1);
        }

        return '+' . $phone;
    }

    /**
     * Get delivery status with color.
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'sent' => 'bg-blue-100 text-blue-800',
            'delivered' => 'bg-green-100 text-green-800',
            'failed' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get type with color.
     */
    public function getTypeColorAttribute()
    {
        return match($this->type) {
            'password' => 'bg-purple-100 text-purple-800',
            'meeting' => 'bg-blue-100 text-blue-800',
            'announcement' => 'bg-green-100 text-green-800',
            'reminder' => 'bg-orange-100 text-orange-800',
            'welcome' => 'bg-indigo-100 text-indigo-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get truncated message for display.
     */
    public function getTruncatedMessageAttribute($length = 50)
    {
        return strlen($this->message) > $length 
            ? substr($this->message, 0, $length) . '...' 
            : $this->message;
    }
}
