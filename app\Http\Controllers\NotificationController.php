<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Notification;
use App\Models\Farmer;
use Inertia\Inertia;

class NotificationController extends Controller
{
    /**
     * Display admin notifications management page.
     */
    public function adminIndex()
    {
        // Group notifications by title, message, type, and created_at (same notification content)
        $groupedNotifications = Notification::with('user.farmer')
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy(function ($notification) {
                // Group by notification content and creation time (rounded to minute)
                return $notification->title . '|' . $notification->message . '|' . $notification->type . '|' . $notification->created_at->format('Y-m-d H:i');
            })
            ->map(function ($group) {
                $firstNotification = $group->first();
                $totalRecipients = $group->count();
                $readCount = $group->where('is_read', true)->count();
                $unreadCount = $group->where('is_read', false)->count();

                // Get detailed recipient information
                $recipients = $group->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'user_name' => $notification->user->name ?? 'Unknown User',
                        'farmer_name' => $notification->user->farmer->name ?? null,
                        'is_read' => $notification->is_read,
                        'user_id' => $notification->user_id,
                    ];
                })->sortBy('farmer_name');

                return [
                    'id' => $firstNotification->id, // Use first notification ID as group ID
                    'title' => $firstNotification->title,
                    'message' => $firstNotification->message,
                    'type' => $firstNotification->type,
                    'created_at' => $firstNotification->created_at,
                    'total_recipients' => $totalRecipients,
                    'read_count' => $readCount,
                    'unread_count' => $unreadCount,
                    'read_percentage' => $totalRecipients > 0 ? round(($readCount / $totalRecipients) * 100, 1) : 0,
                    'recipients' => $recipients,
                ];
            })
            ->values()
            ->take(20); // Limit to 20 notification groups

        $stats = [
            'total_notifications' => Notification::count(),
            'unread_notifications' => Notification::where('is_read', false)->count(),
            'meeting_notifications' => Notification::where('type', 'meeting')->count(),
            'general_notifications' => Notification::where('type', 'general')->count(),
            'unique_notifications' => $groupedNotifications->count(),
        ];

        return Inertia::render('Admin/Notifications', [
            'notifications' => $groupedNotifications,
            'stats' => $stats,
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Display member notifications page.
     */
    public function memberIndex()
    {
        $user = auth()->user();
        
        $notifications = Notification::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'type' => $notification->type,
                    'is_read' => $notification->is_read,
                    'created_at' => $notification->created_at,
                    'data' => $notification->data ? json_decode($notification->data, true) : null,
                ];
            });

        return Inertia::render('Member/Notifications', [
            'notifications' => $notifications,
            'auth' => ['user' => $user]
        ]);
    }

    /**
     * Create and send a new notification.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'type' => 'required|in:general,meeting,loan,equipment,announcement',
            'send_to' => 'required|in:all,active,specific',
            'specific_members' => 'required_if:send_to,specific|array',
            'specific_members.*' => 'exists:farmers,id',
        ]);

        $farmers = collect();

        switch ($request->send_to) {
            case 'all':
                $farmers = Farmer::with('user')->get();
                break;
            case 'active':
                $farmers = Farmer::with('user')->where('status', 'active')->get();
                break;
            case 'specific':
                $farmers = Farmer::with('user')->whereIn('id', $request->specific_members)->get();
                break;
        }

        $notificationCount = 0;
        foreach ($farmers as $farmer) {
            if ($farmer->user) {
                Notification::create([
                    'user_id' => $farmer->user->id,
                    'title' => $request->title,
                    'message' => $request->message,
                    'type' => $request->type,
                    'is_read' => false,
                    'data' => json_encode([
                        'sent_by' => auth()->user()->name,
                        'sent_at' => now()->toISOString(),
                    ]),
                ]);
                $notificationCount++;
            }
        }

        return redirect()->back()->with('success', "Notification sent to {$notificationCount} members successfully!");
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead($id)
    {
        $user = auth()->user();
        
        $notification = Notification::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $notification->update(['is_read' => true]);

        return redirect()->back()->with('success', 'Notification marked as read!');
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead()
    {
        $user = auth()->user();
        
        Notification::where('user_id', $user->id)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        return redirect()->back()->with('success', 'All notifications marked as read!');
    }

    /**
     * Delete a notification.
     */
    public function destroy($id)
    {
        $user = auth()->user();
        
        $notification = Notification::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $notification->delete();

        return redirect()->back()->with('success', 'Notification deleted successfully!');
    }

    /**
     * Get unread notifications count for user.
     */
    public function getUnreadCount()
    {
        $user = auth()->user();
        
        $count = Notification::where('user_id', $user->id)
            ->where('is_read', false)
            ->count();

        return response()->json(['count' => $count]);
    }
}
