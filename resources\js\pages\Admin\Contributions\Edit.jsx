import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function EditContribution({ auth, contribution, members }) {
    const { data, setData, put, processing, errors } = useForm({
        member_id: contribution.member_id || '',
        amount: contribution.amount || '',
        type: contribution.type || 'monthly',
        description: contribution.description || '',
        payment_method: contribution.payment_method || 'cash',
        reference_number: contribution.reference_number || '',
        contribution_date: contribution.contribution_date ? contribution.contribution_date.split('T')[0] : '',
        status: contribution.status || 'pending',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(`/admin/contributions/${contribution.id}`);
    };

    const contributionTypes = [
        { value: 'monthly', label: 'Monthly Contribution' },
        { value: 'special', label: 'Special Contribution' },
        { value: 'emergency', label: 'Emergency Fund' },
        { value: 'development', label: 'Development Fund' },
    ];

    const paymentMethods = [
        { value: 'cash', label: 'Cash' },
        { value: 'bank_transfer', label: 'Bank Transfer' },
        { value: 'mobile_money', label: 'Mobile Money' },
    ];

    const statusOptions = [
        { value: 'pending', label: 'Pending' },
        { value: 'confirmed', label: 'Confirmed' },
        { value: 'rejected', label: 'Rejected' },
    ];

    return (
        <AdminLayout auth={auth}>
            <Head title="Edit Contribution" />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                        <div className="p-6 sm:p-8">
                            {/* Header */}
                            <div className="flex items-center justify-between mb-8">
                                <div>
                                    <Link
                                        href={`/admin/contributions/${contribution.id}`}
                                        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
                                    >
                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                        </svg>
                                        Back to Contribution Details
                                    </Link>
                                    <h1 className="text-3xl font-bold text-gray-900">Edit Contribution</h1>
                                    <p className="mt-2 text-gray-600">Update contribution information and details</p>
                                </div>
                            </div>

                            {/* Form */}
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Member Selection */}
                                    <div className="md:col-span-2">
                                        <label htmlFor="member_id" className="block text-sm font-medium text-gray-700 mb-2">
                                            Select Member <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            id="member_id"
                                            value={data.member_id}
                                            onChange={(e) => setData('member_id', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        >
                                            <option value="">Choose a member...</option>
                                            {members.map((member) => (
                                                <option key={member.id} value={member.id}>
                                                    {member.name} ({member.email})
                                                    {member.farm_size && ` - ${member.farm_size} acres`}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.member_id && <p className="mt-1 text-sm text-red-600">{errors.member_id}</p>}
                                    </div>

                                    {/* Amount */}
                                    <div>
                                        <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                                            Amount (ZMW) <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="number"
                                            id="amount"
                                            value={data.amount}
                                            onChange={(e) => setData('amount', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            min="0"
                                            step="0.01"
                                            required
                                            placeholder="0.00"
                                        />
                                        {errors.amount && <p className="mt-1 text-sm text-red-600">{errors.amount}</p>}
                                    </div>

                                    {/* Type */}
                                    <div>
                                        <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                                            Contribution Type <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            id="type"
                                            value={data.type}
                                            onChange={(e) => setData('type', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        >
                                            {contributionTypes.map((type) => (
                                                <option key={type.value} value={type.value}>
                                                    {type.label}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.type && <p className="mt-1 text-sm text-red-600">{errors.type}</p>}
                                    </div>

                                    {/* Payment Method */}
                                    <div>
                                        <label htmlFor="payment_method" className="block text-sm font-medium text-gray-700 mb-2">
                                            Payment Method <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            id="payment_method"
                                            value={data.payment_method}
                                            onChange={(e) => setData('payment_method', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        >
                                            {paymentMethods.map((method) => (
                                                <option key={method.value} value={method.value}>
                                                    {method.label}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.payment_method && <p className="mt-1 text-sm text-red-600">{errors.payment_method}</p>}
                                    </div>

                                    {/* Status */}
                                    <div>
                                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                                            Status <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            id="status"
                                            value={data.status}
                                            onChange={(e) => setData('status', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        >
                                            {statusOptions.map((status) => (
                                                <option key={status.value} value={status.value}>
                                                    {status.label}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.status && <p className="mt-1 text-sm text-red-600">{errors.status}</p>}
                                    </div>

                                    {/* Reference Number */}
                                    <div>
                                        <label htmlFor="reference_number" className="block text-sm font-medium text-gray-700 mb-2">
                                            Reference Number
                                        </label>
                                        <input
                                            type="text"
                                            id="reference_number"
                                            value={data.reference_number}
                                            onChange={(e) => setData('reference_number', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            placeholder="Transaction reference (optional)"
                                        />
                                        {errors.reference_number && <p className="mt-1 text-sm text-red-600">{errors.reference_number}</p>}
                                    </div>

                                    {/* Contribution Date */}
                                    <div>
                                        <label htmlFor="contribution_date" className="block text-sm font-medium text-gray-700 mb-2">
                                            Contribution Date <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="date"
                                            id="contribution_date"
                                            value={data.contribution_date}
                                            onChange={(e) => setData('contribution_date', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        />
                                        {errors.contribution_date && <p className="mt-1 text-sm text-red-600">{errors.contribution_date}</p>}
                                    </div>

                                    {/* Description */}
                                    <div className="md:col-span-2">
                                        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                                            Description
                                        </label>
                                        <textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            placeholder="Additional notes or description (optional)"
                                        />
                                        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                                    </div>
                                </div>

                                {/* Submit Button */}
                                <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                    <Link
                                        href={`/admin/contributions/${contribution.id}`}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </Link>
                                    <button
                                        type="submit"
                                        disabled={processing}
                                        className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {processing ? 'Updating...' : 'Update Contribution'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
