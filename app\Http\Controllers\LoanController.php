<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Loan;
use App\Models\LoanRepayment;
use App\Models\ActivityLog;
use Carbon\Carbon;

class LoanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new loan application.
     */
    public function create()
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile associated with this user.']);
        }

        // Get existing loans for this farmer
        $existingLoans = Loan::where('farmer_id', $farmer->id)->get();

        // Get loan settings (you can create a settings model or use default values)
        $loanSettings = [
            'default_interest_rate' => 5.0,
            'minimum_loan_amount' => 1000,
            'maximum_loan_amount' => 100000,
        ];

        return Inertia::render('Loans/Apply', [
            'auth' => ['user' => $user],
            'existingLoans' => $existingLoans,
            'loanSettings' => $loanSettings
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'purpose' => 'required|string|max:255',
            'term_months' => 'required|integer|min:1',
        ]);

        $user = $request->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile found.']);
        }

        $loan = new Loan();
        $loan->farmer_id = $farmer->id;
        $loan->amount = $request->amount;
        $loan->interest_rate = 5.0; // Default 5% annual interest rate
        $loan->term_months = $request->term_months;
        $loan->purpose = $request->purpose;
        $loan->status = 'pending';
        $loan->application_date = now();
        $loan->save();

        // Log activity
        ActivityLog::create([
            'user_id' => $user->id,
            'action' => 'create',
            'subject_type' => Loan::class,
            'subject_id' => $loan->id,
            'description' => "Loan application submitted for {$request->amount} MW",
        ]);

        return redirect()->back()->with('success', 'Loan application submitted successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $loan = Loan::with(['farmer.user', 'approvedBy', 'repayments.receivedBy'])
            ->findOrFail($id);

        return Inertia::render('Admin/Loans/Show', [
            'loan' => $loan,
        ]);
    }

    /**
     * Admin view for managing loans
     */
    public function adminIndex()
    {
        $loans = Loan::with(['farmer.user', 'approvedBy', 'repayments'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($loan) {
                $totalRepaid = $loan->repayments->sum('amount');
                $remainingBalance = max(0, $loan->amount - $totalRepaid);

                // Calculate monthly payment and interest details
                $principal = $loan->amount;
                $monthlyRate = ($loan->interest_rate ?? 5) / 100 / 12;
                $termMonths = $loan->term_months ?? 12;

                if ($monthlyRate > 0) {
                    $monthlyPayment = $principal * ($monthlyRate * pow(1 + $monthlyRate, $termMonths)) /
                                    (pow(1 + $monthlyRate, $termMonths) - 1);
                } else {
                    $monthlyPayment = $principal / $termMonths;
                }

                $totalInterest = ($monthlyPayment * $termMonths) - $principal;
                $totalRepayable = $principal + $totalInterest;

                return [
                    'id' => $loan->id,
                    'farmer_name' => $loan->farmer->user->name,
                    'farmer_id' => $loan->farmer->id,
                    'amount' => $loan->amount,
                    'interest_rate' => $loan->interest_rate ?? 5,
                    'term_months' => $loan->term_months ?? 12,
                    'purpose' => $loan->purpose,
                    'status' => $loan->status,
                    'application_date' => $loan->application_date,
                    'approval_date' => $loan->approval_date,
                    'disbursement_date' => $loan->disbursement_date,
                    'approved_by' => $loan->approvedBy?->name,
                    'rejection_reason' => $loan->rejection_reason,
                    'total_repaid' => $totalRepaid,
                    'remaining_balance' => $remainingBalance,
                    'repayment_count' => $loan->repayments->count(),
                    'monthly_payment' => $monthlyPayment,
                    'total_interest' => $totalInterest,
                    'total_repayable' => $totalRepayable,
                    'created_at' => $loan->created_at,
                ];
            });

        return Inertia::render('Admin/Loans', [
            'loans' => $loans,
        ]);
    }

    /**
     * Approve a loan application
     */
    public function approve(Request $request, $id)
    {
        $request->validate([
            'interest_rate' => 'required|numeric|min:0|max:100',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $loan = Loan::findOrFail($id);

        $loan->update([
            'status' => 'approved',
            'approval_date' => now(),
            'approved_by' => auth()->id(),
            'interest_rate' => $request->interest_rate,
        ]);

        // Log activity
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'approve',
            'subject_type' => Loan::class,
            'subject_id' => $loan->id,
            'description' => "Loan approved for {$loan->farmer->user->name} - {$loan->amount} MW",
        ]);

        return redirect()->back()->with('success', 'Loan approved successfully!');
    }

    /**
     * Reject a loan application
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $loan = Loan::findOrFail($id);

        $loan->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'rejection_reason' => $request->rejection_reason,
        ]);

        // Log activity
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'reject',
            'subject_type' => Loan::class,
            'subject_id' => $loan->id,
            'description' => "Loan rejected for {$loan->farmer->user->name} - {$loan->amount} MW",
        ]);

        return redirect()->back()->with('success', 'Loan rejected.');
    }

    /**
     * Disburse an approved loan
     */
    public function disburse($id)
    {
        $loan = Loan::findOrFail($id);

        if ($loan->status !== 'approved') {
            return redirect()->back()->withErrors(['error' => 'Only approved loans can be disbursed.']);
        }

        $loan->update([
            'status' => 'disbursed',
            'disbursement_date' => now(),
        ]);

        // Log activity
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'disburse',
            'subject_type' => Loan::class,
            'subject_id' => $loan->id,
            'description' => "Loan disbursed to {$loan->farmer->user->name} - {$loan->amount} MW",
        ]);

        return redirect()->back()->with('success', 'Loan disbursed successfully!');
    }

    /**
     * Record a loan repayment
     */
    public function recordRepayment(Request $request, $id)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string|max:100',
            'receipt_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        $loan = Loan::findOrFail($id);

        if (!in_array($loan->status, ['disbursed', 'partial_repayment'])) {
            return redirect()->back()->withErrors(['error' => 'Can only record repayments for disbursed loans.']);
        }

        $totalRepaid = $loan->repayments->sum('amount');
        $remainingBalance = $loan->amount - $totalRepaid;

        if ($request->amount > $remainingBalance) {
            return redirect()->back()->withErrors(['error' => 'Repayment amount cannot exceed remaining balance.']);
        }

        $repayment = LoanRepayment::create([
            'loan_id' => $loan->id,
            'amount' => $request->amount,
            'payment_date' => now(),
            'payment_method' => $request->payment_method,
            'receipt_number' => $request->receipt_number,
            'received_by' => auth()->id(),
            'notes' => $request->notes,
        ]);

        // Update loan status based on repayment
        $newTotalRepaid = $totalRepaid + $request->amount;
        if ($newTotalRepaid >= $loan->amount) {
            $loan->update(['status' => 'completed']);
        } else {
            $loan->update(['status' => 'partial_repayment']);
        }

        // Log activity
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'repayment',
            'subject_type' => Loan::class,
            'subject_id' => $loan->id,
            'description' => "Repayment recorded for {$loan->farmer->user->name} - {$request->amount} MW",
        ]); 

        return redirect()->back()->with('success', 'Repayment recorded successfully!');
    }
}
