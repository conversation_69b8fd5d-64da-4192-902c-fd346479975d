<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transport_deliveries', function (Blueprint $table) {
            $table->id();
            $table->string('delivery_number')->unique(); // e.g., "DEL-2025-001"
            $table->foreignId('farmer_id')->constrained('farmers')->onDelete('cascade');
            $table->foreignId('vehicle_id')->constrained('vehicles')->onDelete('cascade');
            $table->foreignId('transport_rate_id')->constrained('transport_rates')->onDelete('cascade');

            // Delivery Details
            $table->date('delivery_date');
            $table->time('departure_time')->nullable();
            $table->time('arrival_time')->nullable();
            $table->decimal('sugarcane_weight_tons', 8, 2); // Weight from weighbridge
            $table->decimal('distance_km', 8, 2); // Actual distance traveled

            // Financial Calculations
            $table->decimal('transport_rate_per_ton', 8, 2); // Rate at time of delivery
            $table->decimal('fuel_surcharge', 8, 2)->default(0);
            $table->decimal('total_transport_cost', 10, 2); // Calculated total cost
            $table->decimal('gross_sales_amount', 10, 2)->nullable(); // Member's gross earnings
            $table->decimal('net_amount_after_transport', 10, 2)->nullable(); // After transport deduction

            // Status and Tracking
            $table->string('status')->default('pending'); // pending, completed, cancelled
            $table->string('destination')->default('Illovo Sugar Mill');
            $table->string('pickup_location'); // Farm location or collection point
            $table->text('delivery_notes')->nullable();
            $table->boolean('payment_deducted')->default(false);
            $table->timestamp('payment_deducted_at')->nullable();

            // Additional tracking
            $table->string('weighbridge_ticket_number')->nullable();
            $table->decimal('fuel_used_liters', 8, 2)->nullable();
            $table->decimal('fuel_cost', 8, 2)->nullable();

            $table->timestamps();

            $table->index(['farmer_id', 'delivery_date']);
            $table->index(['vehicle_id', 'delivery_date']);
            $table->index(['status', 'delivery_date']);
            $table->index('delivery_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transport_deliveries');
    }
};
