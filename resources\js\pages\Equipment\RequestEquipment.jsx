import React, { useState } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

const RequestEquipment = ({ auth, availableEquipment, memberRequests }) => {
  const [selectedEquipment, setSelectedEquipment] = useState(null);
  const [showRequestForm, setShowRequestForm] = useState(false);

  const { data, setData, post, processing, errors, reset } = useForm({
    equipment_id: '',
    start_date: '',
    end_date: '',
    purpose: '',
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'MW',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-ZM', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateRentalCost = () => {
    if (!selectedEquipment || !data.start_date || !data.end_date) return 0;
    
    const startDate = new Date(data.start_date);
    const endDate = new Date(data.end_date);
    const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    
    return days * selectedEquipment.rental_cost_per_day;
  };

  const handleEquipmentSelect = (equipment) => {
    setSelectedEquipment(equipment);
    setData('equipment_id', equipment.id);
    setShowRequestForm(true);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    post('/equipment/request', {
      onSuccess: () => {
        reset();
        setSelectedEquipment(null);
        setShowRequestForm(false);
      }
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <MemberLayout user={auth.user} header="Request Equipment">
      <Head title="Request Equipment - Siyamphanje Cooperative" />
      
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Request Farm Equipment</h1>
                <p className="mt-2 text-gray-600">Borrow equipment from the cooperative for your farming needs</p>
              </div>
              <Link
                href="/dashboard/member"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Available Equipment */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Available Equipment</h2>
                
                {availableEquipment && availableEquipment.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {availableEquipment.map((equipment) => (
                      <div
                        key={equipment.id}
                        className="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer"
                        onClick={() => handleEquipmentSelect(equipment)}
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">{equipment.name}</h3>
                            <p className="text-sm text-gray-600">{equipment.type}</p>
                          </div>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Available
                          </span>
                        </div>
                        
                        <p className="text-gray-700 text-sm mb-4">{equipment.description}</p>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Model:</span>
                            <span className="text-sm font-medium">{equipment.model || 'N/A'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Year:</span>
                            <span className="text-sm font-medium">{equipment.purchase_year || 'N/A'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Rental Cost:</span>
                            <span className="text-lg font-bold text-green-600">
                              {formatCurrency(equipment.rental_cost_per_day)}/day
                            </span>
                          </div>
                        </div>
                        
                        <button className="w-full mt-4 bg-gradient-to-r from-green-500 to-green-600 text-white py-2 px-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-colors">
                          Request This Equipment
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Equipment Available</h3>
                    <p className="text-gray-600">There is currently no equipment available for rent.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Request Form / My Requests */}
            <div className="lg:col-span-1">
              {showRequestForm && selectedEquipment ? (
                <div className="bg-white rounded-2xl shadow-xl p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Request: {selectedEquipment.name}</h3>
                  
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-2">
                        Start Date
                      </label>
                      <input
                        type="date"
                        id="start_date"
                        value={data.start_date}
                        onChange={(e) => setData('start_date', e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                        required
                      />
                      {errors.start_date && <p className="mt-1 text-sm text-red-600">{errors.start_date}</p>}
                    </div>

                    <div>
                      <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-2">
                        End Date
                      </label>
                      <input
                        type="date"
                        id="end_date"
                        value={data.end_date}
                        onChange={(e) => setData('end_date', e.target.value)}
                        min={data.start_date || new Date().toISOString().split('T')[0]}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                        required
                      />
                      {errors.end_date && <p className="mt-1 text-sm text-red-600">{errors.end_date}</p>}
                    </div>

                    <div>
                      <label htmlFor="purpose" className="block text-sm font-medium text-gray-700 mb-2">
                        Purpose
                      </label>
                      <textarea
                        id="purpose"
                        value={data.purpose}
                        onChange={(e) => setData('purpose', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                        placeholder="Describe how you plan to use this equipment..."
                        required
                      />
                      {errors.purpose && <p className="mt-1 text-sm text-red-600">{errors.purpose}</p>}
                    </div>

                    {data.start_date && data.end_date && (
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Rental Summary</h4>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span>Daily Rate:</span>
                            <span>{formatCurrency(selectedEquipment.rental_cost_per_day)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Duration:</span>
                            <span>{Math.ceil((new Date(data.end_date) - new Date(data.start_date)) / (1000 * 60 * 60 * 24)) + 1} days</span>
                          </div>
                          <div className="flex justify-between font-bold text-lg border-t pt-2">
                            <span>Total Cost:</span>
                            <span className="text-green-600">{formatCurrency(calculateRentalCost())}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex space-x-3">
                      <button
                        type="submit"
                        disabled={processing}
                        className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white py-2 px-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-colors disabled:opacity-50"
                      >
                        {processing ? 'Submitting...' : 'Submit Request'}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setShowRequestForm(false);
                          setSelectedEquipment(null);
                          reset();
                        }}
                        className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              ) : (
                <div className="bg-white rounded-2xl shadow-xl p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">My Equipment Requests</h3>
                  
                  {memberRequests && memberRequests.length > 0 ? (
                    <div className="space-y-4">
                      {memberRequests.slice(0, 5).map((request) => (
                        <div key={request.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium text-gray-900">{request.equipment.name}</h4>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                              {request.status}
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-2">{request.purpose}</p>
                          
                          <div className="text-xs text-gray-500 space-y-1">
                            <div>Period: {formatDate(request.start_date)} - {formatDate(request.end_date)}</div>
                            <div>Cost: {formatCurrency(request.total_rental_cost)}</div>
                            {request.status === 'approved' && (
                              <div className="flex items-center space-x-2">
                                <span>Payment:</span>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(request.payment_status)}`}>
                                  {request.payment_status}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <p className="text-gray-600 text-sm">No equipment requests yet</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MemberLayout>
  );
};

export default RequestEquipment;
