<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Your App Title</title>
        
        <!-- Fonts (optional, keep if needed) -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />
        
        <!-- Vite Styles (for CSS) -->
        @vite(['resources/js/main.jsx', 'resources/css/app.css']) <!-- Load assets -->
    
    </head>
    <body class="antialiased">
        <!-- React Root -->
        <div id="app" data-page="{{ json_encode($page) }}"></div>

        
        <!-- Vite React Script -->
        @viteReact('resources/js/main.jsx')
    </body>
</html>
</html>
