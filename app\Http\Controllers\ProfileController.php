<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use App\Models\User;
use App\Models\Farmer;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request)
    {
        $user = $request->user();
        $user->load(['role', 'farmer']);

        return Inertia::render('Profile/Edit', [
            'auth' => [
                'user' => $user
            ]
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request)
    {
        $user = $request->user();
        $user->load('farmer');

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            // Farmer specific fields
            'farm_size' => ['nullable', 'string', 'max:100'],
            'land_size' => ['nullable', 'numeric', 'min:0'],
            'location_coordinates' => ['nullable', 'string', 'max:100'],
            'identification_number' => ['nullable', 'string', 'max:50'],
            'bank_account_number' => ['nullable', 'string', 'max:50'],
            'bank_name' => ['nullable', 'string', 'max:100'],
            'gender' => ['nullable', 'in:male,female,other'],
        ]);

        // Update user information
        $user->update([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'] ?? null,
            'address' => $validated['address'] ?? null,
        ]);

        // Update or create farmer profile if user has farmer relationship
        if ($user->farmer) {
            $user->farmer->update([
                'farm_size' => $validated['farm_size'] ?? null,
                'land_size' => $validated['land_size'] ?? null,
                'location_coordinates' => $validated['location_coordinates'] ?? null,
                'identification_number' => $validated['identification_number'] ?? null,
                'bank_account_number' => $validated['bank_account_number'] ?? null,
                'bank_name' => $validated['bank_name'] ?? null,
                'gender' => $validated['gender'] ?? null,
            ]);
        } else {
            // Create farmer profile if it doesn't exist
            Farmer::create([
                'user_id' => $user->id,
                'farm_size' => $validated['farm_size'] ?? null,
                'land_size' => $validated['land_size'] ?? null,
                'location_coordinates' => $validated['location_coordinates'] ?? null,
                'identification_number' => $validated['identification_number'] ?? null,
                'bank_account_number' => $validated['bank_account_number'] ?? null,
                'bank_name' => $validated['bank_name'] ?? null,
                'gender' => $validated['gender'] ?? null,
                'membership_date' => now(),
                'status' => 'active',
            ]);
        }

        return redirect()->back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        $request->user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->back()->with('success', 'Password updated successfully!');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request)
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        // Delete farmer profile if exists
        if ($user->farmer) {
            $user->farmer->delete();
        }

        // Logout and delete user
        auth()->logout();
        $user->delete();

        return redirect('/')->with('success', 'Account deleted successfully.');
    }

    /**
     * Get user profile data for API
     */
    public function show(Request $request)
    {
        $user = $request->user();
        $user->load(['role', 'farmer']);

        return response()->json([
            'user' => $user,
            'farmer' => $user->farmer,
        ]);
    }
}
