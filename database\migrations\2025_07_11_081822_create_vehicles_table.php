<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->string('registration_number')->unique(); // e.g., "MZ-123-ABC"
            $table->string('vehicle_type'); // truck, trailer, pickup
            $table->string('make'); // Toyota, Isuzu, etc.
            $table->string('model');
            $table->integer('year');
            $table->decimal('capacity_tons', 8, 2); // Maximum load capacity
            $table->decimal('fuel_consumption_per_km', 8, 2)->nullable(); // Liters per km
            $table->string('status')->default('active'); // active, maintenance, retired
            $table->string('driver_name')->nullable();
            $table->string('driver_phone')->nullable();
            $table->date('insurance_expiry')->nullable();
            $table->date('license_expiry')->nullable();
            $table->decimal('maintenance_cost_ytd', 10, 2)->default(0); // Year to date maintenance
            $table->integer('total_trips')->default(0);
            $table->decimal('total_distance_km', 10, 2)->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['status', 'vehicle_type']);
            $table->index('registration_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
