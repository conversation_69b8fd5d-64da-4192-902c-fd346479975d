import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function EditMember({ auth, member }) {
    const { data, setData, put, processing, errors } = useForm({
        name: member.name || '',
        email: member.email || '',
        phone: member.phone || '',
        address: member.address || '',
        land_size: member.land_size || '',
        farm_location: member.farm_location || '',
        crops_grown: member.crops_grown || '',
        farming_experience: member.farming_experience || '',
        status: member.status || 'active',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(`/admin/members/${member.id}`);
    };

    const statusOptions = [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'suspended', label: 'Suspended' },
        { value: 'pending', label: 'Pending' },
    ];

    return (
        <AdminLayout auth={auth}>
            <Head title={`Edit Member: ${member.name}`} />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                        <div className="p-6 sm:p-8">
                            {/* Header */}
                            <div className="flex items-center justify-between mb-8">
                                <div>
                                    <Link
                                        href={`/admin/members/${member.id}`}
                                        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
                                    >
                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                        </svg>
                                        Back to Member Details
                                    </Link>
                                    <h1 className="text-3xl font-bold text-gray-900">Edit Member</h1>
                                    <p className="mt-2 text-gray-600">Update member information and settings</p>
                                </div>
                            </div>

                            {/* Form */}
                            <form onSubmit={handleSubmit} className="space-y-8">
                                {/* Personal Information */}
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                                                Full Name <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="text"
                                                id="name"
                                                value={data.name}
                                                onChange={(e) => setData('name', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                required
                                            />
                                            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                                        </div>

                                        <div>
                                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                                Email Address <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="email"
                                                id="email"
                                                value={data.email}
                                                onChange={(e) => setData('email', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                required
                                            />
                                            {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                                        </div>

                                        <div>
                                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                                                Phone Number
                                            </label>
                                            <input
                                                type="tel"
                                                id="phone"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="+265..."
                                            />
                                            {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
                                        </div>

                                        <div>
                                            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                                                Status <span className="text-red-500">*</span>
                                            </label>
                                            <select
                                                id="status"
                                                value={data.status}
                                                onChange={(e) => setData('status', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                required
                                            >
                                                {statusOptions.map((option) => (
                                                    <option key={option.value} value={option.value}>
                                                        {option.label}
                                                    </option>
                                                ))}
                                            </select>
                                            {errors.status && <p className="mt-1 text-sm text-red-600">{errors.status}</p>}
                                        </div>

                                        <div className="md:col-span-2">
                                            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                                                Address
                                            </label>
                                            <textarea
                                                id="address"
                                                value={data.address}
                                                onChange={(e) => setData('address', e.target.value)}
                                                rows={3}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Enter full address..."
                                            />
                                            {errors.address && <p className="mt-1 text-sm text-red-600">{errors.address}</p>}
                                        </div>
                                    </div>
                                </div>

                                {/* Farm Information */}
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">Farm Information</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label htmlFor="land_size" className="block text-sm font-medium text-gray-700 mb-2">
                                                Farm Size (acres)
                                            </label>
                                            <input
                                                type="number"
                                                id="land_size"
                                                value={data.land_size}
                                                onChange={(e) => setData('land_size', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                min="0"
                                                step="0.1"
                                                placeholder="0.0"
                                            />
                                            {errors.land_size && <p className="mt-1 text-sm text-red-600">{errors.land_size}</p>}
                                        </div>

                                        <div>
                                            <label htmlFor="farming_experience" className="block text-sm font-medium text-gray-700 mb-2">
                                                Farming Experience (years)
                                            </label>
                                            <input
                                                type="number"
                                                id="farming_experience"
                                                value={data.farming_experience}
                                                onChange={(e) => setData('farming_experience', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                min="0"
                                                placeholder="0"
                                            />
                                            {errors.farming_experience && <p className="mt-1 text-sm text-red-600">{errors.farming_experience}</p>}
                                        </div>

                                        <div>
                                            <label htmlFor="farm_location" className="block text-sm font-medium text-gray-700 mb-2">
                                                Farm Location
                                            </label>
                                            <input
                                                type="text"
                                                id="farm_location"
                                                value={data.farm_location}
                                                onChange={(e) => setData('farm_location', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="e.g., Chongwe District"
                                            />
                                            {errors.farm_location && <p className="mt-1 text-sm text-red-600">{errors.farm_location}</p>}
                                        </div>

                                        <div>
                                            <label htmlFor="crops_grown" className="block text-sm font-medium text-gray-700 mb-2">
                                                Crops Grown
                                            </label>
                                            <input
                                                type="text"
                                                id="crops_grown"
                                                value={data.crops_grown}
                                                onChange={(e) => setData('crops_grown', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="e.g., Maize, Soybeans, Vegetables"
                                            />
                                            {errors.crops_grown && <p className="mt-1 text-sm text-red-600">{errors.crops_grown}</p>}
                                        </div>
                                    </div>
                                </div>

                                {/* Submit Buttons */}
                                <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                    <Link
                                        href={`/admin/members/${member.id}`}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </Link>
                                    <button
                                        type="submit"
                                        disabled={processing}
                                        className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {processing ? 'Updating...' : 'Update Member'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
