<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\User;
use App\Models\Farmer;
use App\Models\SavingsAccount;
use App\Models\Contribution;
use App\Models\Loan;
use App\Models\Notification;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    /**
     * Main dashboard route - redirects based on user role
     */
    public function index()
    {
        // Clear any potential cache issues by getting fresh user data

        $user = auth()->user();
        $user = User::with('role')->find($user->id);

        // Ensure we have a valid role
        if (!$user || !$user->role) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Invalid user role. Please contact administrator.');
        }

        // Log the dashboard access for debugging
        Log::info('Dashboard access', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_role' => $user->role->name,
            'ip_address' => request()->ip(),
        ]);

        switch ($user->role->name) {
            case 'admin':
                return redirect()->route('dashboard.admin');
            case 'staff':
                return redirect()->route('dashboard.staff');
            case 'member':
            default:
                return redirect()->route('dashboard.member');
        }
    }

    /**
     * Display the admin dashboard
     */
    public function adminDashboard()
    {
        $user = auth()->user();
        $user = User::with('role')->find($user->id);

        // Double-check admin role (additional security layer)
        if ($user->role->name !== 'admin') {
            Log::warning('Non-admin user attempted to access admin dashboard', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_role' => $user->role->name,
                'ip_address' => request()->ip(),
            ]);

            auth()->logout();
            return redirect()->route('login')->with('error', 'Unauthorized access. You have been logged out for security reasons.');
        }

        // Get dashboard statistics
        $stats = [
            'total_members' => User::count(),
            'total_savings' => SavingsAccount::sum('balance'),
            'total_contributions' => Contribution::sum('amount'),
            'total_loans' => Loan::sum('amount'),
            'equipment_requests' => $this->getAdminEquipmentStats(),
            'loans' => $this->getAdminLoanStats(),
        ];


        // Get comprehensive recent activities with real data

        // Recent loan applications
        $recentLoans = Loan::with('farmer')
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function ($loan) {
                return [
                    'id' => 'loan_' . $loan->id,
                    'type' => 'loan_application',
                    'description' => "Loan application for " . number_format($loan->amount) . " ZMW",
                    'amount' => $loan->amount,
                    'time' => $loan->created_at->diffForHumans(),
                    'status' => $loan->status,
                    'icon' => 'loan',
                    'created_at' => $loan->created_at,
                    'user' => $loan->farmer->name,
                    'details' => "Purpose: {$loan->purpose}",
                    'action_url' => "/admin/loans",
                    'priority' => $loan->status === 'pending' ? 'high' : 'normal'
                ];
            });

        // Recent contributions
        $recentContributions = Contribution::with(['member', 'farmer'])
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function ($contribution) {
                // Get the user name from member relationship, fallback to farmer if available
                $userName = $contribution->member ? $contribution->member->name :
                           ($contribution->farmer ? $contribution->farmer->name : 'Unknown Member');

                return [
                    'id' => 'contribution_' . $contribution->id,
                    'type' => 'contribution',
                    'description' => "Monthly contribution of " . number_format($contribution->amount) . " ZMW",
                    'amount' => $contribution->amount,
                    'time' => $contribution->created_at->diffForHumans(),
                    'status' => 'completed',
                    'icon' => 'contribution',
                    'created_at' => $contribution->created_at,
                    'user' => $userName,
                    'details' => "Date: {$contribution->contribution_date}",
                    'action_url' => "/admin/contributions",
                    'priority' => 'normal'
                ];
            });

        // Recent member registrations
        $recentMembers = Farmer::orderBy('created_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function ($farmer) {
                return [
                    'id' => 'member_' . $farmer->id,
                    'type' => 'member_registration',
                    'description' => "New member registration",
                    'amount' => null,
                    'time' => $farmer->created_at->diffForHumans(),
                    'status' => $farmer->status,
                    'icon' => 'member',
                    'created_at' => $farmer->created_at,
                    'user' => $farmer->name,
                    'details' => "Land size: {$farmer->land_size} hectares",
                    'action_url' => "/admin/members",
                    'priority' => $farmer->status === 'pending' ? 'high' : 'normal'
                ];
            });

        // Recent equipment requests
        $recentEquipmentRequests = \App\Models\EquipmentRequest::with(['equipment', 'user'])
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function ($request) {
                return [
                    'id' => 'equipment_request_' . $request->id,
                    'type' => 'equipment_request',
                    'description' => "Equipment request for " . ($request->equipment->name ?? 'equipment'),
                    'amount' => $request->estimated_cost,
                    'time' => $request->created_at->diffForHumans(),
                    'status' => $request->status,
                    'icon' => 'equipment',
                    'created_at' => $request->created_at,
                    'user' => $request->user->name,
                    'details' => "Purpose: {$request->purpose}",
                    'action_url' => "/admin/equipment-requests",
                    'priority' => $request->status === 'pending' ? 'high' : 'normal'
                ];
            });

        // Combine and sort all activities
        $recentActivity = $recentLoans
            ->concat($recentContributions)
            ->concat($recentMembers)
            ->concat($recentEquipmentRequests)
            ->sortByDesc('created_at')
            ->take(8)
            ->values();

        return Inertia::render('Dashboard/AdminDashboard', [
            'stats' => $stats,
            'recentActivity' => $recentActivity,
            'auth' => [
                'user' => User::with('role')->find(auth()->id())
            ]
        ]);
    }

    
    public function staffDashboard()
    {
        return Inertia::render('Dashboard/StaffDashboard', [
            'auth' => [
                'user' => User::with('role')->find(auth()->id())
            ]
        ]);
    }

    public function memberDashboard()
    {
        // Get fresh user data to avoid cache issues
        $user = auth()->user();
        $user = User::with(['role', 'farmer'])->find($user->id);

        // Ensure we have a valid user and role
        if (!$user || !$user->role) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Invalid user session. Please log in again.');
        }

        // This dashboard is for members only
        if ($user->role->name !== 'member') {
            Log::warning('Non-member user attempted to access member dashboard', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_role' => $user->role->name,
                'ip_address' => request()->ip(),
                'session_id' => session()->getId(),
                'timestamp' => now(),
            ]);

            // Clear session and redirect to appropriate dashboard
            session()->invalidate();
            session()->regenerateToken();

            // Redirect to appropriate dashboard
            switch ($user->role->name) {
                case 'admin':
                    return redirect()->route('dashboard.admin')->with('info', 'Redirected to admin dashboard.');
                case 'staff':
                    return redirect()->route('dashboard.staff')->with('info', 'Redirected to staff dashboard.');
                default:
                    auth()->logout();
                    return redirect()->route('login')->with('error', 'Invalid role. Please contact administrator.');
            }
        }

        // Log successful member dashboard access
        Log::info('Member dashboard accessed', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'session_id' => session()->getId(),
            'timestamp' => now(),
        ]);

        $farmer = $user->farmer;

        // Initialize default values
        $memberStats = [
            'totalSavings' => 0,
            'totalContributions' => 0,
            'totalLoans' => 0,
            'activeLoanBalance' => 0,
            'nextPaymentDue' => null,
            'memberSince' => null,
            'contributionsUpToDate' => true,
        ];

        $recentActivities = [];

        if ($farmer) {
            // Get member statistics with proper loan calculations
            $totalLoansAmount = $farmer->loans()->whereIn('status', ['approved', 'disbursed', 'partial_repayment', 'completed'])->sum('amount') ?? 0;

            // Calculate total repayments for this member's loans
            $totalRepayments = \App\Models\LoanRepayment::whereHas('loan', function ($query) use ($farmer) {
                $query->where('farmer_id', $farmer->id);
            })->sum('amount') ?? 0;

            // Calculate active loan balance (total loans minus repayments)
            $activeLoanBalance = max(0, $totalLoansAmount - $totalRepayments);

            // Get only active loans (not completed or rejected)
            $activeLoansAmount = $farmer->loans()->whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->sum('amount') ?? 0;

            $memberStats = [
                'totalSavings' => $farmer->savingsAccounts()->sum('balance') ?? 0,
                'totalContributions' => Contribution::where('farmer_id', $farmer->id)->sum('amount') ?? 0,
                'totalLoans' => $totalLoansAmount,
                'activeLoanBalance' => $activeLoanBalance,
                'activeLoansAmount' => $activeLoansAmount,
                'totalRepayments' => $totalRepayments,
                'nextPaymentDue' => $this->getNextPaymentDue($farmer),
                'memberSince' => $farmer->membership_date ? $farmer->membership_date->format('Y') : null,
                'contributionsUpToDate' => $this->checkContributionsStatus($farmer),
                'equipmentRequests' => $this->getEquipmentRequestStats($user),
            ];

            // Get recent activities for this member
            $recentActivities = $this->getMemberRecentActivities($farmer);
        }

        // Get notifications for the user
        $notifications = \App\Models\Notification::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'type' => $notification->type,
                    'is_read' => $notification->is_read,
                    'created_at' => $notification->created_at,
                ];
            });

        $unreadNotificationsCount = \App\Models\Notification::where('user_id', $user->id)
            ->where('is_read', false)
            ->count();

        return Inertia::render('Dashboard/MemberDashboard', [
            'auth' => [
                'user' => $user
            ],
            'memberStats' => $memberStats,
            'recentActivities' => $recentActivities,
            'notifications' => $notifications,
            'unreadNotificationsCount' => $unreadNotificationsCount,
        ]);
    }

    private function getNextPaymentDue($farmer)
    {
        $activeLoans = $farmer->loans()->where('status', 'approved')->get();

        if ($activeLoans->isEmpty()) {
            return null;
        }

        // Simple calculation - in a real system you'd have a proper repayment schedule
        $oldestLoan = $activeLoans->sortBy('disbursement_date')->first();
        if ($oldestLoan && $oldestLoan->disbursement_date) {
            $nextDue = \Carbon\Carbon::parse($oldestLoan->disbursement_date)->addMonth();
            $daysUntilDue = $nextDue->diffInDays(now(), false);

            return [
                'date' => $nextDue->format('Y-m-d'),
                'days' => $daysUntilDue > 0 ? -$daysUntilDue : abs($daysUntilDue),
                'overdue' => $daysUntilDue > 0
            ];
        }

        return null;
    }

    private function checkContributionsStatus($farmer)
    {
        // Check if member has made a contribution in the last 2 months
        $recentContribution = $farmer->contributions()
            ->where('contribution_date', '>=', now()->subMonths(2))
            ->exists();

        return $recentContribution;
    }

    private function getMemberRecentActivities($farmer)
    {
        $activities = collect();

        // Get recent savings deposits
        $recentSavings = $farmer->savingsAccounts()
            ->where('updated_at', '>=', now()->subDays(30))
            ->orderBy('updated_at', 'desc')
            ->take(3)
            ->get();

        foreach ($recentSavings as $savings) {
            $activities->push([
                'type' => 'savings_deposit',
                'title' => 'Savings Deposit',
                'description' => 'Deposited to savings account ' . $savings->account_number,
                'amount' => $savings->balance,
                'date' => $savings->updated_at,
                'status' => 'completed',
                'icon_type' => 'savings'
            ]);
        }

        // Get recent contributions
        $recentContributions = $farmer->contributions()
            ->orderBy('contribution_date', 'desc')
            ->take(3)
            ->get();

        foreach ($recentContributions as $contribution) {
            $activities->push([
                'type' => 'contribution',
                'title' => 'Monthly Contribution',
                'description' => 'Contribution payment via ' . $contribution->payment_method,
                'amount' => $contribution->amount,
                'date' => $contribution->contribution_date,
                'status' => 'completed',
                'icon_type' => 'contribution'
            ]);
        }

        // Get recent loans
        $recentLoans = $farmer->loans()
            ->orderBy('created_at', 'desc')
            ->take(2)
            ->get();

        foreach ($recentLoans as $loan) {
            $activities->push([
                'type' => 'loan',
                'title' => $loan->status === 'approved' ? 'Loan Approved' : 'Loan Application',
                'description' => 'Loan for ' . $loan->purpose,
                'amount' => $loan->amount,
                'date' => $loan->created_at,
                'status' => $loan->status,
                'icon_type' => 'loan'
            ]);
        }

        // Get recent equipment requests
        $recentEquipmentRequests = \App\Models\EquipmentRequest::where('user_id', $farmer->user_id)
            ->with('equipment')
            ->orderBy('created_at', 'desc')
            ->take(2)
            ->get();

        foreach ($recentEquipmentRequests as $request) {
            $activities->push([
                'type' => 'equipment_request',
                'title' => 'Equipment Request',
                'description' => 'Requested ' . ($request->equipment->name ?? 'equipment') . ' for ' . $request->purpose,
                'amount' => $request->estimated_cost,
                'date' => $request->created_at,
                'status' => $request->status,
                'icon_type' => 'equipment'
            ]);
        }

        // Sort by date and return top 5
        return $activities->sortByDesc('date')->take(5)->values();
    }

    private function getEquipmentRequestStats($user)
    {
        $totalRequests = \App\Models\EquipmentRequest::where('user_id', $user->id)->count();
        $pendingRequests = \App\Models\EquipmentRequest::where('user_id', $user->id)->where('status', 'pending')->count();
        $approvedRequests = \App\Models\EquipmentRequest::where('user_id', $user->id)->where('status', 'approved')->count();
        $pendingPayments = \App\Models\EquipmentRequest::where('user_id', $user->id)
            ->where('status', 'approved')
            ->where('payment_status', 'pending')
            ->count();

        $recentRequest = \App\Models\EquipmentRequest::where('user_id', $user->id)
            ->with('equipment')
            ->orderBy('created_at', 'desc')
            ->first();

        return [
            'total' => $totalRequests,
            'pending' => $pendingRequests,
            'approved' => $approvedRequests,
            'pendingPayments' => $pendingPayments,
            'recentRequest' => $recentRequest,
        ];
    }

    private function getAdminEquipmentStats()
    {
        $totalRequests = \App\Models\EquipmentRequest::count();
        $pendingRequests = \App\Models\EquipmentRequest::where('status', 'pending')->count();
        $approvedRequests = \App\Models\EquipmentRequest::where('status', 'approved')->count();
        $rejectedRequests = \App\Models\EquipmentRequest::where('status', 'rejected')->count();
        $pendingPayments = \App\Models\EquipmentRequest::where('status', 'approved')
            ->where('payment_status', 'pending')
            ->count();
        $totalEquipment = \App\Models\Equipment::count();
        $availableEquipment = \App\Models\Equipment::where('status', 'available')
            ->count();

        $recentRequests = \App\Models\EquipmentRequest::with(['equipment', 'user'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return [
            'total' => $totalRequests,
            'pending' => $pendingRequests,
            'approved' => $approvedRequests,
            'rejected' => $rejectedRequests,
            'pendingPayments' => $pendingPayments,
            'totalEquipment' => $totalEquipment,
            'availableEquipment' => $availableEquipment,
            'recentRequests' => $recentRequests,
        ];
    }

    private function getAdminLoanStats()
    {
        $totalLoans = \App\Models\Loan::count();
        $pendingLoans = \App\Models\Loan::where('status', 'pending')->count();
        $approvedLoans = \App\Models\Loan::where('status', 'approved')->count();
        $disbursedLoans = \App\Models\Loan::where('status', 'disbursed')->count();
        $completedLoans = \App\Models\Loan::where('status', 'completed')->count();
        $rejectedLoans = \App\Models\Loan::where('status', 'rejected')->count();

        $totalLoanAmount = \App\Models\Loan::whereIn('status', ['approved', 'disbursed', 'partial_repayment', 'completed'])->sum('amount');
        $totalRepayments = \App\Models\LoanRepayment::sum('amount');
        $outstandingAmount = $totalLoanAmount - $totalRepayments;

        $recentLoans = \App\Models\Loan::with(['farmer.user'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return [
            'total' => $totalLoans,
            'pending' => $pendingLoans,
            'approved' => $approvedLoans,
            'disbursed' => $disbursedLoans,
            'completed' => $completedLoans,
            'rejected' => $rejectedLoans,
            'totalAmount' => $totalLoanAmount,
            'totalRepayments' => $totalRepayments,
            'outstandingAmount' => $outstandingAmount,
            'recentLoans' => $recentLoans,
        ];
    }
}