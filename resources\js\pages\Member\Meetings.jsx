import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

export default function MemberMeetings({ auth, meetings, warning }) {
    const [selectedMeeting, setSelectedMeeting] = useState(null);
    const [showDetailsModal, setShowDetailsModal] = useState(false);

    const { post, processing } = useForm();

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-ZM', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (timeString) => {
        // Handle both datetime strings and time-only strings
        if (timeString.includes(' ')) {
            // It's a full datetime string, extract just the time part
            return new Date(timeString).toLocaleTimeString('en-ZM', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            // It's just a time string
            return new Date(`2000-01-01 ${timeString}`).toLocaleTimeString('en-ZM', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    };

    const getMeetingTypeColor = (type) => {
        switch (type) {
            case 'general': return 'bg-blue-100 text-blue-800';
            case 'board': return 'bg-purple-100 text-purple-800';
            case 'emergency': return 'bg-red-100 text-red-800';
            case 'training': return 'bg-green-100 text-green-800';
            case 'annual': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getResponseStatusColor = (status) => {
        switch (status) {
            case 'attending': return 'bg-green-100 text-green-800';
            case 'not_attending': return 'bg-red-100 text-red-800';
            case 'maybe': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const isUpcoming = (meetingDate) => {
        return new Date(meetingDate) >= new Date();
    };

    const handleAttendanceResponse = (meetingId, response) => {
        post(`/meetings/${meetingId}/attendance`, {
            response_status: response,
        });
    };

    const showMeetingDetails = (meeting) => {
        setSelectedMeeting(meeting);
        setShowDetailsModal(true);
    };

    const upcomingMeetings = meetings?.filter(meeting => isUpcoming(meeting.meeting_date)) || [];
    const pastMeetings = meetings?.filter(meeting => !isUpcoming(meeting.meeting_date)) || [];

    return (
        <MemberLayout user={auth.user} header="Meetings">
            <Head title="Meetings - Siyamphanje Cooperative" />
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-green-50 p-6">
                <div className="max-w-6xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Meetings</h1>
                                <p className="mt-2 text-gray-600">View upcoming meetings and manage your attendance</p>
                            </div>
                            <Link
                                href="/dashboard/member"
                                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                ← Back to Dashboard
                            </Link>
                        </div>
                    </div>

                    {/* Warning Message */}
                    {warning && (
                        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-yellow-700">{warning}</p>
                                </div>
                            </div>
                        </div>
                    )}



                    {/* Upcoming Meetings */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-6">Upcoming Meetings</h2>
                        
                        {upcomingMeetings.length > 0 ? (
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                {upcomingMeetings.map((meeting) => (
                                    <div key={meeting.id} className="bg-white rounded-xl shadow-lg overflow-hidden border-l-4 border-blue-500">
                                        <div className="p-6">
                                            <div className="flex justify-between items-start mb-4">
                                                <div className="flex-1">
                                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                                        {meeting.title}
                                                    </h3>
                                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMeetingTypeColor(meeting.meeting_type)}`}>
                                                        {meeting.meeting_type}
                                                    </span>
                                                </div>
                                                {meeting.user_attendance?.response_status && (
                                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getResponseStatusColor(meeting.user_attendance.response_status)}`}>
                                                        {meeting.user_attendance.response_status}
                                                    </span>
                                                )}
                                            </div>

                                            <div className="space-y-3 mb-6">
                                                <div className="flex items-center text-gray-600">
                                                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                                    </svg>
                                                    <span className="font-medium">{formatDate(meeting.meeting_date)}</span>
                                                </div>
                                                
                                                <div className="flex items-center text-gray-600">
                                                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>{formatTime(meeting.meeting_time)}</span>
                                                </div>
                                                
                                                <div className="flex items-center text-gray-600">
                                                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>{meeting.location}</span>
                                                </div>
                                            </div>

                                            <p className="text-gray-600 mb-6 line-clamp-3">
                                                {meeting.description}
                                            </p>

                                            <div className="flex space-x-3">
                                                <button
                                                    onClick={() => showMeetingDetails(meeting)}
                                                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                                                >
                                                    View Details
                                                </button>
                                                
                                                {!meeting.user_attendance?.response_status && (
                                                    <div className="flex space-x-2">
                                                        <button
                                                            onClick={() => handleAttendanceResponse(meeting.id, 'attending')}
                                                            disabled={processing}
                                                            className="bg-green-600 text-white py-2 px-3 rounded-md hover:bg-green-700 transition-colors text-sm font-medium disabled:opacity-50"
                                                        >
                                                            ✓
                                                        </button>
                                                        <button
                                                            onClick={() => handleAttendanceResponse(meeting.id, 'not_attending')}
                                                            disabled={processing}
                                                            className="bg-red-600 text-white py-2 px-3 rounded-md hover:bg-red-700 transition-colors text-sm font-medium disabled:opacity-50"
                                                        >
                                                            ✗
                                                        </button>
                                                        <button
                                                            onClick={() => handleAttendanceResponse(meeting.id, 'maybe')}
                                                            disabled={processing}
                                                            className="bg-yellow-600 text-white py-2 px-3 rounded-md hover:bg-yellow-700 transition-colors text-sm font-medium disabled:opacity-50"
                                                        >
                                                            ?
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="bg-white rounded-xl shadow-lg p-12 text-center">
                                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No Upcoming Meetings</h3>
                                <p className="text-gray-600">There are no meetings scheduled at the moment. Check back later for updates.</p>
                            </div>
                        )}
                    </div>

                    {/* Past Meetings */}
                    {pastMeetings.length > 0 && (
                        <div>
                            <h2 className="text-2xl font-bold text-gray-900 mb-6">Past Meetings</h2>
                            
                            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Meeting
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Date & Time
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Type
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Your Response
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {pastMeetings.map((meeting) => (
                                                <tr key={meeting.id} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div>
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {meeting.title}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                {meeting.location}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">{formatDate(meeting.meeting_date)}</div>
                                                        <div className="text-sm text-gray-500">{formatTime(meeting.meeting_time)}</div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMeetingTypeColor(meeting.meeting_type)}`}>
                                                            {meeting.meeting_type}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {meeting.user_attendance?.response_status ? (
                                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getResponseStatusColor(meeting.user_attendance.response_status)}`}>
                                                                {meeting.user_attendance.response_status}
                                                            </span>
                                                        ) : (
                                                            <span className="text-sm text-gray-500">No response</span>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <button
                                                            onClick={() => showMeetingDetails(meeting)}
                                                            className="text-blue-600 hover:text-blue-900"
                                                        >
                                                            View Details
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Meeting Details Modal */}
            {showDetailsModal && selectedMeeting && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <div className="flex justify-between items-start mb-4">
                                <h3 className="text-xl font-bold text-gray-900">
                                    {selectedMeeting.title}
                                </h3>
                                <button
                                    onClick={() => setShowDetailsModal(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </button>
                            </div>

                            <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h4 className="text-sm font-medium text-gray-700 mb-1">Meeting Type</h4>
                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMeetingTypeColor(selectedMeeting.meeting_type)}`}>
                                            {selectedMeeting.meeting_type}
                                        </span>
                                    </div>

                                    <div>
                                        <h4 className="text-sm font-medium text-gray-700 mb-1">Your Response</h4>
                                        {selectedMeeting.user_attendance?.response_status ? (
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getResponseStatusColor(selectedMeeting.user_attendance.response_status)}`}>
                                                {selectedMeeting.user_attendance.response_status}
                                            </span>
                                        ) : (
                                            <span className="text-sm text-gray-500">No response yet</span>
                                        )}
                                    </div>

                                    <div>
                                        <h4 className="text-sm font-medium text-gray-700 mb-1">Date</h4>
                                        <p className="text-sm text-gray-900">{formatDate(selectedMeeting.meeting_date)}</p>
                                    </div>

                                    <div>
                                        <h4 className="text-sm font-medium text-gray-700 mb-1">Time</h4>
                                        <p className="text-sm text-gray-900">{formatTime(selectedMeeting.meeting_time)}</p>
                                    </div>

                                    <div className="md:col-span-2">
                                        <h4 className="text-sm font-medium text-gray-700 mb-1">Location</h4>
                                        <p className="text-sm text-gray-900">{selectedMeeting.location}</p>
                                    </div>
                                </div>

                                <div>
                                    <h4 className="text-sm font-medium text-gray-700 mb-2">Description</h4>
                                    <p className="text-sm text-gray-900">{selectedMeeting.description}</p>
                                </div>

                                {selectedMeeting.agenda && (
                                    <div>
                                        <h4 className="text-sm font-medium text-gray-700 mb-2">Agenda</h4>
                                        <div className="bg-gray-50 rounded-lg p-4">
                                            <pre className="text-sm text-gray-900 whitespace-pre-wrap">{selectedMeeting.agenda}</pre>
                                        </div>
                                    </div>
                                )}

                                {isUpcoming(selectedMeeting.meeting_date) && (
                                    <div className="border-t border-gray-200 pt-4">
                                        <h4 className="text-sm font-medium text-gray-700 mb-3">Update Your Attendance</h4>
                                        <div className="flex space-x-3">
                                            <button
                                                onClick={() => {
                                                    handleAttendanceResponse(selectedMeeting.id, 'attending');
                                                    setShowDetailsModal(false);
                                                }}
                                                disabled={processing}
                                                className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors text-sm font-medium disabled:opacity-50"
                                            >
                                                I'll Attend
                                            </button>
                                            <button
                                                onClick={() => {
                                                    handleAttendanceResponse(selectedMeeting.id, 'maybe');
                                                    setShowDetailsModal(false);
                                                }}
                                                disabled={processing}
                                                className="flex-1 bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition-colors text-sm font-medium disabled:opacity-50"
                                            >
                                                Maybe
                                            </button>
                                            <button
                                                onClick={() => {
                                                    handleAttendanceResponse(selectedMeeting.id, 'not_attending');
                                                    setShowDetailsModal(false);
                                                }}
                                                disabled={processing}
                                                className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors text-sm font-medium disabled:opacity-50"
                                            >
                                                Can't Attend
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </MemberLayout>
    );
}
