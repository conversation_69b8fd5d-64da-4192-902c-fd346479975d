import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

export default function EventShow({ auth, event }) {
    const [showRSVPModal, setShowRSVPModal] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        response_status: event.user_attendance?.response_status || 'attending',
        comments: event.user_attendance?.comments || '',
    });

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-ZM', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
    };

    const formatTime = (timeString) => {
        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-ZM', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getTypeColor = (type) => {
        const colors = {
            meeting: 'bg-blue-100 text-blue-800',
            training: 'bg-green-100 text-green-800',
            field_day: 'bg-yellow-100 text-yellow-800',
            social: 'bg-purple-100 text-purple-800',
            market_day: 'bg-orange-100 text-orange-800',
            other: 'bg-gray-100 text-gray-800'
        };
        return colors[type] || colors.other;
    };

    const getTypeIcon = (type) => {
        const icons = {
            meeting: (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
            ),
            training: (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                </svg>
            ),
            field_day: (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
            ),
            social: (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
            ),
            market_day: (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM6 12a1 1 0 112 0v4H6v-4zm6 0a1 1 0 112 0v4h-2v-4z" clipRule="evenodd" />
                </svg>
            ),
            other: (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
            )
        };
        return icons[type] || icons.other;
    };

    const handleRSVP = (e) => {
        e.preventDefault();
        post(`/events/${event.id}/attendance`, {
            onSuccess: () => {
                setShowRSVPModal(false);
            }
        });
    };

    const getAttendanceStatusColor = (status) => {
        const colors = {
            attending: 'bg-green-100 text-green-800',
            not_attending: 'bg-red-100 text-red-800',
            maybe: 'bg-yellow-100 text-yellow-800',
            pending: 'bg-gray-100 text-gray-800'
        };
        return colors[status] || colors.pending;
    };

    const isUpcoming = (eventDate) => {
        return new Date(eventDate) > new Date();
    };

    return (
        <MemberLayout auth={auth}>
            <Head title={`${event.title} - Event Details`} />
            
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-4xl mx-auto">
                    {/* Breadcrumb */}
                    <nav className="flex mb-6" aria-label="Breadcrumb">
                        <ol className="inline-flex items-center space-x-1 md:space-x-3">
                            <li className="inline-flex items-center">
                                <Link href="/dashboard" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                    <svg className="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                    </svg>
                                    Dashboard
                                </Link>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <Link href="/events" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                                        Events Calendar
                                    </Link>
                                </div>
                            </li>
                            <li aria-current="page">
                                <div className="flex items-center">
                                    <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">Event Details</span>
                                </div>
                            </li>
                        </ol>
                    </nav>

                    {/* Event Header */}
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
                        <div className="bg-gradient-to-r from-green-600 to-green-700 px-8 py-6 text-white">
                            <div className="flex items-center space-x-4">
                                <div className="p-3 bg-white bg-opacity-20 rounded-lg">
                                    {getTypeIcon(event.type)}
                                </div>
                                <div>
                                    <h1 className="text-3xl font-bold">{event.title}</h1>
                                    <div className="flex items-center space-x-4 mt-2">
                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white`}>
                                            {event.type.replace('_', ' ').toUpperCase()}
                                        </span>
                                        {event.user_attendance && (
                                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getAttendanceStatusColor(event.user_attendance.response_status)}`}>
                                                {event.user_attendance.response_status.replace('_', ' ').toUpperCase()}
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="p-8">
                            {/* Event Details */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                                <div className="space-y-4">
                                    <div className="flex items-center space-x-3">
                                        <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                        </svg>
                                        <div>
                                            <p className="text-sm text-gray-500">Date</p>
                                            <p className="font-semibold text-gray-900">{formatDate(event.event_date)}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-3">
                                        <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                        </svg>
                                        <div>
                                            <p className="text-sm text-gray-500">Time</p>
                                            <p className="font-semibold text-gray-900">
                                                {formatTime(event.start_time)}
                                                {event.end_time && ` - ${formatTime(event.end_time)}`}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-3">
                                        <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                        </svg>
                                        <div>
                                            <p className="text-sm text-gray-500">Location</p>
                                            <p className="font-semibold text-gray-900">{event.location}</p>
                                        </div>
                                    </div>

                                    {event.max_attendees && (
                                        <div className="flex items-center space-x-3">
                                            <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                                            </svg>
                                            <div>
                                                <p className="text-sm text-gray-500">Capacity</p>
                                                <p className="font-semibold text-gray-900">{event.attendees.length} / {event.max_attendees} attendees</p>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <p className="text-sm text-gray-500 mb-2">Organized by</p>
                                        <p className="font-semibold text-gray-900">{event.created_by || 'Unknown'}</p>
                                    </div>

                                    {isUpcoming(event.event_date) && (
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <div className="flex items-center justify-between">
                                                <div>
                                                    <p className="text-sm font-medium text-blue-900">RSVP Status</p>
                                                    <p className="text-sm text-blue-700">
                                                        {event.user_attendance 
                                                            ? `You are ${event.user_attendance.response_status.replace('_', ' ')}`
                                                            : 'Please respond to this event'
                                                        }
                                                    </p>
                                                </div>
                                                <button
                                                    onClick={() => setShowRSVPModal(true)}
                                                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                                                >
                                                    {event.user_attendance ? 'Update RSVP' : 'RSVP Now'}
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Description */}
                            <div className="mb-8">
                                <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                                <div className="prose prose-sm max-w-none text-gray-700">
                                    <p>{event.description}</p>
                                </div>
                            </div>

                            {/* Agenda */}
                            {event.agenda && (
                                <div className="mb-8">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Agenda</h3>
                                    <div className="prose prose-sm max-w-none text-gray-700">
                                        <p className="whitespace-pre-line">{event.agenda}</p>
                                    </div>
                                </div>
                            )}

                            {/* Attendees */}
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    Attendees ({event.attendees.filter(a => a.response_status === 'attending').length})
                                </h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {event.attendees
                                        .filter(attendee => attendee.response_status === 'attending')
                                        .map((attendee, index) => (
                                            <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                    <span className="text-green-600 font-medium text-sm">
                                                        {attendee.farmer_name ? attendee.farmer_name.charAt(0).toUpperCase() : 'M'}
                                                    </span>
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">{attendee.farmer_name || 'Member'}</p>
                                                    <p className="text-sm text-gray-500">Attending</p>
                                                </div>
                                            </div>
                                        ))
                                    }
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="mt-8 flex space-x-4">
                                <Link
                                    href="/events"
                                    className="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors"
                                >
                                    Back to Events
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* RSVP Modal */}
            {showRSVPModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg max-w-md w-full p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">RSVP for {event.title}</h3>
                        
                        <form onSubmit={handleRSVP}>
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Your Response
                                </label>
                                <select
                                    value={data.response_status}
                                    onChange={(e) => setData('response_status', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                >
                                    <option value="attending">I will attend</option>
                                    <option value="not_attending">I cannot attend</option>
                                    <option value="maybe">Maybe</option>
                                </select>
                                {errors.response_status && (
                                    <p className="mt-1 text-sm text-red-600">{errors.response_status}</p>
                                )}
                            </div>

                            <div className="mb-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Comments (Optional)
                                </label>
                                <textarea
                                    value={data.comments}
                                    onChange={(e) => setData('comments', e.target.value)}
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    placeholder="Any additional comments..."
                                />
                                {errors.comments && (
                                    <p className="mt-1 text-sm text-red-600">{errors.comments}</p>
                                )}
                            </div>

                            <div className="flex space-x-3">
                                <button
                                    type="submit"
                                    disabled={processing}
                                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                                >
                                    {processing ? 'Saving...' : 'Save Response'}
                                </button>
                                <button
                                    type="button"
                                    onClick={() => setShowRSVPModal(false)}
                                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </MemberLayout>
    );
}
