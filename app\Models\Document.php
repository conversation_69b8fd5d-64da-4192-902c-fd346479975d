<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Document extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'category',
        'access_level',
        'uploaded_by',
        'download_count',
        'is_featured',
        'is_active',
        'published_at',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'download_count' => 'integer',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * Get the user who uploaded the document.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get file extension.
     */
    public function getFileExtensionAttribute()
    {
        return pathinfo($this->file_name, PATHINFO_EXTENSION);
    }

    /**
     * Increment download count.
     */
    public function incrementDownloadCount()
    {
        $this->increment('download_count');
    }

    /**
     * Get file icon based on file type
     */
    public function getFileIconAttribute()
    {
        $icons = [
            'pdf' => '📄',
            'doc' => '📝',
            'docx' => '📝',
            'xls' => '📊',
            'xlsx' => '📊',
            'ppt' => '📊',
            'pptx' => '📊',
            'txt' => '📄',
            'jpg' => '🖼️',
            'jpeg' => '🖼️',
            'png' => '🖼️',
            'gif' => '🖼️',
        ];

        return $icons[strtolower($this->file_type)] ?? '📎';
    }

    /**
     * Get category display name
     */
    public function getCategoryDisplayAttribute()
    {
        $categories = [
            'bylaws' => 'Bylaws & Constitution',
            'forms' => 'Forms & Applications',
            'policies' => 'Policies & Procedures',
            'reports' => 'Reports & Statements',
            'guides' => 'Guides & Manuals',
            'announcements' => 'Announcements',
            'meeting_minutes' => 'Meeting Minutes',
            'financial' => 'Financial Documents',
            'legal' => 'Legal Documents',
            'training' => 'Training Materials',
        ];

        return $categories[$this->category] ?? ucfirst($this->category);
    }

    /**
     * Scope for active documents
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured documents
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for member accessible documents
     */
    public function scopeMemberAccessible($query)
    {
        return $query->whereIn('access_level', ['public', 'member']);
    }

    /**
     * Check if user can access this document.
     */
    public function canBeAccessedBy(User $user)
    {
        switch ($this->access_level) {
            case 'public':
                return true;
            case 'members_only':
            case 'member':
                return $user->farmer !== null;
            case 'admin_only':
            case 'admin':
                return $user->role === 'admin';
            default:
                return false;
        }
    }
}
