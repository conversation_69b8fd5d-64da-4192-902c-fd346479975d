<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default SMS Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default SMS provider that will be used to send
    | SMS notifications. You may set this to any of the providers defined
    | in the "providers" array below.
    |
    */

    'default_provider' => env('SMS_DEFAULT_PROVIDER', 'africastalking'),

    /*
    |--------------------------------------------------------------------------
    | SMS Providers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the SMS providers for your application. You may
    | use multiple providers and switch between them as needed.
    |
    */

    'providers' => [
        'mailersend' => [
            'api_key' => env('MAILERSEND_API_KEY'),
            'sms_from' => env('MAILERSEND_SMS_FROM', 'SIYAMPHANJE'),
            'endpoint' => 'https://api.mailersend.com/v1',
        ],

        'africastalking' => [
            'api_key' => env('AFRICASTALKING_API_KEY'),
            'username' => env('AFRICASTALKING_USERNAME'),
            'sender_id' => env('AFRICASTALKING_SENDER_ID', 'SIYAMPHANJE'),
            'endpoint' => env('AFRICASTALKING_ENDPOINT', 'https://api.africastalking.com/version1/messaging'),
        ],

        'twilio' => [
            'account_sid' => env('TWILIO_ACCOUNT_SID'),
            'auth_token' => env('TWILIO_AUTH_TOKEN'),
            'from_number' => env('TWILIO_FROM_NUMBER'),
            'endpoint' => 'https://api.twilio.com/2010-04-01/Accounts',
        ],

        'simulation' => [
            'enabled' => env('SMS_SIMULATION_MODE', true),
            'success_rate' => env('SMS_SIMULATION_SUCCESS_RATE', 80), // Percentage
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Templates
    |--------------------------------------------------------------------------
    |
    | Define SMS message templates for different notification types.
    | Use {variable} syntax for dynamic content.
    |
    */

    'templates' => [
        'password' => [
            'subject' => 'Login Credentials - Siyamphanje Cooperative',
            'message' => 'Welcome to Siyamphanje Cooperative, {name}! Your login details:
Email: {email}
Password: {password}
Login at: {login_url}
Please change your password after first login.',
        ],

        'welcome' => [
            'subject' => 'Welcome to Siyamphanje Cooperative',
            'message' => 'Welcome to Siyamphanje Cooperative, {name}! We\'re excited to have you as a member. You\'ll receive your login details shortly. For support, contact us at {support_phone}.',
        ],

        'meeting_reminder' => [
            'subject' => 'Meeting Reminder',
            'message' => 'Meeting Reminder: {title}
Date: {date}
Time: {time}
Location: {location}
Please confirm your attendance. - Siyamphanje Cooperative',
        ],

        'meeting_notification' => [
            'subject' => 'New Meeting Scheduled',
            'message' => 'New Meeting: {title}
Date: {date}
Time: {time}
Location: {location}
Agenda: {agenda}
Please RSVP. - Siyamphanje Cooperative',
        ],

        'announcement' => [
            'subject' => 'Announcement',
            'message' => 'ANNOUNCEMENT: {title}
{content}
- Siyamphanje Cooperative',
        ],

        'loan_approval' => [
            'subject' => 'Loan Approved',
            'message' => 'Good news! Your loan application for MWK {amount} has been approved. Please visit our office to complete the documentation. - Siyamphanje Cooperative',
        ],

        'loan_reminder' => [
            'subject' => 'Loan Payment Reminder',
            'message' => 'Reminder: Your loan payment of MWK {amount} is due on {due_date}. Please make your payment to avoid late fees. - Siyamphanje Cooperative',
        ],

        'contribution_reminder' => [
            'subject' => 'Contribution Reminder',
            'message' => 'Reminder: Your monthly contribution of MWK {amount} is due. Please make your payment at your earliest convenience. - Siyamphanje Cooperative',
        ],

        'event_reminder' => [
            'subject' => 'Event Reminder',
            'message' => 'Event Reminder: {title}
Date: {date}
Time: {time}
Location: {location}
Don\'t miss out! - Siyamphanje Cooperative',
        ],

        'equipment_approved' => [
            'subject' => 'Equipment Request Approved',
            'message' => 'Your equipment request for {equipment} has been approved. Rental fee: MWK {fee}. Please visit our office to collect. - Siyamphanje Cooperative',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Settings
    |--------------------------------------------------------------------------
    |
    | General SMS configuration settings.
    |
    */

    'settings' => [
        'max_message_length' => 160, // Standard SMS length
        'long_message_parts' => 153, // Length per part for long messages
        'rate_limit' => [
            'enabled' => true,
            'max_per_minute' => 60,
            'max_per_hour' => 1000,
        ],
        'retry' => [
            'enabled' => true,
            'max_attempts' => 3,
            'delay_seconds' => 300, // 5 minutes
        ],
        'delivery_reports' => [
            'enabled' => true,
            'webhook_url' => env('SMS_DELIVERY_WEBHOOK_URL'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cost Settings
    |--------------------------------------------------------------------------
    |
    | SMS cost configuration for different providers and message types.
    |
    */

    'costs' => [
        'mailersend' => [
            'local' => 45, // MWK per SMS (MailerSend rates for Malawi)
            'international' => 400,
        ],
        'africastalking' => [
            'local' => 50, // MWK per SMS
            'international' => 500,
        ],
        'twilio' => [
            'local' => 120, // MWK per SMS (Twilio rates for Malawi)
            'international' => 800,
        ],
        'currency' => 'MWK',
    ],

    /*
    |--------------------------------------------------------------------------
    | Phone Number Validation
    |--------------------------------------------------------------------------
    |
    | Settings for phone number validation and formatting.
    |
    */

    'phone_validation' => [
        'country_code' => '265', // Malawi
        'local_formats' => [
            '/^08[0-9]{8}$/', // 08XXXXXXXX (Airtel)
            '/^09[0-9]{8}$/', // 09XXXXXXXX (TNM)
            '/^88[0-9]{7}$/', // 88XXXXXXX (Access)
            '/^99[0-9]{7}$/', // 99XXXXXXX (Access)
        ],
        'international_format' => '/^\+265[0-9]{9}$/',
    ],
];
