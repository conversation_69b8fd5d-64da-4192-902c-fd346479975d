<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Farmer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'farm_size',
        'location_coordinates',
        'membership_date',
        'identification_number',
        'bank_account_number',
        'bank_name',
        'gender',
        'address',
        'land_size',
        'status'
    ];

    protected $casts = [
        'membership_date' => 'date',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function crops()
    {
        return $this->belongsToMany(Crop::class, 'farmer_crops')
            ->withPivot('area_planted', 'planting_date', 'expected_harvest_date')
            ->withTimestamps();
    }

    public function farmerCrops()
    {
        return $this->hasMany(FarmerCrop::class);
    }

    public function equipment()
    {
        return $this->hasMany(FarmEquipment::class, 'owner_id');
    }

    public function contributions()
    {
        return $this->hasMany(Contribution::class);
    }

    public function loans()
    {
        return $this->hasMany(Loan::class);
    }

    public function savingsAccounts()
    {
        return $this->hasMany(SavingsAccount::class);
    }

    public function sugarcaneRecords()
    {
        return $this->hasMany(SugarcaneRecord::class);
    }

    public function harvests()
    {
        return $this->hasMany(Harvest::class);
    }

    public function equipmentRequests()
    {
        return $this->hasMany(EquipmentRequest::class, 'user_id', 'user_id');
    }

    public function meetingAttendances()
    {
        return $this->hasMany(MeetingAttendance::class);
    }
}
