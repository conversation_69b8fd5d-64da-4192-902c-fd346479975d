<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
    <meta name="csrf-token" content="">
</head>
<body>
    <h2>Login Test</h2>
    <form id="loginForm">
        <div>
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="password" value="password" required>
        </div>
        <div>
            <button type="submit">Login</button>
        </div>
    </form>
    
    <div id="result"></div>

    <script>
        // Get CSRF token from the server
        fetch('http://127.0.0.1:8000/login')
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const csrfToken = doc.querySelector('meta[name="csrf-token"]').getAttribute('content');
                document.querySelector('meta[name="csrf-token"]').setAttribute('content', csrfToken);
                console.log('CSRF Token:', csrfToken);
            });

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            const formData = new FormData();
            formData.append('email', email);
            formData.append('password', password);
            formData.append('_token', csrfToken);
            
            fetch('http://127.0.0.1:8000/login', {
                method: 'POST',
                body: formData,
                credentials: 'include'
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(data => {
                document.getElementById('result').innerHTML = '<pre>' + data + '</pre>';
                console.log('Response:', data);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            });
        });
    </script>
</body>
</html>
