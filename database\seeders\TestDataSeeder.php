<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Farmer;
use App\Models\SavingsAccount;
use App\Models\Loan;
use App\Models\Contribution;
use Carbon\Carbon;

class TestDataSeeder extends Seeder
{
    public function run(): void
    {
        // Find the member user
        $memberUser = User::where('email', '<EMAIL>')->first();
        
        if (!$memberUser) {
            $this->command->error('Member user not found. Please run MemberSeeder first.');
            return;
        }

        // Create or find farmer profile
        $farmer = Farmer::firstOrCreate(
            ['user_id' => $memberUser->id],
            [
                'farm_size' => '5.5 hectares',
                'location_coordinates' => '-15.4167, 28.2833',
                'membership_date' => Carbon::now()->subMonths(12),
                'identification_number' => 'ID123456789',
                'bank_account_number' => '**********',
                'bank_name' => 'Zanaco Bank',
                'gender' => 'male',
                'address' => '123 Farm Road, Lusaka, Zambia',
                'land_size' => 10.0,
                'status' => 'active'
            ]
        );

        // Create savings accounts
        SavingsAccount::firstOrCreate(
            ['farmer_id' => $farmer->id, 'account_number' => 'SAV001'],
            [
                'balance' => 15000.00,
                'opening_date' => Carbon::now()->subMonths(10),
                'status' => 'active',
            ]
        );

        SavingsAccount::firstOrCreate(
            ['farmer_id' => $farmer->id, 'account_number' => 'SAV002'],
            [
                'balance' => 5000.00,
                'opening_date' => Carbon::now()->subMonths(8),
                'status' => 'active',
            ]
        );

        // Create loans
        Loan::firstOrCreate(
            ['farmer_id' => $farmer->id, 'purpose' => 'Equipment Purchase'],
            [
                'amount' => 25000.00,
                'interest_rate' => 12.5,
                'term_months' => 24,
                'status' => 'approved',
                'application_date' => Carbon::now()->subMonths(6),
                'approval_date' => Carbon::now()->subMonths(6)->addDays(5),
                'disbursement_date' => Carbon::now()->subMonths(6)->addDays(10),
            ]
        );

        Loan::firstOrCreate(
            ['farmer_id' => $farmer->id, 'purpose' => 'Seed Purchase'],
            [
                'amount' => 8000.00,
                'interest_rate' => 10.0,
                'term_months' => 12,
                'status' => 'pending',
                'application_date' => Carbon::now()->subDays(15),
            ]
        );

        // Create contributions
        for ($i = 1; $i <= 6; $i++) {
            Contribution::firstOrCreate(
                [
                    'farmer_id' => $farmer->id,
                    'contribution_date' => Carbon::now()->subMonths($i)->startOfMonth()
                ],
                [
                    'amount' => 500.00,
                    'payment_method' => 'bank_transfer',
                    'notes' => 'Monthly membership contribution',
                    'recorded_by' => $memberUser->id,
                    'receipt_number' => 'RCP' . str_pad($i, 3, '0', STR_PAD_LEFT),
                ]
            );
        }

        // Add some special contributions
        Contribution::firstOrCreate(
            [
                'farmer_id' => $farmer->id,
                'contribution_date' => Carbon::now()->subMonths(3),
                'payment_method' => 'cash'
            ],
            [
                'amount' => 2000.00,
                'notes' => 'Equipment fund contribution',
                'recorded_by' => $memberUser->id,
                'receipt_number' => 'RCP007',
            ]
        );

        $this->command->info('Test data created successfully for member: ' . $memberUser->email);
    }
}
