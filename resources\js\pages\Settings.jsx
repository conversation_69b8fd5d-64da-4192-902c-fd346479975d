import React, { useState } from 'react';
import { Head, usePage } from '@inertiajs/react';
import { Inertia } from '@inertiajs/inertia';
import AdminLayout from '@/Layouts/AdminLayout';

const Settings = () => {
  const { settings, auth, users, roles } = usePage().props;
  const [activeTab, setActiveTab] = useState('financial');
  const [formData, setFormData] = useState({
    // Financial Settings
    currency: settings.currency || 'K',
    savingsInterestRate: settings.savingsInterestRate || 0,
    loanInterestRate: settings.loanInterestRate || 0,
    minimumSavings: settings.minimumSavings || 0,
    
    // Notification Settings
    emailNotifications: settings.emailNotifications === 'true',
    smsNotifications: settings.smsNotifications === 'true',
    pushNotifications: settings.pushNotifications === 'true',
    
    // Security Settings
    sessionTimeout: settings.sessionTimeout || 0,
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    Inertia.post(route('admin.settings.update'), formData, {
      onSuccess: () => {
        alert('Settings saved successfully!');
      },
      onError: (errors) => {
        console.error('Error saving settings:', errors);
        alert('Error saving settings. Please check console for details.');
      },
    });
  };

  return (
    <AdminLayout auth={auth}>
      <Head title="Cooperative Settings" />
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800">Cooperative Settings</h1>
            <button 
              onClick={handleSubmit}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Save Changes
            </button>
          </div>

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            {/* Settings Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex overflow-x-auto">
                
                <button
                  onClick={() => setActiveTab('financial')}
                  className={`px-6 py-4 font-medium text-sm whitespace-nowrap ${
                    activeTab === 'financial' 
                      ? 'border-b-2 border-green-600 text-green-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <i className="fas fa-money-bill-wave mr-2"></i> Financial
                </button>
                <button
                  onClick={() => setActiveTab('notifications')}
                  className={`px-6 py-4 font-medium text-sm whitespace-nowrap ${
                    activeTab === 'notifications' 
                      ? 'border-b-2 border-green-600 text-green-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <i className="fas fa-bell mr-2"></i> Notifications
                </button>
                <button
                  onClick={() => setActiveTab('security')}
                  className={`px-6 py-4 font-medium text-sm whitespace-nowrap ${
                    activeTab === 'security' 
                      ? 'border-b-2 border-green-600 text-green-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <i className="fas fa-shield-alt mr-2"></i> Security
                </button>
                <button
                  onClick={() => setActiveTab('users')}
                  className={`px-6 py-4 font-medium text-sm whitespace-nowrap ${
                    activeTab === 'users' 
                      ? 'border-b-2 border-green-600 text-green-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <i className="fas fa-users mr-2"></i> Users & Permissions
                </button>
              </nav>
            </div>

            {/* Settings Content */}
            <div className="p-6">
              

              {/* Financial Settings */}
              {activeTab === 'financial' && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">Financial Configuration</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Default Currency</label>
                      <select
                        name="currency"
                        value={formData.currency}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md p-2"
                      >
                        <option value="K">K - Malawian Kwacha</option>
                        <option value="USD">USD - US Dollar</option>
                        <option value="KES">KES - Kenyan Shilling</option>
                        <option value="TZS">TZS - Tanzanian Shilling</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Savings Interest Rate (% per annum)
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          name="savingsInterestRate"
                          value={formData.savingsInterestRate}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md p-2 pr-8"
                          min="0"
                          max="100"
                          step="0.1"
                        />
                        <span className="absolute right-3 top-2 text-gray-500">%</span>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Loan Interest Rate (% per annum)
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          name="loanInterestRate"
                          value={formData.loanInterestRate}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md p-2 pr-8"
                          min="0"
                          max="100"
                          step="0.1"
                        />
                        <span className="absolute right-3 top-2 text-gray-500">%</span>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Minimum Savings Contribution
                      </label>
                      <div className="relative">
                        <span className="absolute left-3 top-2 text-gray-500">{formData.currency}</span>
                        <input
                          type="number"
                          name="minimumSavings"
                          value={formData.minimumSavings}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md p-2 pl-12"
                          min="0"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="pt-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Financial Year</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Start Date</label>
                        <input
                          type="date"
                          className="w-full border border-gray-300 rounded-md p-2"
                          value="2023-01-01"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">End Date</label>
                        <input
                          type="date"
                          className="w-full border border-gray-300 rounded-md p-2"
                          value="2023-12-31"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Notification Settings */}
              {activeTab === 'notifications' && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">Notification Preferences</h2>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-800">Email Notifications</h3>
                        <p className="text-sm text-gray-500">Receive important updates via email</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="emailNotifications"
                          checked={formData.emailNotifications}
                          onChange={handleChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                      </label>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-800">SMS Notifications</h3>
                        <p className="text-sm text-gray-500">Receive SMS alerts for important activities</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="smsNotifications"
                          checked={formData.smsNotifications}
                          onChange={handleChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                      </label>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-800">Push Notifications</h3>
                        <p className="text-sm text-gray-500">Get real-time updates on your mobile device</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="pushNotifications"
                          checked={formData.pushNotifications}
                          onChange={handleChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                      </label>
                    </div>
                  </div>
                  
                  <div className="pt-4">
                    <h3 className="font-medium text-gray-800 mb-2">Notification Templates</h3>
                    <div className="space-y-3">
                      <div className="p-3 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">Loan Approval</span>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </div>
                      </div>
                      <div className="p-3 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">Contribution Reminder</span>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </div>
                      </div>
                      <div className="p-3 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">Meeting Announcement</span>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeTab === 'security' && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">Security Settings</h2>
                  
                  <div className="space-y-4">
                    
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Session Timeout (minutes)</label>
                      <input
                        type="number"
                        name="sessionTimeout"
                        value={formData.sessionTimeout}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md p-2"
                        min="5"
                        max="120"
                      />
                    </div>
                    
                    
                    
                    <div className="pt-4">
                      <h3 className="font-medium text-gray-800 mb-2">Active Sessions</h3>
                      <div className="space-y-3">
                        <div className="p-3 border border-gray-200 rounded-md">
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="font-medium">Chrome on Windows</p>
                              <p className="text-sm text-gray-500">Mzuzu, Malawi • Last active: 2 hours ago</p>
                            </div>
                            <button className="text-red-600 hover:text-red-800 text-sm">
                              Logout
                            </button>
                          </div>
                        </div>
                        <div className="p-3 border border-gray-200 rounded-md">
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="font-medium">Safari on iPhone</p>
                              <p className="text-sm text-gray-500">Mzuzu, Malawi • Last active: 1 day ago</p>
                            </div>
                            <button className="text-red-600 hover:text-red-800 text-sm">
                              Logout
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Users & Permissions */}
              {activeTab === 'users' && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">Users & Permissions</h2>
                  
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="font-medium text-gray-800">Staff Members</h3>
                    <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm">
                      <i className="fas fa-plus mr-2"></i> Add Staff
                    </button>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg overflow-hidden border border-gray-200">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                      {users.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <span className="text-blue-800 font-medium">{user.name.charAt(0).toUpperCase()}</span>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{user.name}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${user.role && user.role.name === 'Admin' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>{user.role ? user.role.name : 'N/A'}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>{user.is_active ? 'Active' : 'Inactive'}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button className="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                            <button className="text-red-600 hover:text-red-900">Remove</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    </table>
                  </div>
                  
                  <div className="pt-6">
                    <h3 className="font-medium text-gray-800 mb-3">Role Permissions</h3>
                    <div className="bg-gray-50 rounded-lg overflow-hidden border border-gray-200">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Savings</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loans</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reports</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Settings</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {roles.map((role) => (
                            <tr key={role.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{role.name}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {role.permissions.some(p => p.name === 'members') ? (
                                  <i className="fas fa-check text-green-500"></i>
                                ) : (
                                  <i className="fas fa-times text-red-500"></i>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {role.permissions.some(p => p.name === 'savings') ? (
                                  <i className="fas fa-check text-green-500"></i>
                                ) : (
                                  <i className="fas fa-times text-red-500"></i>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {role.permissions.some(p => p.name === 'loans') ? (
                                  <i className="fas fa-check text-green-500"></i>
                                ) : (
                                  <i className="fas fa-times text-red-500"></i>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {role.permissions.some(p => p.name === 'reports') ? (
                                  <i className="fas fa-check text-green-500"></i>
                                ) : (
                                  <i className="fas fa-times text-red-500"></i>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {role.permissions.some(p => p.name === 'settings') ? (
                                  <i className="fas fa-check text-green-500"></i>
                                ) : (
                                  <i className="fas fa-times text-red-500"></i>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Settings;