<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MailerSendEmailService
{
    protected $apiKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.mailersend.api_key', env('MAILERSEND_EMAIL_API_KEY'));
        $this->baseUrl = 'https://api.mailersend.com/v1';
    }

    /**
     * Send email using MailerSend API.
     */
    public function sendEmail($to, $subject, $htmlContent, $textContent = null, $fromEmail = null, $fromName = null)
    {
        try {
            if (!$this->apiKey) {
                throw new \Exception('MailerSend API key not configured');
            }

            $fromEmail = $fromEmail ?? config('mail.from.address', '<EMAIL>');
            $fromName = $fromName ?? config('mail.from.name', 'Siyamphanje Cooperative');

            $data = [
                'from' => [
                    'email' => $fromEmail,
                    'name' => $fromName,
                ],
                'to' => [
                    [
                        'email' => $to,
                        'name' => $to,
                    ]
                ],
                'subject' => $subject,
                'html' => $htmlContent,
            ];

            if ($textContent) {
                $data['text'] = $textContent;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest',
            ])->post($this->baseUrl . '/email', $data);

            if ($response->successful()) {
                $responseData = $response->json();
                
                Log::info('Email sent successfully via MailerSend', [
                    'to' => $to,
                    'subject' => $subject,
                    'message_id' => $responseData['data']['id'] ?? 'unknown'
                ]);

                return [
                    'success' => true,
                    'message_id' => $responseData['data']['id'] ?? null,
                    'response' => $response->body()
                ];
            } else {
                $errorData = $response->json();
                $errorMessage = $errorData['message'] ?? 'MailerSend email API error';

                Log::error('Email sending failed via MailerSend', [
                    'to' => $to,
                    'subject' => $subject,
                    'error' => $errorMessage,
                    'response' => $response->body()
                ]);

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'response' => $response->body()
                ];
            }
        } catch (\Exception $e) {
            Log::error('MailerSend email service error', [
                'to' => $to,
                'subject' => $subject,
                'exception' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Email service error: ' . $e->getMessage(),
                'response' => null
            ];
        }
    }

    /**
     * Send welcome email to new member.
     */
    public function sendWelcomeEmail($email, $name, $loginUrl = null)
    {
        $loginUrl = $loginUrl ?? url('/login');
        
        $subject = 'Welcome to Siyamphanje Cooperative!';
        
        $htmlContent = "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #2d5a27;'>Welcome to Siyamphanje Cooperative!</h2>
                
                <p>Dear {$name},</p>
                
                <p>We're excited to welcome you as a new member of Siyamphanje Cooperative! Your membership application has been approved.</p>
                
                <p>You will receive your login credentials via SMS shortly. Once you receive them, you can access your member portal at:</p>
                
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='{$loginUrl}' style='background-color: #2d5a27; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                        Access Member Portal
                    </a>
                </p>
                
                <p>As a member, you'll have access to:</p>
                <ul>
                    <li>Member directory and networking</li>
                    <li>Event calendar and meeting schedules</li>
                    <li>Document library and resources</li>
                    <li>Equipment borrowing system</li>
                    <li>Financial services and loan applications</li>
                </ul>
                
                <p>If you have any questions or need assistance, please don't hesitate to contact us.</p>
                
                <p>Welcome aboard!</p>
                
                <p style='margin-top: 30px;'>
                    Best regards,<br>
                    <strong>Siyamphanje Cooperative Team</strong>
                </p>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                <p style='font-size: 12px; color: #666; text-align: center;'>
                    This email was sent to {$email}. If you received this email in error, please ignore it.
                </p>
            </div>
        </body>
        </html>";

        $textContent = "Welcome to Siyamphanje Cooperative!\n\n" .
                      "Dear {$name},\n\n" .
                      "We're excited to welcome you as a new member of Siyamphanje Cooperative! " .
                      "Your membership application has been approved.\n\n" .
                      "You will receive your login credentials via SMS shortly. " .
                      "Once you receive them, you can access your member portal at: {$loginUrl}\n\n" .
                      "Welcome aboard!\n\n" .
                      "Best regards,\n" .
                      "Siyamphanje Cooperative Team";

        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }

    /**
     * Send password reset email.
     */
    public function sendPasswordResetEmail($email, $name, $resetUrl)
    {
        $subject = 'Password Reset - Siyamphanje Cooperative';
        
        $htmlContent = "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #2d5a27;'>Password Reset Request</h2>
                
                <p>Dear {$name},</p>
                
                <p>We received a request to reset your password for your Siyamphanje Cooperative account.</p>
                
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='{$resetUrl}' style='background-color: #2d5a27; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                        Reset Password
                    </a>
                </p>
                
                <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
                
                <p>This link will expire in 60 minutes for security reasons.</p>
                
                <p style='margin-top: 30px;'>
                    Best regards,<br>
                    <strong>Siyamphanje Cooperative Team</strong>
                </p>
            </div>
        </body>
        </html>";

        $textContent = "Password Reset Request\n\n" .
                      "Dear {$name},\n\n" .
                      "We received a request to reset your password for your Siyamphanje Cooperative account.\n\n" .
                      "Please visit the following link to reset your password: {$resetUrl}\n\n" .
                      "If you didn't request this password reset, please ignore this email.\n\n" .
                      "Best regards,\n" .
                      "Siyamphanje Cooperative Team";

        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }

    /**
     * Send notification email.
     */
    public function sendNotificationEmail($email, $name, $title, $content)
    {
        $subject = $title . ' - Siyamphanje Cooperative';
        
        $htmlContent = "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #2d5a27;'>{$title}</h2>
                
                <p>Dear {$name},</p>
                
                <div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                    {$content}
                </div>
                
                <p style='margin-top: 30px;'>
                    Best regards,<br>
                    <strong>Siyamphanje Cooperative Team</strong>
                </p>
            </div>
        </body>
        </html>";

        $textContent = "{$title}\n\n" .
                      "Dear {$name},\n\n" .
                      strip_tags($content) . "\n\n" .
                      "Best regards,\n" .
                      "Siyamphanje Cooperative Team";

        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }
}
