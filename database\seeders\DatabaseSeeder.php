<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * This seeder creates the essential users for the cooperative management system:
     * - Admin user for system administration
     * - Member user for testing member functionality
     * - Staff user for staff operations
     */
    public function run(): void
    {
        $this->call([
            AdminSeeder::class,
            MemberSeeder::class,
            StaffSeeder::class,
        ]);

        $this->command->info('Essential users created successfully!');
        $this->command->info('- Admin user: <EMAIL>');
        $this->command->info('- Member user: <EMAIL>');
        $this->command->info('- Staff user: <EMAIL>');
    }
}
