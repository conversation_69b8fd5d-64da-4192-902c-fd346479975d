<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransportRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'region_name',
        'distance_km',
        'rate_per_ton',
        'fuel_surcharge',
        'is_active',
        'description',
    ];

    protected $casts = [
        'distance_km' => 'decimal:2',
        'rate_per_ton' => 'decimal:2',
        'fuel_surcharge' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get transport deliveries using this rate
     */
    public function transportDeliveries()
    {
        return $this->hasMany(TransportDelivery::class);
    }

    /**
     * Get formatted rate per ton
     */
    public function getFormattedRateAttribute()
    {
        return 'MWK ' . number_format($this->rate_per_ton, 2) . '/ton';
    }

    /**
     * Get total rate including surcharge
     */
    public function getTotalRatePerTonAttribute()
    {
        return $this->rate_per_ton + $this->fuel_surcharge;
    }

    /**
     * Get formatted total rate
     */
    public function getFormattedTotalRateAttribute()
    {
        return 'MWK ' . number_format($this->total_rate_per_ton, 2) . '/ton';
    }

    /**
     * Calculate transport cost for given weight
     */
    public function calculateCost($weightTons)
    {
        return $weightTons * $this->total_rate_per_ton;
    }

    /**
     * Scope for active rates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for rates by region
     */
    public function scopeByRegion($query, $region)
    {
        return $query->where('region_name', 'like', "%{$region}%");
    }
}
