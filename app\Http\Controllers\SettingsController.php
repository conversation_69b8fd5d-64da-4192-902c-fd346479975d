<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\Role;
use App\Models\Farmer;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class SettingsController extends Controller
{
    public function index()
    {
        // Get current system settings
        $systemSettings = SystemSetting::all()->pluck('value', 'key')->toArray();

        $settings = [
            'cooperative' => [
                'name' => $systemSettings['cooperative_name'] ?? config('app.name', 'Siyamphanje Cooperative'),
                'description' => $systemSettings['cooperative_description'] ?? 'A modern agricultural cooperative management system',
                'address' => $systemSettings['cooperative_address'] ?? 'BT, Malawi',
                'phone' => $systemSettings['cooperative_phone'] ?? '+265 XXX XXX XXX',
                'email' => $systemSettings['cooperative_email'] ?? '<EMAIL>',
                'registration_number' => $systemSettings['cooperative_registration'] ?? 'COOP-2023-001',
            ],
            'financial' => [
                'default_interest_rate' => (float)($systemSettings['default_interest_rate'] ?? 5.0),
                'minimum_loan_amount' => (int)($systemSettings['minimum_loan_amount'] ?? 1000),
                'maximum_loan_amount' => (int)($systemSettings['maximum_loan_amount'] ?? 100000),
                'minimum_savings_balance' => (int)($systemSettings['minimum_savings_balance'] ?? 100),
                'contribution_frequency' => $systemSettings['contribution_frequency'] ?? 'monthly',
                'late_payment_penalty' => (float)($systemSettings['late_payment_penalty'] ?? 2.0),
            ],
            'system' => [
                'maintenance_mode' => (bool)($systemSettings['maintenance_mode'] ?? false),
                'allow_registration' => (bool)($systemSettings['allow_registration'] ?? true),
                'require_approval' => (bool)($systemSettings['require_approval'] ?? true),
                'backup_frequency' => $systemSettings['backup_frequency'] ?? 'daily',
                'session_timeout' => (int)($systemSettings['session_timeout'] ?? 120),
            ],
            'notifications' => [
                'email_notifications' => (bool)($systemSettings['email_notifications'] ?? true),
                'sms_notifications' => (bool)($systemSettings['sms_notifications'] ?? false),
                'loan_reminders' => (bool)($systemSettings['loan_reminders'] ?? true),
                'contribution_reminders' => (bool)($systemSettings['contribution_reminders'] ?? true),
                'equipment_notifications' => (bool)($systemSettings['equipment_notifications'] ?? true),
            ]
        ];

        // Get system statistics
        $systemStats = [
            'total_users' => User::count(),
            'total_farmers' => Farmer::count(),
            'database_size' => $this->getDatabaseSize(),
            'last_backup' => $this->getLastBackupDate(),
            'system_uptime' => $this->getSystemUptime(),
        ];

        $users = User::with('role')->get();
        $roles = Role::with('permissions')->get();

        return Inertia::render('Admin/Settings', [
            'settings' => $settings,
            'systemStats' => $systemStats,
            'users' => $users,
            'roles' => $roles,
            'auth' => [
                'user' => auth()->user()
            ]
        ]);
    }

    public function update(Request $request)
    {
        foreach ($request->all() as $key => $value) {
            SystemSetting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return redirect()->back()->with('success', 'Settings updated successfully.');
    }

    private function getDatabaseSize()
    {
        try {
            $size = DB::select("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema = ?", [config('database.connections.mysql.database')]);
            return $size[0]->{'DB Size in MB'} . ' MB';
        } catch (\Exception $e) {
            return 'N/A';
        }
    }

    private function getLastBackupDate()
    {
        // This would typically check your backup system
        // For now, return a placeholder
        return now()->subDays(1)->format('Y-m-d H:i:s');
    }

    private function getSystemUptime()
    {
        // This would typically check system uptime
        // For now, return a placeholder
        return '99.9%';
    }

    public function updateCooperativeSettings(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:500',
            'address' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'registration_number' => 'required|string|max:100',
        ]);

        $settings = [
            'cooperative_name' => $request->name,
            'cooperative_description' => $request->description,
            'cooperative_address' => $request->address,
            'cooperative_phone' => $request->phone,
            'cooperative_email' => $request->email,
            'cooperative_registration' => $request->registration_number,
        ];

        foreach ($settings as $key => $value) {
            SystemSetting::updateOrCreate(['key' => $key], ['value' => $value]);
        }

        return redirect()->back()->with('success', 'Cooperative settings updated successfully.');
    }

    public function updateFinancialSettings(Request $request)
    {
        $request->validate([
            'default_interest_rate' => 'required|numeric|min:0|max:100',
            'minimum_loan_amount' => 'required|integer|min:1',
            'maximum_loan_amount' => 'required|integer|min:1',
            'minimum_savings_balance' => 'required|integer|min:0',
            'contribution_frequency' => 'required|in:weekly,monthly,quarterly',
            'late_payment_penalty' => 'required|numeric|min:0|max:100',
        ]);

        $settings = [
            'default_interest_rate' => $request->default_interest_rate,
            'minimum_loan_amount' => $request->minimum_loan_amount,
            'maximum_loan_amount' => $request->maximum_loan_amount,
            'minimum_savings_balance' => $request->minimum_savings_balance,
            'contribution_frequency' => $request->contribution_frequency,
            'late_payment_penalty' => $request->late_payment_penalty,
        ];

        foreach ($settings as $key => $value) {
            SystemSetting::updateOrCreate(['key' => $key], ['value' => $value]);
        }

        return redirect()->back()->with('success', 'Financial settings updated successfully.');
    }

    public function updateSystemSettings(Request $request)
    {
        $request->validate([
            'maintenance_mode' => 'boolean',
            'allow_registration' => 'boolean',
            'require_approval' => 'boolean',
            'backup_frequency' => 'required|in:daily,weekly,monthly',
            'session_timeout' => 'required|integer|min:30|max:480',
        ]);

        $settings = [
            'maintenance_mode' => $request->maintenance_mode ? '1' : '0',
            'allow_registration' => $request->allow_registration ? '1' : '0',
            'require_approval' => $request->require_approval ? '1' : '0',
            'backup_frequency' => $request->backup_frequency,
            'session_timeout' => $request->session_timeout,
        ];

        foreach ($settings as $key => $value) {
            SystemSetting::updateOrCreate(['key' => $key], ['value' => $value]);
        }

        return redirect()->back()->with('success', 'System settings updated successfully.');
    }

    public function updateNotificationSettings(Request $request)
    {
        $settings = [
            'email_notifications' => $request->email_notifications ? '1' : '0',
            'sms_notifications' => $request->sms_notifications ? '1' : '0',
            'loan_reminders' => $request->loan_reminders ? '1' : '0',
            'contribution_reminders' => $request->contribution_reminders ? '1' : '0',
            'equipment_notifications' => $request->equipment_notifications ? '1' : '0',
        ];

        foreach ($settings as $key => $value) {
            SystemSetting::updateOrCreate(['key' => $key], ['value' => $value]);
        }

        return redirect()->back()->with('success', 'Notification settings updated successfully.');
    }
}
