<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_items', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('category', ['equipment', 'supplies', 'seeds', 'fertilizer', 'tools', 'other']);
            $table->string('sku')->unique()->nullable(); // stock keeping unit
            $table->integer('quantity');
            $table->string('unit'); // pieces, kg, liters, etc.
            $table->decimal('unit_cost', 10, 2)->nullable();
            $table->decimal('total_value', 12, 2)->nullable();
            $table->string('location')->nullable(); // where it's stored
            $table->integer('minimum_stock')->default(0); // alert threshold
            $table->enum('status', ['available', 'low_stock', 'out_of_stock', 'reserved'])->default('available');
            $table->date('purchase_date')->nullable();
            $table->date('expiry_date')->nullable();
            $table->string('supplier')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('added_by')->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_items');
    }
};
