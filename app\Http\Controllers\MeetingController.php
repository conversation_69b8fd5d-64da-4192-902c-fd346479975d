<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Meeting;
use App\Models\MeetingAttendance;
use App\Models\Farmer;
use App\Models\Notification;
use Inertia\Inertia;
use Carbon\Carbon;

class MeetingController extends Controller
{
    /**
     * Display admin meetings management page.
     */
    public function adminIndex()
    {
        $meetings = Meeting::with(['attendances.farmer'])
            ->orderBy('meeting_date', 'desc')
            ->get()
            ->map(function ($meeting) {
                return [
                    'id' => $meeting->id,
                    'title' => $meeting->title,
                    'description' => $meeting->description,
                    'meeting_date' => $meeting->meeting_date,
                    'meeting_time' => $meeting->meeting_time,
                    'location' => $meeting->location,
                    'meeting_type' => $meeting->meeting_type,
                    'status' => $meeting->status,
                    'max_attendees' => $meeting->max_attendees,
                    'created_at' => $meeting->created_at,
                    'total_attendees' => $meeting->attendances->count(),
                    'confirmed_attendees' => $meeting->attendances->where('attended', true)->count(),
                ];
            });

        $stats = [
            'total_meetings' => Meeting::count(),
            'upcoming_meetings' => Meeting::where('meeting_date', '>=', now())->count(),
            'completed_meetings' => Meeting::where('meeting_date', '<', now())->count(),
            'total_members' => Farmer::count(),
        ];

        return Inertia::render('Admin/Meetings', [
            'meetings' => $meetings,
            'stats' => $stats,
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Display member meetings page.
     */
    public function memberIndex()
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            // If no farmer profile, show all meetings but without attendance data
            $meetings = Meeting::where('status', 'scheduled')
                ->orderBy('meeting_date', 'asc')
                ->get()
                ->map(function ($meeting) {
                    return [
                        'id' => $meeting->id,
                        'title' => $meeting->title,
                        'description' => $meeting->description,
                        'meeting_date' => $meeting->meeting_date,
                        'meeting_time' => $meeting->meeting_time,
                        'location' => $meeting->location,
                        'meeting_type' => $meeting->meeting_type,
                        'agenda' => $meeting->agenda,
                        'user_attendance' => null,
                    ];
                });

            return Inertia::render('Member/Meetings', [
                'meetings' => $meetings,
                'auth' => ['user' => $user],
                'warning' => 'No farmer profile found. Please contact admin to set up your farmer profile.'
            ]);
        }

        // Get all scheduled meetings
        $meetings = Meeting::where('status', 'scheduled')
            ->orderBy('meeting_date', 'asc')
            ->get()
            ->map(function ($meeting) use ($farmer) {
                // Get user's attendance record for this meeting
                $userAttendance = MeetingAttendance::where('meeting_id', $meeting->id)
                    ->where('farmer_id', $farmer->id)
                    ->first();

                return [
                    'id' => $meeting->id,
                    'title' => $meeting->title,
                    'description' => $meeting->description,
                    'meeting_date' => $meeting->meeting_date,
                    'meeting_time' => $meeting->meeting_time,
                    'location' => $meeting->location,
                    'meeting_type' => $meeting->meeting_type,
                    'agenda' => $meeting->agenda,
                    'user_attendance' => $userAttendance ? [
                        'id' => $userAttendance->id,
                        'attended' => $userAttendance->attended,
                        'response_status' => $userAttendance->response_status ?? 'pending',
                    ] : null,
                ];
            });

        return Inertia::render('Member/Meetings', [
            'meetings' => $meetings,
            'auth' => ['user' => $user]
        ]);
    }

    /**
     * Show meeting details for members
     */
    public function memberShow($id)
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        $meeting = Meeting::with(['calledBy', 'attendances.farmer.user'])
            ->findOrFail($id);

        // Get user's attendance record if exists
        $userAttendance = null;
        if ($farmer) {
            $userAttendance = $meeting->attendances()
                ->where('farmer_id', $farmer->id)
                ->first();
        }

        $meetingData = [
            'id' => $meeting->id,
            'title' => $meeting->title,
            'description' => $meeting->description,
            'meeting_date' => $meeting->meeting_date,
            'meeting_time' => $meeting->meeting_time,
            'location' => $meeting->location,
            'meeting_type' => $meeting->meeting_type,
            'agenda' => $meeting->agenda,
            'status' => $meeting->status,
            'max_attendees' => $meeting->max_attendees,
            'created_by' => $meeting->calledBy->name ?? 'Unknown',
            'user_attendance' => $userAttendance ? [
                'id' => $userAttendance->id,
                'response_status' => $userAttendance->response_status ?? 'pending',
                'attended' => $userAttendance->attended,
                'comments' => $userAttendance->comments,
            ] : null,
            'attendees' => $meeting->attendances->map(function ($attendance) {
                return [
                    'id' => $attendance->id,
                    'farmer_name' => $attendance->farmer->user->name ?? 'Unknown',
                    'response_status' => $attendance->response_status,
                    'attended' => $attendance->attended,
                    'comments' => $attendance->comments,
                ];
            }),
            'is_upcoming' => $meeting->meeting_date >= \Carbon\Carbon::today(),
        ];

        return Inertia::render('Meetings/Show', [
            'meeting' => $meetingData,
            'auth' => ['user' => $user]
        ]);
    }

    /**
     * Store a newly created meeting.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'meeting_date' => 'required|date|after_or_equal:today',
            'meeting_time' => 'required|string',
            'location' => 'required|string|max:255',
            'meeting_type' => 'required|in:general,board,emergency,training,annual',
            'agenda' => 'nullable|string|max:2000',
            'max_attendees' => 'nullable|integer|min:1',
            'send_notifications' => 'boolean',
        ]);

        // Combine date and time for the meeting_time field
        $meetingDateTime = $request->meeting_date . ' ' . $request->meeting_time . ':00';

        $meeting = Meeting::create([
            'title' => $request->title,
            'description' => $request->description,
            'meeting_date' => $request->meeting_date,
            'meeting_time' => $meetingDateTime,
            'location' => $request->location,
            'meeting_type' => $request->meeting_type,
            'agenda' => $request->agenda,
            'max_attendees' => $request->max_attendees,
            'status' => 'scheduled',
            'created_by' => auth()->id(),
            'called_by' => auth()->id(),
        ]);

        // Create attendance records for all farmers
        $farmers = Farmer::all(); // Get all farmers regardless of status
        foreach ($farmers as $farmer) {
            MeetingAttendance::create([
                'meeting_id' => $meeting->id,
                'farmer_id' => $farmer->id,
                'attended' => false,
                'response_status' => 'pending',
            ]);
        }

        // Send notifications if requested
        if ($request->send_notifications) {
            $this->sendMeetingNotifications($meeting, $farmers);
        }

        return redirect()->back()->with('success', 'Meeting scheduled successfully and notifications sent to all members!');
    }

    /**
     * Display meeting details.
     */
    public function show($id)
    {
        $meeting = Meeting::with(['attendances.farmer.user', 'calledBy'])->findOrFail($id);

        return Inertia::render('Admin/MeetingDetails', [
            'meeting' => [
                'id' => $meeting->id,
                'title' => $meeting->title,
                'description' => $meeting->description,
                'meeting_date' => $meeting->meeting_date,
                'meeting_time' => $meeting->meeting_time,
                'location' => $meeting->location,
                'meeting_type' => $meeting->meeting_type,
                'agenda' => $meeting->agenda,
                'status' => $meeting->status,
                'max_attendees' => $meeting->max_attendees,
                'created_by' => $meeting->calledBy->name ?? 'Unknown',
                'attendances' => $meeting->attendances->map(function ($attendance) {
                    return [
                        'id' => $attendance->id,
                        'farmer_name' => $attendance->farmer->user->name ?? 'Unknown Member',
                        'farmer_id' => $attendance->farmer->id,
                        'user_id' => $attendance->farmer->user->id ?? null,
                        'attended' => $attendance->attended,
                        'response_status' => $attendance->response_status,
                        'comments' => $attendance->comments,
                    ];
                }),
            ],
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Update meeting attendance response.
     */
    public function updateAttendance(Request $request, $meetingId)
    {
        $request->validate([
            'response_status' => 'required|in:attending,not_attending,maybe',
        ]);

        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile associated with this user.']);
        }

        $attendance = MeetingAttendance::where('meeting_id', $meetingId)
            ->where('farmer_id', $farmer->id)
            ->firstOrFail();

        $attendance->update([
            'response_status' => $request->response_status,
        ]);

        return redirect()->back()->with('success', 'Your attendance response has been updated!');
    }

    /**
     * Update meeting details.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'meeting_date' => 'required|date',
            'meeting_time' => 'required|string',
            'location' => 'required|string|max:255',
            'meeting_type' => 'required|in:general,board,emergency,training,annual',
            'agenda' => 'nullable|string|max:2000',
            'status' => 'required|in:scheduled,completed,cancelled',
        ]);

        $meeting = Meeting::findOrFail($id);

        // Combine date and time for the meeting_time field
        $meetingDateTime = $request->meeting_date . ' ' . $request->meeting_time . ':00';

        $meeting->update([
            'title' => $request->title,
            'description' => $request->description,
            'meeting_date' => $request->meeting_date,
            'meeting_time' => $meetingDateTime,
            'location' => $request->location,
            'meeting_type' => $request->meeting_type,
            'agenda' => $request->agenda,
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', 'Meeting updated successfully!');
    }

    /**
     * Delete a meeting.
     */
    public function destroy($id)
    {
        $meeting = Meeting::findOrFail($id);

        // Delete associated attendance records
        MeetingAttendance::where('meeting_id', $id)->delete();

        // Delete the meeting
        $meeting->delete();

        return redirect()->back()->with('success', 'Meeting deleted successfully!');
    }

    /**
     * Send meeting notifications to farmers.
     */
    private function sendMeetingNotifications($meeting, $farmers)
    {
        foreach ($farmers as $farmer) {
            if ($farmer->user) {
                Notification::create([
                    'user_id' => $farmer->user->id,
                    'title' => 'New Meeting Scheduled: ' . $meeting->title,
                    'message' => "A new meeting has been scheduled for {$meeting->meeting_date} at {$meeting->meeting_time}. Location: {$meeting->location}. Please confirm your attendance.",
                    'type' => 'meeting',
                    'is_read' => false,
                    'data' => json_encode([
                        'meeting_id' => $meeting->id,
                        'meeting_date' => $meeting->meeting_date,
                        'meeting_time' => $meeting->meeting_time,
                        'location' => $meeting->location,
                    ]),
                ]);
            }
        }
    }
}
