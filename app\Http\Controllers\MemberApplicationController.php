<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\MemberApplication;
use App\Models\User;
use App\Models\Farmer;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\WelcomeNewMember;

class MemberApplicationController extends Controller
{
    /**
     * Store a new member application from public registration
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:member_applications,email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'gender' => 'nullable|in:male,female,other',
            'identification_number' => 'nullable|string|max:50',
            'farm_size' => 'nullable|string|max:100',
            'land_size' => 'nullable|numeric|min:0',
            'location_coordinates' => 'nullable|string|max:100',
            'bank_name' => 'nullable|string|max:100',
            'bank_account_number' => 'nullable|string|max:50',
        ]);

        $application = MemberApplication::create($validated);

        Log::info('New member application submitted', [
            'application_id' => $application->id,
            'name' => $application->name,
            'email' => $application->email,
            'ip_address' => $request->ip(),
        ]);

        return redirect()->back()->with('success', 'Your membership application has been submitted successfully! You will be contacted once it has been reviewed.');
    }

    /**
     * Display pending applications for admin review
     */
    public function adminIndex(Request $request)
    {
        $status = $request->get('status', 'pending');
        $search = $request->get('search');

        $query = MemberApplication::with('reviewedBy')
            ->orderBy('created_at', 'desc');

        if ($status && $status !== 'all') {
            $query->where('status', $status);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $applications = $query->paginate(15);

        // Get statistics
        $stats = [
            'pending' => MemberApplication::pending()->count(),
            'approved' => MemberApplication::approved()->count(),
            'rejected' => MemberApplication::rejected()->count(),
            'total' => MemberApplication::count(),
        ];

        return Inertia::render('Admin/MemberApplications/Index', [
            'applications' => $applications,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'search' => $search,
            ],
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Show application details for admin review
     */
    public function show($id)
    {
        $application = MemberApplication::with('reviewedBy')->findOrFail($id);

        return Inertia::render('Admin/MemberApplications/Show', [
            'application' => $application,
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Approve application and create member account
     */
    public function approve(Request $request, $id)
    {
        $validated = $request->validate([
            'password' => 'required|string|min:8|confirmed',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        $application = MemberApplication::findOrFail($id);

        if (!$application->isPending()) {
            return redirect()->back()->with('error', 'This application has already been processed.');
        }

        // Get member role first
        $memberRole = Role::where('name', 'member')->first();
        if (!$memberRole) {
            return redirect()->back()->with('error', 'Member role not found. Please contact system administrator.');
        }

        // Create user account with role_id
        $user = User::create([
            'name' => $application->name,
            'email' => $application->email,
            'password' => Hash::make($validated['password']),
            'phone' => $application->phone,
            'address' => $application->address,
            'role_id' => $memberRole->id,
            'email_verified_at' => now(), // Auto-verify admin-approved accounts
        ]);

        // Create farmer profile
        Farmer::create([
            'user_id' => $user->id,
            'farm_size' => $application->farm_size,
            'land_size' => $application->land_size,
            'location_coordinates' => $application->location_coordinates,
            'identification_number' => $application->identification_number,
            'bank_account_number' => $application->bank_account_number,
            'bank_name' => $application->bank_name,
            'gender' => $application->gender,
            'membership_date' => now(),
            'status' => 'active',
        ]);

        // Update application status
        $application->update([
            'status' => 'approved',
            'admin_notes' => $validated['admin_notes'],
            'reviewed_by' => auth()->id(),
            'reviewed_at' => now(),
        ]);

        // Send welcome email with login credentials
        $emailMessage = "";
        try {
            Mail::to($application->email)->send(new WelcomeNewMember(
                $application->name,
                $application->email,
                $validated['password'],
                url('/login')
            ));

            Log::info('Welcome email with credentials sent successfully', [
                'application_id' => $application->id,
                'user_id' => $user->id,
                'email' => $application->email,
            ]);
            $emailMessage = " Welcome email with login credentials sent successfully.";
        } catch (\Exception $e) {
            Log::error('Email service error during approval', [
                'application_id' => $application->id,
                'user_id' => $user->id,
                'exception' => $e->getMessage(),
            ]);
            $emailMessage = " Note: Email delivery failed - please provide login details manually.";
        }

        Log::info('Member application approved', [
            'application_id' => $application->id,
            'user_id' => $user->id,
            'approved_by' => auth()->id(),
        ]);

        return redirect()->route('admin.applications.index')
            ->with('success', "Application approved! Member account created for {$application->name}.{$emailMessage}");
    }

    /**
     * Reject application
     */
    public function reject(Request $request, $id)
    {
        $validated = $request->validate([
            'rejection_reason' => 'required|string|max:500',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        $application = MemberApplication::findOrFail($id);

        if (!$application->isPending()) {
            return redirect()->back()->with('error', 'This application has already been processed.');
        }

        $application->update([
            'status' => 'rejected',
            'rejection_reason' => $validated['rejection_reason'],
            'admin_notes' => $validated['admin_notes'],
            'reviewed_by' => auth()->id(),
            'reviewed_at' => now(),
        ]);

        Log::info('Member application rejected', [
            'application_id' => $application->id,
            'rejected_by' => auth()->id(),
            'reason' => $validated['rejection_reason'],
        ]);

        return redirect()->route('admin.applications.index')
            ->with('success', "Application rejected for {$application->name}.");
    }

    /**
     * Delete application
     */
    public function destroy($id)
    {
        $application = MemberApplication::findOrFail($id);

        Log::info('Member application deleted', [
            'application_id' => $application->id,
            'name' => $application->name,
            'email' => $application->email,
            'deleted_by' => auth()->id(),
        ]);

        $application->delete();

        return redirect()->route('admin.applications.index')
            ->with('success', 'Application deleted successfully.');
    }
}
