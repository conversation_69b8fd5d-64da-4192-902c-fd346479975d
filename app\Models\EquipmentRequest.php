<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EquipmentRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'equipment_id',
        'user_id',
        'start_date',
        'end_date',
        'purpose',
        'operator_needed',
        'transport_needed',
        'notes',
        'estimated_cost',
        'status',
        'approved_by',
        'rejection_reason',
        'payment_status',
        'payment_date',
        'admin_notes'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'payment_date' => 'datetime',
        'operator_needed' => 'boolean',
        'transport_needed' => 'boolean',
        'estimated_cost' => 'decimal:2',
    ];

    public function equipment()
    {
        return $this->belongsTo(Equipment::class, 'equipment_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }
}
