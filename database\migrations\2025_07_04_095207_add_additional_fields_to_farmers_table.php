<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('farmers', function (Blueprint $table) {
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('bank_name');
            $table->text('address')->nullable()->after('gender');
            $table->decimal('land_size', 8, 2)->nullable()->comment('Land size in hectares')->after('address');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->after('land_size');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('farmers', function (Blueprint $table) {
            $table->dropColumn(['gender', 'address', 'land_size', 'status']);
        });
    }
};
