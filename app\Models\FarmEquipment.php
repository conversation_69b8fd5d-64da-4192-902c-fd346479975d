<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FarmEquipment extends Model
{
    use HasFactory;

    protected $fillable = [
        'owner_id',
        'name',
        'type',
        'model',
        'purchase_year',
        'value',
        'rental_cost_per_day',
        'description',
        'is_available',
        'available_for_rent'
    ];

    public function owner()
    {
        return $this->belongsTo(Farmer::class, 'owner_id');
    }

    public function equipmentRequests()
    {
        return $this->hasMany(EquipmentRequest::class, 'equipment_id');
    }
}
