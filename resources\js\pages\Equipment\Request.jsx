import React, { useState } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

const EquipmentRequest = ({ auth, availableEquipment, existingRequests }) => {
  const [selectedEquipment, setSelectedEquipment] = useState(null);

  const { data, setData, post, processing, errors, reset } = useForm({
    equipment_id: '',
    start_date: '',
    end_date: '',
    purpose: '',
    location: '',
    operator_needed: false,
    transport_needed: false,
    additional_requirements: '',
    emergency_contact: '',
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'MW',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-ZM');
  };

  const calculateRentalCost = () => {
    if (!selectedEquipment || !data.start_date || !data.end_date) return 0;
    
    const startDate = new Date(data.start_date);
    const endDate = new Date(data.end_date);
    const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    
    let totalCost = days * (selectedEquipment.daily_rate || 0);
    
    if (data.operator_needed && selectedEquipment.operator_rate) {
      totalCost += days * selectedEquipment.operator_rate;
    }
    
    if (data.transport_needed && selectedEquipment.transport_rate) {
      totalCost += selectedEquipment.transport_rate;
    }
    
    return totalCost;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    post('/equipment/request', {
      onSuccess: () => {
        reset();
        setSelectedEquipment(null);
      }
    });
  };

  const handleEquipmentSelect = (equipment) => {
    setSelectedEquipment(equipment);
    setData('equipment_id', equipment.id);
  };

  const getEquipmentStatus = (equipment) => {
    if (equipment.status === 'maintenance') return { color: 'bg-red-100 text-red-800', text: 'Under Maintenance' };
    if (equipment.status === 'rented') return { color: 'bg-yellow-100 text-yellow-800', text: 'Currently Rented' };
    return { color: 'bg-green-100 text-green-800', text: 'Available' };
  };

  const isEquipmentAvailable = (equipment) => {
    return equipment.status === 'available';
  };

  return (
    <MemberLayout user={auth.user} header="Request Equipment">
      <Head title="Request Equipment - Siyamphanje Cooperative" />
      
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-blue-50 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Request Equipment</h1>
                <p className="mt-2 text-gray-600">Borrow farm equipment for your agricultural activities</p>
              </div>
              <Link
                href="/dashboard/member"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Equipment Selection */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Available Equipment</h2>
                
                {availableEquipment && availableEquipment.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {availableEquipment.map((equipment) => {
                      const status = getEquipmentStatus(equipment);
                      const isAvailable = isEquipmentAvailable(equipment);
                      
                      return (
                        <div
                          key={equipment.id}
                          onClick={() => isAvailable && handleEquipmentSelect(equipment)}
                          className={`border-2 rounded-xl p-6 transition-all duration-300 ${
                            selectedEquipment?.id === equipment.id
                              ? 'border-indigo-500 bg-indigo-50'
                              : isAvailable
                              ? 'border-gray-200 hover:border-indigo-300 hover:bg-gray-50 cursor-pointer'
                              : 'border-gray-200 bg-gray-50 opacity-60 cursor-not-allowed'
                          }`}
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <h3 className="text-lg font-semibold text-gray-900">{equipment.name}</h3>
                              <p className="text-sm text-gray-600">{equipment.type}</p>
                            </div>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${status.color}`}>
                              {status.text}
                            </span>
                          </div>
                          
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Daily Rate:</span>
                              <span className="font-medium text-gray-900">{formatCurrency(equipment.daily_rate)}</span>
                            </div>
                            {equipment.operator_rate && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">Operator Rate:</span>
                                <span className="font-medium text-gray-900">{formatCurrency(equipment.operator_rate)}/day</span>
                              </div>
                            )}
                            {equipment.transport_rate && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">Transport:</span>
                                <span className="font-medium text-gray-900">{formatCurrency(equipment.transport_rate)}</span>
                              </div>
                            )}
                          </div>
                          
                          {equipment.description && (
                            <p className="mt-3 text-sm text-gray-600">{equipment.description}</p>
                          )}
                          
                          {selectedEquipment?.id === equipment.id && (
                            <div className="mt-4 pt-4 border-t border-indigo-200">
                              <div className="flex items-center text-indigo-600">
                                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span className="text-sm font-medium">Selected for request</span>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Equipment Available</h3>
                    <p className="text-gray-600">All equipment is currently rented or under maintenance.</p>
                  </div>
                )}
              </div>

              {/* Request Form */}
              {selectedEquipment && (
                <div className="bg-white rounded-2xl shadow-xl p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Request Details</h2>
                  
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Rental Period */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-2">
                          Start Date <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="date"
                          id="start_date"
                          value={data.start_date}
                          onChange={(e) => setData('start_date', e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                          required
                        />
                        {errors.start_date && <p className="mt-1 text-sm text-red-600">{errors.start_date}</p>}
                      </div>

                      <div>
                        <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-2">
                          End Date <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="date"
                          id="end_date"
                          value={data.end_date}
                          onChange={(e) => setData('end_date', e.target.value)}
                          min={data.start_date || new Date().toISOString().split('T')[0]}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                          required
                        />
                        {errors.end_date && <p className="mt-1 text-sm text-red-600">{errors.end_date}</p>}
                      </div>
                    </div>

                    {/* Purpose and Location */}
                    <div>
                      <label htmlFor="purpose" className="block text-sm font-medium text-gray-700 mb-2">
                        Purpose of Use <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        id="purpose"
                        value={data.purpose}
                        onChange={(e) => setData('purpose', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Describe how you plan to use this equipment..."
                        required
                      />
                      {errors.purpose && <p className="mt-1 text-sm text-red-600">{errors.purpose}</p>}
                    </div>

                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                        Usage Location <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="location"
                        value={data.location}
                        onChange={(e) => setData('location', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Where will you use this equipment?"
                        required
                      />
                      {errors.location && <p className="mt-1 text-sm text-red-600">{errors.location}</p>}
                    </div>

                    {/* Additional Services */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">Additional Services</h3>
                      
                      {selectedEquipment.operator_rate && (
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="operator_needed"
                            checked={data.operator_needed}
                            onChange={(e) => setData('operator_needed', e.target.checked)}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          />
                          <label htmlFor="operator_needed" className="ml-2 block text-sm text-gray-900">
                            I need an operator ({formatCurrency(selectedEquipment.operator_rate)}/day)
                          </label>
                        </div>
                      )}

                      {selectedEquipment.transport_rate && (
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="transport_needed"
                            checked={data.transport_needed}
                            onChange={(e) => setData('transport_needed', e.target.checked)}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          />
                          <label htmlFor="transport_needed" className="ml-2 block text-sm text-gray-900">
                            I need transport/delivery ({formatCurrency(selectedEquipment.transport_rate)})
                          </label>
                        </div>
                      )}
                    </div>

                    {/* Additional Requirements */}
                    <div>
                      <label htmlFor="additional_requirements" className="block text-sm font-medium text-gray-700 mb-2">
                        Additional Requirements
                      </label>
                      <textarea
                        id="additional_requirements"
                        value={data.additional_requirements}
                        onChange={(e) => setData('additional_requirements', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Any special requirements or instructions..."
                      />
                      {errors.additional_requirements && <p className="mt-1 text-sm text-red-600">{errors.additional_requirements}</p>}
                    </div>

                    {/* Emergency Contact */}
                    <div>
                      <label htmlFor="emergency_contact" className="block text-sm font-medium text-gray-700 mb-2">
                        Emergency Contact <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        id="emergency_contact"
                        value={data.emergency_contact}
                        onChange={(e) => setData('emergency_contact', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Emergency contact number"
                        required
                      />
                      {errors.emergency_contact && <p className="mt-1 text-sm text-red-600">{errors.emergency_contact}</p>}
                    </div>

                    <div className="flex space-x-3">
                      <button
                        type="submit"
                        disabled={processing}
                        className="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white py-3 px-4 rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-colors disabled:opacity-50 font-medium"
                      >
                        {processing ? 'Submitting...' : 'Submit Request'}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          reset();
                          setSelectedEquipment(null);
                        }}
                        className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium"
                      >
                        Clear
                      </button>
                    </div>
                  </form>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Cost Calculator */}
              {selectedEquipment && data.start_date && data.end_date && (
                <div className="bg-white rounded-2xl shadow-xl p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Cost Estimate</h3>
                  <div className="space-y-3">
                    <div className="bg-indigo-50 rounded-lg p-4">
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-indigo-700">Equipment:</span>
                          <span className="font-medium text-indigo-900">{selectedEquipment.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-indigo-700">Duration:</span>
                          <span className="font-medium text-indigo-900">
                            {Math.ceil((new Date(data.end_date) - new Date(data.start_date)) / (1000 * 60 * 60 * 24)) + 1} days
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-indigo-700">Daily Rate:</span>
                          <span className="font-medium text-indigo-900">{formatCurrency(selectedEquipment.daily_rate)}</span>
                        </div>
                        {data.operator_needed && selectedEquipment.operator_rate && (
                          <div className="flex justify-between">
                            <span className="text-indigo-700">Operator:</span>
                            <span className="font-medium text-indigo-900">{formatCurrency(selectedEquipment.operator_rate)}/day</span>
                          </div>
                        )}
                        {data.transport_needed && selectedEquipment.transport_rate && (
                          <div className="flex justify-between">
                            <span className="text-indigo-700">Transport:</span>
                            <span className="font-medium text-indigo-900">{formatCurrency(selectedEquipment.transport_rate)}</span>
                          </div>
                        )}
                        <div className="flex justify-between border-t border-indigo-200 pt-2">
                          <span className="text-indigo-700 font-medium">Total Cost:</span>
                          <span className="font-bold text-indigo-900 text-lg">{formatCurrency(calculateRentalCost())}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Equipment Guidelines */}
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Equipment Guidelines</h3>
                <ul className="space-y-3 text-sm">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Inspect equipment before use</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Follow safety procedures</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Return equipment clean and in good condition</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Report any damages immediately</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Payment due before equipment pickup</span>
                  </li>
                </ul>
              </div>

              {/* Your Recent Requests */}
              {existingRequests && existingRequests.length > 0 && (
                <div className="bg-white rounded-2xl shadow-xl p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Your Recent Requests</h3>
                  <div className="space-y-3">
                    {existingRequests.slice(0, 5).map((request) => (
                      <div key={request.id} className="border border-gray-200 rounded-lg p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium text-gray-900">{request.equipment?.name}</p>
                            <p className="text-sm text-gray-600">{formatDate(request.start_date)} - {formatDate(request.end_date)}</p>
                          </div>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            request.status === 'approved' ? 'bg-green-100 text-green-800' :
                            request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            request.status === 'rejected' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {request.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MemberLayout>
  );
};

export default EquipmentRequest;
