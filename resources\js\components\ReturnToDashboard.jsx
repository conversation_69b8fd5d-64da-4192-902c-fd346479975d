import React from 'react';
import { Link } from '@inertiajs/react';

export default function ReturnToDashboard({ 
    user, 
    className = '', 
    variant = 'button', // 'button', 'link', 'floating'
    position = 'top-left' // 'top-left', 'top-right', 'bottom-left', 'bottom-right'
}) {
    // Determine dashboard route based on user role
    const getDashboardRoute = () => {
        if (user?.role?.name === 'admin') {
            return '/dashboard/admin';
        }
        return '/dashboard';
    };

    // Base styles for different variants
    const getVariantStyles = () => {
        switch (variant) {
            case 'link':
                return 'inline-flex items-center text-green-600 hover:text-green-800 font-medium transition-colors';
            case 'floating':
                return 'fixed z-50 bg-green-600 hover:bg-green-700 text-white rounded-full p-3 shadow-lg transition-all duration-300 hover:shadow-xl';
            case 'button':
            default:
                return 'inline-flex items-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors shadow-sm';
        }
    };

    // Position styles for floating variant
    const getPositionStyles = () => {
        if (variant !== 'floating') return '';
        
        switch (position) {
            case 'top-right':
                return 'top-6 right-6';
            case 'bottom-left':
                return 'bottom-6 left-6';
            case 'bottom-right':
                return 'bottom-6 right-6';
            case 'top-left':
            default:
                return 'top-6 left-6';
        }
    };

    // Icon size based on variant
    const getIconSize = () => {
        switch (variant) {
            case 'floating':
                return 'w-6 h-6';
            case 'link':
                return 'w-4 h-4';
            case 'button':
            default:
                return 'w-5 h-5';
        }
    };

    const combinedClassName = `${getVariantStyles()} ${getPositionStyles()} ${className}`;

    return (
        <Link
            href={getDashboardRoute()}
            className={combinedClassName}
            title="Return to Dashboard"
        >
            <svg 
                className={`${getIconSize()} ${variant !== 'floating' ? 'mr-2' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
            >
                <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" 
                />
            </svg>
            {variant !== 'floating' && (
                <span>
                    {user?.role?.name === 'admin' ? 'Admin Dashboard' : 'Dashboard'}
                </span>
            )}
        </Link>
    );
}

// Alternative compact version for headers
export function DashboardBreadcrumb({ user, currentPage, className = '' }) {
    const getDashboardRoute = () => {
        if (user?.role?.name === 'admin') {
            return '/dashboard/admin';
        }
        return '/dashboard';
    };

    return (
        <nav className={`flex items-center space-x-2 text-sm text-gray-600 ${className}`}>
            <Link 
                href={getDashboardRoute()}
                className="hover:text-green-600 transition-colors"
            >
                <svg className="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                {user?.role?.name === 'admin' ? 'Admin Dashboard' : 'Dashboard'}
            </Link>
            <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
            <span className="text-gray-900 font-medium">{currentPage}</span>
        </nav>
    );
}

// Quick action version for page headers
export function QuickDashboardReturn({ user, className = '' }) {
    const getDashboardRoute = () => {
        if (user?.role?.name === 'admin') {
            return '/dashboard/admin';
        }
        return '/dashboard';
    };

    return (
        <Link
            href={getDashboardRoute()}
            className={`inline-flex items-center px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors ${className}`}
        >
            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Dashboard
        </Link>
    );
}
