<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Loan;
use App\Models\LoanRepayment;
use App\Models\Farmer;
use App\Models\User;
use Carbon\Carbon;

class LoanSeeder extends Seeder
{
    public function run(): void
    {
        // Find the member user
        $memberUser = User::where('email', '<EMAIL>')->first();
        if (!$memberUser || !$memberUser->farmer) {
            $this->command->error('Member user not found or has no farmer profile.');
            return;
        }

        $memberFarmer = $memberUser->farmer;

        // Find admin user for approvals
        $adminUser = User::where('email', '<EMAIL>')->first();
        if (!$adminUser) {
            $adminUser = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('admin123'),
                'role_id' => 1, // Admin role
            ]);
        }

        // Create sample loans with different statuses
        $loans = [
            [
                'farmer_id' => $memberFarmer->id,
                'amount' => 50000.00,
                'interest_rate' => 5.0,
                'term_months' => 12,
                'purpose' => 'Purchase seeds and fertilizer for maize farming',
                'status' => 'pending',
                'application_date' => Carbon::now()->subDays(2),
            ],
            [
                'farmer_id' => $memberFarmer->id,
                'amount' => 25000.00,
                'interest_rate' => 4.5,
                'term_months' => 6,
                'purpose' => 'Irrigation system upgrade',
                'status' => 'approved',
                'application_date' => Carbon::now()->subDays(10),
                'approval_date' => Carbon::now()->subDays(8),
                'approved_by' => $adminUser->id,
            ],
            [
                'farmer_id' => $memberFarmer->id,
                'amount' => 75000.00,
                'interest_rate' => 5.5,
                'term_months' => 18,
                'purpose' => 'Livestock purchase - dairy cows',
                'status' => 'disbursed',
                'application_date' => Carbon::now()->subDays(30),
                'approval_date' => Carbon::now()->subDays(25),
                'disbursement_date' => Carbon::now()->subDays(20),
                'approved_by' => $adminUser->id,
            ],
            [
                'farmer_id' => $memberFarmer->id,
                'amount' => 15000.00,
                'interest_rate' => 6.0,
                'term_months' => 8,
                'purpose' => 'Farm equipment maintenance',
                'status' => 'rejected',
                'application_date' => Carbon::now()->subDays(15),
                'approved_by' => $adminUser->id,
                'rejection_reason' => 'Insufficient collateral provided. Please reapply with additional security.',
            ],
            [
                'farmer_id' => $memberFarmer->id,
                'amount' => 40000.00,
                'interest_rate' => 4.0,
                'term_months' => 24,
                'purpose' => 'Greenhouse construction for vegetable farming',
                'status' => 'completed',
                'application_date' => Carbon::now()->subMonths(6),
                'approval_date' => Carbon::now()->subMonths(6)->addDays(3),
                'disbursement_date' => Carbon::now()->subMonths(5),
                'approved_by' => $adminUser->id,
            ],
        ];

        foreach ($loans as $loanData) {
            $loan = Loan::firstOrCreate(
                [
                    'farmer_id' => $loanData['farmer_id'],
                    'amount' => $loanData['amount'],
                    'purpose' => $loanData['purpose']
                ],
                $loanData
            );

            // Add repayments for disbursed and completed loans
            if (in_array($loan->status, ['disbursed', 'partial_repayment', 'completed'])) {
                $this->createRepayments($loan, $adminUser->id);
            }
        }

        $this->command->info('Loan test data created successfully!');
    }

    private function createRepayments($loan, $adminUserId)
    {
        $totalAmount = $loan->amount;
        $monthlyPayment = $totalAmount / $loan->term_months;

        if ($loan->status === 'completed') {
            // Create full repayment history
            for ($i = 1; $i <= $loan->term_months; $i++) {
                LoanRepayment::firstOrCreate(
                    [
                        'loan_id' => $loan->id,
                        'payment_date' => $loan->disbursement_date->addMonths($i)
                    ],
                    [
                        'amount' => $monthlyPayment,
                        'payment_method' => $this->getRandomPaymentMethod(),
                        'receipt_number' => 'RCP-' . str_pad($loan->id * 100 + $i, 6, '0', STR_PAD_LEFT),
                        'received_by' => $adminUserId,
                        'notes' => "Monthly payment #{$i}",
                    ]
                );
            }
        } elseif ($loan->status === 'disbursed') {
            // Create partial repayments (2-3 payments)
            $paymentsCount = rand(2, 3);
            for ($i = 1; $i <= $paymentsCount; $i++) {
                LoanRepayment::firstOrCreate(
                    [
                        'loan_id' => $loan->id,
                        'payment_date' => $loan->disbursement_date->addMonths($i)
                    ],
                    [
                        'amount' => $monthlyPayment,
                        'payment_method' => $this->getRandomPaymentMethod(),
                        'receipt_number' => 'RCP-' . str_pad($loan->id * 100 + $i, 6, '0', STR_PAD_LEFT),
                        'received_by' => $adminUserId,
                        'notes' => "Monthly payment #{$i}",
                    ]
                );
            }

            // Update loan status to partial_repayment if there are payments
            if ($paymentsCount > 0) {
                $loan->update(['status' => 'partial_repayment']);
            }
        }
    }

    private function getRandomPaymentMethod()
    {
        $methods = ['cash', 'bank_transfer', 'mobile_money', 'check'];
        return $methods[array_rand($methods)];
    }
}
