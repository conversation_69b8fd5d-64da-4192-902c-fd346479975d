<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vehicle extends Model
{
    use HasFactory;

    protected $fillable = [
        'registration_number',
        'vehicle_type',
        'make',
        'model',
        'year',
        'capacity_tons',
        'fuel_consumption_per_km',
        'status',
        'driver_name',
        'driver_phone',
        'insurance_expiry',
        'license_expiry',
        'maintenance_cost_ytd',
        'total_trips',
        'total_distance_km',
        'notes',
    ];

    protected $casts = [
        'capacity_tons' => 'decimal:2',
        'fuel_consumption_per_km' => 'decimal:2',
        'maintenance_cost_ytd' => 'decimal:2',
        'total_distance_km' => 'decimal:2',
        'insurance_expiry' => 'date',
        'license_expiry' => 'date',
        'total_trips' => 'integer',
        'year' => 'integer',
    ];

    /**
     * Get transport deliveries for this vehicle
     */
    public function transportDeliveries()
    {
        return $this->hasMany(TransportDelivery::class);
    }

    /**
     * Get vehicle type display name
     */
    public function getVehicleTypeDisplayAttribute()
    {
        $types = [
            'truck' => 'Truck',
            'trailer' => 'Trailer',
            'pickup' => 'Pickup',
            'lorry' => 'Lorry',
        ];

        return $types[$this->vehicle_type] ?? ucfirst($this->vehicle_type);
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => 'bg-green-100 text-green-800',
            'maintenance' => 'bg-yellow-100 text-yellow-800',
            'retired' => 'bg-red-100 text-red-800',
        ];

        return $badges[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Check if vehicle needs maintenance
     */
    public function needsMaintenance()
    {
        // Check if insurance or license is expiring within 30 days
        $thirtyDaysFromNow = now()->addDays(30);

        return ($this->insurance_expiry && $this->insurance_expiry <= $thirtyDaysFromNow) ||
               ($this->license_expiry && $this->license_expiry <= $thirtyDaysFromNow);
    }

    /**
     * Get average fuel consumption
     */
    public function getAverageFuelConsumptionAttribute()
    {
        if ($this->total_distance_km > 0 && $this->fuel_consumption_per_km > 0) {
            return $this->total_distance_km * $this->fuel_consumption_per_km;
        }
        return 0;
    }

    /**
     * Scope for active vehicles
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for vehicles needing maintenance
     */
    public function scopeNeedsMaintenance($query)
    {
        $thirtyDaysFromNow = now()->addDays(30);

        return $query->where(function ($q) use ($thirtyDaysFromNow) {
            $q->where('insurance_expiry', '<=', $thirtyDaysFromNow)
              ->orWhere('license_expiry', '<=', $thirtyDaysFromNow);
        });
    }


}
