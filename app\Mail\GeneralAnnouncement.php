<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class GeneralAnnouncement extends Mailable
{
    use Queueable, SerializesModels;

    public $memberName;
    public $title;
    public $content;
    public $actionUrl;
    public $actionText;

    /**
     * Create a new message instance.
     */
    public function __construct($memberName, $title, $content, $actionUrl = null, $actionText = null)
    {
        $this->memberName = $memberName;
        $this->title = $title;
        $this->content = $content;
        $this->actionUrl = $actionUrl;
        $this->actionText = $actionText ?? 'View Details';
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->title . ' - Siyamphanje Cooperative',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.general-announcement',
            text: 'emails.general-announcement-text',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
