<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Meeting;
use App\Models\MeetingAttendance;
use App\Models\Notification;
use App\Models\User;
use App\Models\Farmer;
use Carbon\Carbon;

class MeetingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get admin user and farmers
        $adminUser = User::where('email', '<EMAIL>')->first();
        $farmers = Farmer::with('user')->get();

        if (!$adminUser || $farmers->isEmpty()) {
            $this->command->info('No admin user or farmers found. Please run UserSeeder first.');
            return;
        }

        // Create sample meetings
        $meetings = [
            [
                'title' => 'Monthly General Meeting',
                'description' => 'Our regular monthly meeting to discuss cooperative business, financial updates, and member concerns.',
                'meeting_date' => Carbon::now()->addDays(7)->format('Y-m-d'),
                'meeting_time' => Carbon::now()->addDays(7)->setTime(14, 0)->format('Y-m-d H:i:s'),
                'location' => 'Siyamphanje Community Hall',
                'meeting_type' => 'general',
                'status' => 'scheduled',
                'agenda' => "1. Welcome and attendance\n2. Financial report\n3. Loan applications review\n4. Equipment maintenance update\n5. New member applications\n6. Any other business",
                'max_attendees' => 50,
                'created_by' => $adminUser->id,
                'called_by' => $adminUser->id,
            ],
            [
                'title' => 'Emergency Board Meeting',
                'description' => 'Urgent board meeting to discuss the new government agricultural support program and how we can benefit.',
                'meeting_date' => Carbon::now()->addDays(3)->format('Y-m-d'),
                'meeting_time' => Carbon::now()->addDays(3)->setTime(10, 0)->format('Y-m-d H:i:s'),
                'location' => 'Cooperative Office',
                'meeting_type' => 'emergency',
                'status' => 'scheduled',
                'agenda' => "1. Government agricultural support program overview\n2. Application process and requirements\n3. Timeline and deadlines\n4. Member eligibility criteria\n5. Next steps",
                'max_attendees' => 15,
                'created_by' => $adminUser->id,
                'called_by' => $adminUser->id,
            ],
            [
                'title' => 'Farming Techniques Training',
                'description' => 'Training session on modern farming techniques, crop rotation, and sustainable agriculture practices.',
                'meeting_date' => Carbon::now()->addDays(14)->format('Y-m-d'),
                'meeting_time' => Carbon::now()->addDays(14)->setTime(9, 0)->format('Y-m-d H:i:s'),
                'location' => 'Training Center',
                'meeting_type' => 'training',
                'status' => 'scheduled',
                'agenda' => "1. Introduction to sustainable farming\n2. Crop rotation techniques\n3. Soil management\n4. Pest control methods\n5. Hands-on demonstration\n6. Q&A session",
                'max_attendees' => 30,
                'created_by' => $adminUser->id,
                'called_by' => $adminUser->id,
            ],
            [
                'title' => 'Annual General Meeting 2024',
                'description' => 'Our annual meeting to review the year\'s performance, elect new board members, and plan for the upcoming year.',
                'meeting_date' => Carbon::now()->subDays(30)->format('Y-m-d'),
                'meeting_time' => Carbon::now()->subDays(30)->setTime(13, 0)->format('Y-m-d H:i:s'),
                'location' => 'Siyamphanje Community Hall',
                'meeting_type' => 'annual',
                'status' => 'completed',
                'agenda' => "1. Chairman's report\n2. Financial statements review\n3. Auditor's report\n4. Board elections\n5. Strategic plan for 2025\n6. Member feedback",
                'max_attendees' => 100,
                'created_by' => $adminUser->id,
                'called_by' => $adminUser->id,
            ],
        ];

        foreach ($meetings as $meetingData) {
            $meeting = Meeting::create($meetingData);

            // Create attendance records for all farmers
            foreach ($farmers as $farmer) {
                $attendanceData = [
                    'meeting_id' => $meeting->id,
                    'farmer_id' => $farmer->id,
                    'attended' => false,
                    'response_status' => 'pending',
                ];

                // For past meetings, set some random attendance
                if ($meeting->status === 'completed') {
                    $attendanceData['attended'] = rand(0, 1);
                    $attendanceData['response_status'] = $attendanceData['attended'] ? 'attending' : 'not_attending';
                }

                // For upcoming meetings, set some random responses
                if ($meeting->status === 'scheduled' && rand(0, 2) > 0) {
                    $responses = ['attending', 'not_attending', 'maybe'];
                    $attendanceData['response_status'] = $responses[array_rand($responses)];
                }

                MeetingAttendance::create($attendanceData);
            }

            // Send notifications for scheduled meetings
            if ($meeting->status === 'scheduled') {
                foreach ($farmers as $farmer) {
                    if ($farmer->user) {
                        Notification::create([
                            'user_id' => $farmer->user->id,
                            'title' => 'New Meeting Scheduled: ' . $meeting->title,
                            'message' => "A new meeting has been scheduled for {$meeting->meeting_date} at {$meeting->meeting_time}. Location: {$meeting->location}. Please confirm your attendance.",
                            'type' => 'meeting',
                            'is_read' => rand(0, 3) > 0, // 75% chance of being unread
                            'data' => json_encode([
                                'meeting_id' => $meeting->id,
                                'meeting_date' => $meeting->meeting_date,
                                'meeting_time' => $meeting->meeting_time,
                                'location' => $meeting->location,
                            ]),
                        ]);
                    }
                }
            }
        }

        // Create some general notifications
        $generalNotifications = [
            [
                'title' => 'New Equipment Available',
                'message' => 'We have acquired new farming equipment including a tractor and plowing implements. Members can now request to use this equipment for their farming activities.',
                'type' => 'equipment',
            ],
            [
                'title' => 'Loan Interest Rate Update',
                'message' => 'Due to favorable economic conditions, we are pleased to announce a reduction in our loan interest rates from 12% to 10% per annum, effective immediately.',
                'type' => 'loan',
            ],
            [
                'title' => 'Harvest Season Reminder',
                'message' => 'The harvest season is approaching. Please ensure you have registered all your crops and are prepared for the upcoming harvest activities.',
                'type' => 'general',
            ],
            [
                'title' => 'Important: Member Registration Update',
                'message' => 'All members are required to update their contact information and farming details by the end of this month. Please visit the office or use the online portal.',
                'type' => 'announcement',
            ],
        ];

        foreach ($generalNotifications as $notificationData) {
            foreach ($farmers as $farmer) {
                if ($farmer->user) {
                    Notification::create([
                        'user_id' => $farmer->user->id,
                        'title' => $notificationData['title'],
                        'message' => $notificationData['message'],
                        'type' => $notificationData['type'],
                        'is_read' => rand(0, 4) > 0, // 80% chance of being unread
                        'data' => json_encode([
                            'sent_by' => 'Siyamphanje Cooperative Admin',
                            'sent_at' => now()->toISOString(),
                        ]),
                    ]);
                }
            }
        }

        $this->command->info('Sample meetings and notifications created successfully!');
    }
}
