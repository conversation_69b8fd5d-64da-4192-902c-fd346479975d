<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Farmer;
use App\Models\SavingsAccount;
use App\Models\Loan;
use App\Models\Contribution;
use App\Models\MeetingAttendance;
use App\Models\FarmerCrop;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class ReportController extends Controller
{
    public function index()
    {
        // Get comprehensive statistics for reports
        $stats = [
            'members' => [
                'total' => Farmer::count(),
                'active' => Farmer::where('status', 'active')->count(),
                'inactive' => Farmer::where('status', 'inactive')->count(),
                'new_this_month' => Farmer::where('created_at', '>=', Carbon::now()->startOfMonth())->count(),
            ],
            'financial' => [
                'total_savings' => SavingsAccount::sum('balance'),
                'total_loans' => Loan::sum('amount'),
                'total_contributions' => Contribution::sum('amount'),
                'outstanding_loans' => Loan::whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->sum('amount'),
                'completed_loans' => Loan::where('status', 'completed')->sum('amount'),
            ],
            'activities' => [
                'pending_loans' => Loan::where('status', 'pending')->count(),
                'pending_equipment_requests' => \App\Models\EquipmentRequest::where('status', 'pending')->count(),
                'recent_contributions' => Contribution::where('contribution_date', '>=', Carbon::now()->startOfMonth())->count(),
            ]
        ];

        // Get recent report data for quick insights
        $recentData = [
            'monthly_contributions' => $this->getMonthlyContributions(),
            'loan_performance' => $this->getLoanPerformance(),
            'member_growth' => $this->getMemberGrowth(),
            'financial_trends' => $this->getFinancialTrends(),
        ];

        return Inertia::render('Admin/Reports', [
            'stats' => $stats,
            'recentData' => $recentData,
            'auth' => [
                'user' => auth()->user()
            ]
        ]);
    }

    public function memberIndex()
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile associated with this user.']);
        }

        // Fetch reports relevant to the member, e.g., their loans, savings, contributions
        $reports = [
            'financial' => [
                'title' => 'My Financial Summary',
                'data' => [
                    'totalSavings' => $farmer->savingsAccounts()->sum('balance'),
                    'totalLoans' => $farmer->loans()->sum('amount'),
                    'totalContributions' => $farmer->contributions()->sum('amount'),
                ],
            ],
            'loans' => [
                'title' => 'My Loans',
                'data' => $farmer->loans()->with('repayments')->get(),
            ],
            'savings' => [
                'title' => 'My Savings',
                'data' => $farmer->savingsAccounts()->get(),
            ],
            'contributions' => [
                'title' => 'My Contributions',
                'data' => $farmer->contributions()->get(),
            ],
        ];

        // Get financial summary
        $financialSummary = [
            'total_savings' => $farmer->savingsAccounts()->sum('balance'),
            'total_contributions' => $farmer->contributions()->sum('amount'),
            'monthly_deposits' => 0, // You can calculate this based on your transaction model
            'monthly_withdrawals' => 0,
            'monthly_contributions' => $farmer->contributions()
                ->whereMonth('contribution_date', now()->month)
                ->sum('amount'),
            'savings_accounts' => $farmer->savingsAccounts()->get()->map(function ($account) {
                return [
                    'id' => $account->id,
                    'account_type' => $account->account_type ?? 'Savings',
                    'account_number' => $account->account_number,
                    'balance' => $account->balance,
                ];
            }),
        ];

        // Get recent transactions (placeholder - implement based on your transaction model)
        $recentTransactions = [];

        // Get loan summary
        $loans = $farmer->loans()->get();
        $loanSummary = [
            'total_active_loans' => $loans->whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->sum('amount'),
            'loans' => $loans->map(function ($loan) {
                return [
                    'id' => $loan->id,
                    'purpose' => $loan->purpose,
                    'amount' => $loan->amount,
                    'interest_rate' => $loan->interest_rate ?? 5,
                    'term_months' => $loan->term_months ?? 12,
                    'status' => $loan->status,
                    'monthly_payment' => $loan->monthly_payment ?? ($loan->amount / ($loan->term_months ?? 12)),
                    'outstanding_balance' => $loan->amount, // Calculate based on repayments
                    'next_payment_date' => now()->addMonth()->format('Y-m-d'),
                ];
            }),
        ];

        // Get crop summary
        $crops = FarmerCrop::where('farmer_id', $farmer->id)->get();
        $cropSummary = [
            'total_crops' => $crops->count(),
            'total_area' => $crops->sum('area_planted'),
            'crops' => $crops->map(function ($crop) {
                return [
                    'id' => $crop->id,
                    'crop_name' => $crop->crop_name,
                    'crop_type' => $crop->crop_type,
                    'variety' => $crop->variety,
                    'area_planted' => $crop->area_planted,
                    'planting_date' => $crop->planting_date,
                    'expected_harvest_date' => $crop->expected_harvest_date,
                    'planting_method' => $crop->planting_method,
                    'irrigation_method' => $crop->irrigation_method,
                    'estimated_value' => $crop->estimated_value,
                    'location_description' => $crop->location_description,
                ];
            }),
        ];

        return Inertia::render('Member/Reports', [
            'auth' => ['user' => $user],
            'financialSummary' => $financialSummary,
            'recentTransactions' => $recentTransactions,
            'loanSummary' => $loanSummary,
            'cropSummary' => $cropSummary,
            'auth' => [
                'user' => $user
            ],
            'memberReports' => $reports,
        ]);
    }

    public function generateReport(Request $request)
    {
        $request->validate([
            'reportType' => 'required|string',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'format' => 'nullable|string|in:pdf,excel,csv'
        ]);

        $reportType = $request->input('reportType');
        $startDate = $request->input('startDate') ? Carbon::parse($request->input('startDate')) : null;
        $endDate = $request->input('endDate') ? Carbon::parse($request->input('endDate')) : null;
        $format = $request->input('format', 'pdf');

        try {
            $reportData = $this->getReportData($reportType, $startDate, $endDate);

            // Add format and generation info
            $reportData['format'] = $format;
            $reportData['reportType'] = $reportType;
            $reportData['generatedAt'] = Carbon::now()->toDateTimeString();
            $reportData['message'] = "Report generated successfully! Data includes {$reportData['title']} for the specified period.";

            // If PDF format is requested, generate and download PDF
            if ($format === 'pdf') {
                return $this->generatePDF($reportData);
            }

            // Get current stats for the page
            $stats = [
                'members' => [
                    'total' => Farmer::count(),
                    'active' => Farmer::where('status', 'active')->count(),
                    'inactive' => Farmer::where('status', 'inactive')->count(),
                    'new_this_month' => Farmer::where('created_at', '>=', Carbon::now()->startOfMonth())->count(),
                ],
                'financial' => [
                    'total_savings' => SavingsAccount::sum('balance'),
                    'total_loans' => Loan::sum('amount'),
                    'total_contributions' => Contribution::sum('amount'),
                    'outstanding_loans' => Loan::whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->sum('amount'),
                    'completed_loans' => Loan::where('status', 'completed')->sum('amount'),
                ],
                'activities' => [
                    'pending_loans' => Loan::where('status', 'pending')->count(),
                    'pending_equipment_requests' => \App\Models\EquipmentRequest::where('status', 'pending')->count(),
                    'recent_contributions' => Contribution::where('contribution_date', '>=', Carbon::now()->startOfMonth())->count(),
                ]
            ];

            $recentData = [
                'monthly_contributions' => $this->getMonthlyContributions(),
                'loan_performance' => $this->getLoanPerformance(),
                'member_growth' => $this->getMemberGrowth(),
                'financial_trends' => $this->getFinancialTrends(),
            ];

            return Inertia::render('Admin/Reports', [
                'stats' => $stats,
                'recentData' => $recentData,
                'reportData' => $reportData,
                'auth' => [
                    'user' => auth()->user()
                ]
            ]);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to generate report: ' . $e->getMessage()]);
        }
    }

    private function getReportData(string $reportType, ?Carbon $startDate, ?Carbon $endDate): array
    {
        $reportData = [];

        switch ($reportType) {
            case 'financial':
                // Get all savings (no date filter for total savings)
                $totalSavings = SavingsAccount::sum('balance') ?? 0;

                // Get loans for the period (use application_date)
                $totalLoans = Loan::when($startDate, function ($query) use ($startDate) {
                    $query->where('application_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('application_date', '<=', $endDate);
                })->sum('amount') ?? 0;

                // Get contributions for the period (use contribution_date)
                $totalContributions = Contribution::when($startDate, function ($query) use ($startDate) {
                    $query->where('contribution_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('contribution_date', '<=', $endDate);
                })->sum('amount') ?? 0;

                // Calculate loan repayments
                $totalRepayments = \App\Models\LoanRepayment::when($startDate, function ($query) use ($startDate) {
                    $query->where('payment_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('payment_date', '<=', $endDate);
                })->sum('amount') ?? 0;

                // Outstanding loans (all active loans minus repayments)
                $outstandingLoans = Loan::whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->sum('amount') ?? 0;

                // Equipment value
                $equipmentValue = \App\Models\FarmEquipment::sum('value') ?? 0;

                // Total expenses (operational costs)
                $totalExpenses = $totalLoans * 0.05; // 5% operational cost estimate

                // Net balance calculation
                $totalAssets = $totalSavings + $totalContributions + $equipmentValue;
                $totalLiabilities = $outstandingLoans + $totalExpenses;
                $netBalance = $totalAssets - $totalLiabilities;

                $reportData = [
                    'title' => 'Financial Summary Report',
                    'dateGenerated' => Carbon::now()->toDateString(),
                    'period' => ($startDate && $endDate) ? "{$startDate->toDateString()} to {$endDate->toDateString()}" : 'All time',
                    'data' => [
                        'totalSavings' => $totalSavings,
                        'totalLoans' => $totalLoans,
                        'totalContributions' => $totalContributions,
                        'totalRepayments' => $totalRepayments,
                        'outstandingLoans' => $outstandingLoans,
                        'equipmentValue' => $equipmentValue,
                        'totalExpenses' => $totalExpenses,
                        'totalAssets' => $totalAssets,
                        'totalLiabilities' => $totalLiabilities,
                        'netBalance' => $netBalance,
                    ],
                ];
                break;
            case 'members':
                $totalMembers = Farmer::count();
                $activeMembers = Farmer::where('status', 'active')->count();
                $inactiveMembers = Farmer::where('status', 'inactive')->count();
                $newMembersThisPeriod = Farmer::when($startDate, function ($query) use ($startDate) {
                    $query->where('membership_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('membership_date', '<=', $endDate);
                })->count();

                // Get detailed member lists
                $activeMembersList = Farmer::with('user')
                    ->where('status', 'active')
                    ->get()
                    ->map(function ($farmer) {
                        return [
                            'name' => $farmer->user->name ?? 'Unknown',
                            'membership_date' => $farmer->membership_date ? Carbon::parse($farmer->membership_date)->format('Y-m-d') : 'N/A',
                            'land_size' => $farmer->land_size ?? 0,
                            'farm_location' => $farmer->farm_location ?? 'Not specified',
                        ];
                    });

                $inactiveMembersList = Farmer::with('user')
                    ->where('status', 'inactive')
                    ->get()
                    ->map(function ($farmer) {
                        return [
                            'name' => $farmer->user->name ?? 'Unknown',
                            'membership_date' => $farmer->membership_date ? Carbon::parse($farmer->membership_date)->format('Y-m-d') : 'N/A',
                            'land_size' => $farmer->land_size ?? 0,
                        ];
                    });

                $newMembersList = Farmer::with('user')
                    ->when($startDate, function ($query) use ($startDate) {
                        $query->where('membership_date', '>=', $startDate);
                    })->when($endDate, function ($query) use ($endDate) {
                        $query->where('membership_date', '<=', $endDate);
                    })
                    ->get()
                    ->map(function ($farmer) {
                        return [
                            'name' => $farmer->user->name ?? 'Unknown',
                            'membership_date' => $farmer->membership_date ? Carbon::parse($farmer->membership_date)->format('Y-m-d') : 'N/A',
                            'status' => $farmer->status,
                        ];
                    });

                $reportData = [
                    'title' => 'Members Report',
                    'dateGenerated' => Carbon::now()->toDateString(),
                    'period' => ($startDate && $endDate) ? "{$startDate->toDateString()} to {$endDate->toDateString()}" : 'All time',
                    'totalMembers' => $totalMembers,
                    'activeMembers' => $activeMembers,
                    'inactiveMembers' => $inactiveMembers,
                    'newMembersThisPeriod' => $newMembersThisPeriod,
                    'data' => [
                        'totalMembers' => $totalMembers,
                        'activeMembers' => $activeMembers,
                        'inactiveMembers' => $inactiveMembers,
                        'newMembersThisPeriod' => $newMembersThisPeriod,
                        'activeMembersList' => $activeMembersList,
                        'inactiveMembersList' => $inactiveMembersList,
                        'newMembersList' => $newMembersList,
                    ],
                ];
                break;
            case 'loans':
                // Get loans for the period using application_date
                $totalLoans = Loan::when($startDate, function ($query) use ($startDate) {
                    $query->where('application_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('application_date', '<=', $endDate);
                })->sum('amount') ?? 0;

                $totalRepayments = \App\Models\LoanRepayment::when($startDate, function ($query) use ($startDate) {
                    $query->where('payment_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('payment_date', '<=', $endDate);
                })->sum('amount') ?? 0;

                // Calculate outstanding balance properly
                $allActiveLoans = Loan::whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->sum('amount') ?? 0;
                $allRepayments = \App\Models\LoanRepayment::sum('amount') ?? 0;
                $outstandingBalance = max(0, $allActiveLoans - $allRepayments);

                $averageLoanSize = Loan::when($startDate, function ($query) use ($startDate) {
                    $query->where('application_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('application_date', '<=', $endDate);
                })->avg('amount') ?? 0;

                // Get loan statistics by status
                $loansByStatus = Loan::when($startDate, function ($query) use ($startDate) {
                    $query->where('application_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('application_date', '<=', $endDate);
                })
                ->selectRaw('status, COUNT(*) as count, SUM(amount) as total_amount')
                ->groupBy('status')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->status => [
                        'count' => $item->count,
                        'total_amount' => $item->total_amount
                    ]];
                });

                // Get detailed loan list with borrower information
                $loansList = Loan::with(['farmer.user'])
                    ->when($startDate, function ($query) use ($startDate) {
                        $query->where('application_date', '>=', $startDate);
                    })->when($endDate, function ($query) use ($endDate) {
                        $query->where('application_date', '<=', $endDate);
                    })
                    ->orderBy('application_date', 'desc')
                    ->get()
                    ->map(function ($loan) {
                        $totalRepaid = $loan->repayments()->sum('amount') ?? 0;
                        $remainingBalance = max(0, $loan->amount - $totalRepaid);

                        return [
                            'borrower_name' => $loan->farmer->user->name ?? 'Unknown',
                            'amount' => $loan->amount,
                            'purpose' => $loan->purpose,
                            'status' => $loan->status,
                            'application_date' => $loan->application_date ? Carbon::parse($loan->application_date)->format('Y-m-d') : 'N/A',
                            'interest_rate' => $loan->interest_rate ?? 0,
                            'term_months' => $loan->term_months ?? 0,
                            'total_repaid' => $totalRepaid,
                            'remaining_balance' => $remainingBalance,
                            'repayment_status' => $remainingBalance <= 0 ? 'Fully Paid' : ($totalRepaid > 0 ? 'Partial Payment' : 'No Payment'),
                        ];
                    });

                // Calculate repayment performance
                $totalLoansCount = Loan::when($startDate, function ($query) use ($startDate) {
                    $query->where('application_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('application_date', '<=', $endDate);
                })->count();

                $fullyPaidLoans = $loansList->where('repayment_status', 'Fully Paid')->count();
                $partialPaymentLoans = $loansList->where('repayment_status', 'Partial Payment')->count();
                $noPaymentLoans = $loansList->where('repayment_status', 'No Payment')->count();

                $reportData = [
                    'title' => 'Loan Performance Report',
                    'dateGenerated' => Carbon::now()->toDateString(),
                    'period' => ($startDate && $endDate) ? "{$startDate->toDateString()} to {$endDate->toDateString()}" : 'All time',
                    'data' => [
                        'totalLoans' => $totalLoans,
                        'totalRepayments' => $totalRepayments,
                        'outstandingBalance' => $outstandingBalance,
                        'averageLoanSize' => $averageLoanSize,
                        'totalLoansCount' => $totalLoansCount,
                        'fullyPaidLoans' => $fullyPaidLoans,
                        'partialPaymentLoans' => $partialPaymentLoans,
                        'noPaymentLoans' => $noPaymentLoans,
                        'loansByStatus' => $loansByStatus,
                        'loansList' => $loansList,
                        'repaymentRate' => $totalLoansCount > 0 ? round(($fullyPaidLoans / $totalLoansCount) * 100, 1) : 0,
                    ],
                ];
                break;
            case 'contributions':
                $totalContributions = Contribution::when($startDate, function ($query) use ($startDate) {
                    $query->where('contribution_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('contribution_date', '<=', $endDate);
                })->sum('amount') ?? 0;

                $numberOfContributors = Contribution::when($startDate, function ($query) use ($startDate) {
                    $query->where('contribution_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('contribution_date', '<=', $endDate);
                })->distinct('farmer_id')->count();

                $averageContribution = $numberOfContributors > 0 ? $totalContributions / $numberOfContributors : 0;

                // Get detailed contributor information
                $contributorsList = Contribution::with(['farmer.user'])
                    ->when($startDate, function ($query) use ($startDate) {
                        $query->where('contribution_date', '>=', $startDate);
                    })->when($endDate, function ($query) use ($endDate) {
                        $query->where('contribution_date', '<=', $endDate);
                    })
                    ->selectRaw('farmer_id, SUM(amount) as total_amount, COUNT(*) as contribution_count, MAX(contribution_date) as last_contribution')
                    ->groupBy('farmer_id')
                    ->orderBy('total_amount', 'desc')
                    ->get()
                    ->map(function ($contribution) {
                        return [
                            'name' => $contribution->farmer->user->name ?? 'Unknown',
                            'total_amount' => $contribution->total_amount,
                            'contribution_count' => $contribution->contribution_count,
                            'last_contribution' => $contribution->last_contribution ? Carbon::parse($contribution->last_contribution)->format('Y-m-d') : 'N/A',
                        ];
                    });

                // Get contribution breakdown by type
                $contributionsByType = Contribution::when($startDate, function ($query) use ($startDate) {
                    $query->where('contribution_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('contribution_date', '<=', $endDate);
                })
                ->selectRaw('type, SUM(amount) as total_amount, COUNT(*) as count')
                ->groupBy('type')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->type => [
                        'total_amount' => $item->total_amount,
                        'count' => $item->count
                    ]];
                });

                $reportData = [
                    'title' => 'Contributions Report',
                    'dateGenerated' => Carbon::now()->toDateString(),
                    'period' => ($startDate && $endDate) ? "{$startDate->toDateString()} to {$endDate->toDateString()}" : 'All time',
                    'data' => [
                        'totalContributions' => $totalContributions,
                        'numberOfContributors' => $numberOfContributors,
                        'averageContribution' => $averageContribution,
                        'contributorsList' => $contributorsList,
                        'contributionsByType' => $contributionsByType,
                    ],
                ];
                break;
            case 'equipment':
                $totalEquipment = \App\Models\FarmEquipment::count() ?? 0;
                $availableEquipment = \App\Models\FarmEquipment::where('is_available', true)->count() ?? 0;
                $totalEquipmentValue = \App\Models\FarmEquipment::sum('value') ?? 0;

                // Get rental revenue for the period
                $rentalRevenue = \App\Models\EquipmentRequest::where('status', 'completed')
                    ->where('payment_status', 'paid')
                    ->when($startDate, function ($query) use ($startDate) {
                        $query->where('start_date', '>=', $startDate);
                    })->when($endDate, function ($query) use ($endDate) {
                        $query->where('end_date', '<=', $endDate);
                    })->sum('total_rental_cost') ?? 0;

                // Get equipment requests statistics
                $totalRequests = \App\Models\EquipmentRequest::when($startDate, function ($query) use ($startDate) {
                    $query->where('request_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('request_date', '<=', $endDate);
                })->count() ?? 0;

                $requestsByStatus = \App\Models\EquipmentRequest::when($startDate, function ($query) use ($startDate) {
                    $query->where('request_date', '>=', $startDate);
                })->when($endDate, function ($query) use ($endDate) {
                    $query->where('request_date', '<=', $endDate);
                })
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->status => $item->count];
                });

                // Get detailed equipment list with usage statistics
                $equipmentList = \App\Models\FarmEquipment::withCount(['equipmentRequests as total_requests'])
                    ->withCount(['equipmentRequests as completed_requests' => function ($query) {
                        $query->where('status', 'completed');
                    }])
                    ->get()
                    ->map(function ($equipment) use ($startDate, $endDate) {
                        $periodRequests = $equipment->equipmentRequests()
                            ->when($startDate, function ($query) use ($startDate) {
                                $query->where('request_date', '>=', $startDate);
                            })->when($endDate, function ($query) use ($endDate) {
                                $query->where('request_date', '<=', $endDate);
                            })->count();

                        $periodRevenue = $equipment->equipmentRequests()
                            ->where('status', 'completed')
                            ->where('payment_status', 'paid')
                            ->when($startDate, function ($query) use ($startDate) {
                                $query->where('start_date', '>=', $startDate);
                            })->when($endDate, function ($query) use ($endDate) {
                                $query->where('end_date', '<=', $endDate);
                            })->sum('total_rental_cost') ?? 0;

                        return [
                            'name' => $equipment->name,
                            'type' => $equipment->type,
                            'value' => $equipment->value ?? 0,
                            'rental_cost_per_day' => $equipment->rental_cost_per_day ?? 0,
                            'is_available' => $equipment->is_available,
                            'total_requests' => $equipment->total_requests ?? 0,
                            'completed_requests' => $equipment->completed_requests ?? 0,
                            'period_requests' => $periodRequests,
                            'period_revenue' => $periodRevenue,
                            'utilization_rate' => $equipment->total_requests > 0 ? round(($equipment->completed_requests / $equipment->total_requests) * 100, 1) : 0,
                        ];
                    });

                // Get equipment requests with member details
                $equipmentRequestsList = \App\Models\EquipmentRequest::with(['farmer.user', 'equipment'])
                    ->when($startDate, function ($query) use ($startDate) {
                        $query->where('request_date', '>=', $startDate);
                    })->when($endDate, function ($query) use ($endDate) {
                        $query->where('request_date', '<=', $endDate);
                    })
                    ->orderBy('request_date', 'desc')
                    ->get()
                    ->map(function ($request) {
                        $duration = $request->start_date && $request->end_date ?
                            Carbon::parse($request->start_date)->diffInDays(Carbon::parse($request->end_date)) + 1 : 0;

                        return [
                            'member_name' => $request->farmer->user->name ?? 'Unknown',
                            'equipment_name' => $request->equipment->name ?? 'Unknown Equipment',
                            'purpose' => $request->purpose,
                            'request_date' => $request->request_date ? Carbon::parse($request->request_date)->format('Y-m-d') : 'N/A',
                            'start_date' => $request->start_date ? Carbon::parse($request->start_date)->format('Y-m-d') : 'N/A',
                            'end_date' => $request->end_date ? Carbon::parse($request->end_date)->format('Y-m-d') : 'N/A',
                            'duration_days' => $duration,
                            'rental_cost' => $request->total_rental_cost ?? 0,
                            'status' => $request->status,
                            'payment_status' => $request->payment_status,
                        ];
                    });

                // Calculate utilization metrics
                $averageUtilization = $equipmentList->avg('utilization_rate') ?? 0;
                $mostRequestedEquipment = $equipmentList->sortByDesc('total_requests')->first();
                $highestRevenueEquipment = $equipmentList->sortByDesc('period_revenue')->first();

                $reportData = [
                    'title' => 'Equipment Usage Report',
                    'dateGenerated' => Carbon::now()->toDateString(),
                    'period' => ($startDate && $endDate) ? "{$startDate->toDateString()} to {$endDate->toDateString()}" : 'All time',
                    'data' => [
                        'totalEquipment' => $totalEquipment,
                        'availableEquipment' => $availableEquipment,
                        'totalEquipmentValue' => $totalEquipmentValue,
                        'rentalRevenue' => $rentalRevenue,
                        'totalRequests' => $totalRequests,
                        'requestsByStatus' => $requestsByStatus,
                        'equipmentList' => $equipmentList,
                        'equipmentRequestsList' => $equipmentRequestsList,
                        'averageUtilization' => $averageUtilization,
                        'mostRequestedEquipment' => $mostRequestedEquipment,
                        'highestRevenueEquipment' => $highestRevenueEquipment,
                    ],
                ];
                break;
            default:
                $reportData = [
                    'title' => 'Unknown Report Type',
                    'dateGenerated' => Carbon::now()->toDateString(),
                    'period' => 'N/A',
                    'data' => [],
                ];
                break;
        }

        return $reportData;
    }

    private function generatePDF($reportData)
    {
        // Generate HTML content for PDF
        $html = $this->generateReportHTML($reportData);

        // Create PDF using DomPDF
        $pdf = Pdf::loadHTML($html);
        $pdf->setPaper('A4', 'portrait');

        // Generate filename
        $filename = str_replace(' ', '_', $reportData['title']) . '_' . date('Y-m-d') . '.pdf';

        // Return PDF download
        return $pdf->download($filename);
    }

    private function generateReportHTML($reportData)
    {
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>' . $reportData['title'] . '</title>
    <style>
        @page {
            margin: 20mm;
            size: A4;
        }
        body {
            font-family: DejaVu Sans, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            color: #333;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            background: #059669;
            color: white;
            padding: 25px;
            text-align: center;
            margin-bottom: 25px;
            border-radius: 5px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .report-info {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #059669;
        }
        .report-info h2 {
            margin: 0 0 15px 0;
            color: #1f2937;
            font-size: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #6b7280;
        }
        .content {
            margin-bottom: 30px;
        }
        .section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 25px;
            overflow: hidden;
        }
        .section-header {
            background: #f9fafb;
            padding: 15px 20px;
            border-bottom: 1px solid #e5e7eb;
            font-weight: bold;
            color: #1f2937;
        }
        .section-content {
            padding: 20px;
        }
        .metrics-grid {
            display: table;
            width: 100%;
            border-collapse: separate;
            border-spacing: 15px;
        }
        .metric-card {
            display: table-cell;
            width: 33.33%;
            vertical-align: top;
        }
        .metric-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin-bottom: 10px;
        }
        .metric-card.assets { border-color: #10b981; }
        .metric-card.liabilities { border-color: #8b5cf6; }
        .metric-card.net-positive { border-color: #10b981; }
        .metric-card.net-negative { border-color: #ef4444; }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .metric-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        .metric-description {
            font-size: 12px;
            color: #9ca3af;
        }
        .assets .metric-value { color: #059669; }
        .liabilities .metric-value { color: #7c3aed; }
        .net-positive .metric-value { color: #059669; }
        .net-negative .metric-value { color: #dc2626; }
        .summary-box {
            background: linear-gradient(135deg, #dbeafe, #e0e7ff);
            border: 1px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        .summary-title {
            font-size: 18px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 15px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .summary-item {
            text-align: center;
        }
        .summary-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .summary-label {
            font-size: 14px;
            color: #6b7280;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 11px;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        .data-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        @media print {
            body { margin: 0; }
            .header { background: #059669 !important; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Siyamphanje Cooperative</h1>
        <p>Agricultural Cooperative Society</p>
    </div>

    <div class="report-info">
        <h2>' . $reportData['title'] . '</h2>
        <div class="info-grid">
            <div class="info-item">📅 Period: ' . $reportData['period'] . '</div>
            <div class="info-item">📊 Report Type: ' . strtoupper($reportData['reportType']) . '</div>
            <div class="info-item">🕒 Generated: ' . $reportData['dateGenerated'] . '</div>
        </div>
    </div>

    <div class="content">';

        // Add content based on report type
        if ($reportData['reportType'] === 'financial') {
            $totalAssets = ($reportData['data']['totalSavings'] ?? 0) + ($reportData['data']['totalContributions'] ?? 0);
            $totalLiabilities = ($reportData['data']['totalLoans'] ?? 0) + ($reportData['data']['totalExpenses'] ?? 0);
            $netBalance = $reportData['data']['netBalance'] ?? 0;

            $html .= '
        <div class="summary-box">
            <div class="summary-title">Executive Summary</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value" style="color: ' . ($netBalance >= 0 ? '#059669' : '#dc2626') . ';">
                        ' . ($netBalance >= 0 ? 'POSITIVE' : 'NEGATIVE') . '
                    </div>
                    <div class="summary-label">Financial Health Status</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" style="color: #1e40af;">
                        K' . number_format($totalAssets, 2) . '
                    </div>
                    <div class="summary-label">Total Assets</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">Financial Breakdown</div>
            <div class="section-content">
                <div class="metrics-grid">
                    <div class="metric-card assets">
                        <div class="metric-label">ASSETS</div>
                        <div class="metric-value">K' . number_format($totalAssets, 2) . '</div>
                        <div class="metric-description">
                            Savings: K' . number_format($reportData['data']['totalSavings'] ?? 0, 2) . '<br>
                            Contributions: K' . number_format($reportData['data']['totalContributions'] ?? 0, 2) . '
                        </div>
                    </div>
                    <div class="metric-card liabilities">
                        <div class="metric-label">LIABILITIES</div>
                        <div class="metric-value">K' . number_format($totalLiabilities, 2) . '</div>
                        <div class="metric-description">
                            Loans: K' . number_format($reportData['data']['totalLoans'] ?? 0, 2) . '<br>
                            Expenses: K' . number_format($reportData['data']['totalExpenses'] ?? 0, 2) . '
                        </div>
                    </div>
                    <div class="metric-card ' . ($netBalance >= 0 ? 'net-positive' : 'net-negative') . '">
                        <div class="metric-label">NET POSITION</div>
                        <div class="metric-value">K' . number_format($netBalance, 2) . '</div>
                        <div class="metric-description">
                            ' . ($netBalance >= 0 ? 'Healthy Financial Position' : 'Requires Attention') . '
                        </div>
                    </div>
                </div>
            </div>
        </div>';
        } elseif ($reportData['reportType'] === 'members') {
            $activeRate = $reportData['totalMembers'] > 0 ? round(($reportData['activeMembers'] / $reportData['totalMembers']) * 100) : 0;

            $html .= '
        <div class="summary-box">
            <div class="summary-title">Membership Overview</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value" style="color: #1e40af;">' . ($reportData['totalMembers'] ?? 0) . '</div>
                    <div class="summary-label">Total Members</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" style="color: #059669;">' . $activeRate . '%</div>
                    <div class="summary-label">Active Rate</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">Member Statistics</div>
            <div class="section-content">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Total Members</div>
                        <div class="metric-value" style="color: #1e40af;">' . ($reportData['totalMembers'] ?? 0) . '</div>
                        <div class="metric-description">All registered members</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Active Members</div>
                        <div class="metric-value" style="color: #059669;">' . ($reportData['activeMembers'] ?? 0) . '</div>
                        <div class="metric-description">Currently participating</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Inactive Members</div>
                        <div class="metric-value" style="color: #dc2626;">' . ($reportData['inactiveMembers'] ?? 0) . '</div>
                        <div class="metric-description">Not currently active</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">New This Period</div>
                        <div class="metric-value" style="color: #7c3aed;">' . ($reportData['newMembersThisPeriod'] ?? 0) . '</div>
                        <div class="metric-description">Recent additions</div>
                    </div>
                </div>
            </div>
        </div>';

            // Add member lists if available
            if (!empty($reportData['data']['activeMembersList'])) {
                $html .= '
        <div class="section">
            <div class="section-header">Active Members List</div>
            <div class="section-content">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Membership Date</th>
                            <th>Land Size (acres)</th>
                            <th>Farm Location</th>
                        </tr>
                    </thead>
                    <tbody>';

                foreach ($reportData['data']['activeMembersList'] as $member) {
                    $html .= '
                        <tr>
                            <td>' . htmlspecialchars($member['name']) . '</td>
                            <td>' . htmlspecialchars($member['membership_date']) . '</td>
                            <td>' . htmlspecialchars($member['land_size']) . '</td>
                            <td>' . htmlspecialchars($member['farm_location']) . '</td>
                        </tr>';
                }

                $html .= '
                    </tbody>
                </table>
            </div>
        </div>';
            }

            if (!empty($reportData['data']['newMembersList']) && count($reportData['data']['newMembersList']) > 0) {
                $html .= '
        <div class="section">
            <div class="section-header">New Members This Period</div>
            <div class="section-content">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Membership Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>';

                foreach ($reportData['data']['newMembersList'] as $member) {
                    $html .= '
                        <tr>
                            <td>' . htmlspecialchars($member['name']) . '</td>
                            <td>' . htmlspecialchars($member['membership_date']) . '</td>
                            <td>' . htmlspecialchars(ucfirst($member['status'])) . '</td>
                        </tr>';
                }

                $html .= '
                    </tbody>
                </table>
            </div>
        </div>';
            }
        } elseif ($reportData['reportType'] === 'contributions') {
            $html .= '
        <div class="summary-box">
            <div class="summary-title">Contributions Overview</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value" style="color: #1e40af;">K' . number_format($reportData['data']['totalContributions'] ?? 0, 2) . '</div>
                    <div class="summary-label">Total Contributions</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" style="color: #059669;">' . ($reportData['data']['numberOfContributors'] ?? 0) . '</div>
                    <div class="summary-label">Contributors</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">Contribution Statistics</div>
            <div class="section-content">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Total Contributions</div>
                        <div class="metric-value" style="color: #059669;">K' . number_format($reportData['data']['totalContributions'] ?? 0, 2) . '</div>
                        <div class="metric-description">All contributions in period</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Number of Contributors</div>
                        <div class="metric-value" style="color: #1e40af;">' . ($reportData['data']['numberOfContributors'] ?? 0) . '</div>
                        <div class="metric-description">Active contributors</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Average Contribution</div>
                        <div class="metric-value" style="color: #7c3aed;">K' . number_format($reportData['data']['averageContribution'] ?? 0, 2) . '</div>
                        <div class="metric-description">Per contributor</div>
                    </div>
                </div>
            </div>
        </div>';

            // Add contributors list if available
            if (!empty($reportData['data']['contributorsList'])) {
                $html .= '
        <div class="section">
            <div class="section-header">Contributors List</div>
            <div class="section-content">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Total Amount</th>
                            <th>Number of Contributions</th>
                            <th>Last Contribution</th>
                        </tr>
                    </thead>
                    <tbody>';

                foreach ($reportData['data']['contributorsList'] as $contributor) {
                    $html .= '
                        <tr>
                            <td>' . htmlspecialchars($contributor['name']) . '</td>
                            <td>K' . number_format($contributor['total_amount'], 2) . '</td>
                            <td>' . $contributor['contribution_count'] . '</td>
                            <td>' . htmlspecialchars($contributor['last_contribution']) . '</td>
                        </tr>';
                }

                $html .= '
                    </tbody>
                </table>
            </div>
        </div>';
            }
        } elseif ($reportData['reportType'] === 'loans') {
            $html .= '
        <div class="summary-box">
            <div class="summary-title">Loan Portfolio Overview</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value" style="color: #1e40af;">K' . number_format($reportData['data']['totalLoans'] ?? 0, 2) . '</div>
                    <div class="summary-label">Total Disbursed</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" style="color: #059669;">' . ($reportData['data']['repaymentRate'] ?? 0) . '%</div>
                    <div class="summary-label">Repayment Rate</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">Loan Performance Metrics</div>
            <div class="section-content">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Total Loans</div>
                        <div class="metric-value" style="color: #1e40af;">' . ($reportData['data']['totalLoansCount'] ?? 0) . '</div>
                        <div class="metric-description">Loan applications</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Total Disbursed</div>
                        <div class="metric-value" style="color: #059669;">K' . number_format($reportData['data']['totalLoans'] ?? 0, 2) . '</div>
                        <div class="metric-description">Amount disbursed</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Total Repayments</div>
                        <div class="metric-value" style="color: #7c3aed;">K' . number_format($reportData['data']['totalRepayments'] ?? 0, 2) . '</div>
                        <div class="metric-description">Payments received</div>
                    </div>
                </div>
            </div>
        </div>';

            // Add loans list if available
            if (!empty($reportData['data']['loansList'])) {
                $html .= '
        <div class="section">
            <div class="section-header">Loan Details</div>
            <div class="section-content">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Borrower</th>
                            <th>Amount</th>
                            <th>Purpose</th>
                            <th>Status</th>
                            <th>Application Date</th>
                            <th>Repaid</th>
                            <th>Balance</th>
                        </tr>
                    </thead>
                    <tbody>';

                foreach ($reportData['data']['loansList'] as $loan) {
                    $html .= '
                        <tr>
                            <td>' . htmlspecialchars($loan['borrower_name']) . '</td>
                            <td>K' . number_format($loan['amount'], 2) . '</td>
                            <td>' . htmlspecialchars($loan['purpose']) . '</td>
                            <td>' . htmlspecialchars(ucfirst($loan['status'])) . '</td>
                            <td>' . htmlspecialchars($loan['application_date']) . '</td>
                            <td>K' . number_format($loan['total_repaid'], 2) . '</td>
                            <td>K' . number_format($loan['remaining_balance'], 2) . '</td>
                        </tr>';
                }

                $html .= '
                    </tbody>
                </table>
            </div>
        </div>';
            }
        } elseif ($reportData['reportType'] === 'equipment') {
            $html .= '
        <div class="summary-box">
            <div class="summary-title">Equipment Portfolio Overview</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value" style="color: #1e40af;">' . ($reportData['data']['totalEquipment'] ?? 0) . '</div>
                    <div class="summary-label">Total Equipment</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" style="color: #059669;">K' . number_format($reportData['data']['rentalRevenue'] ?? 0, 2) . '</div>
                    <div class="summary-label">Rental Revenue</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">Equipment Statistics</div>
            <div class="section-content">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Total Equipment</div>
                        <div class="metric-value" style="color: #ea580c;">' . ($reportData['data']['totalEquipment'] ?? 0) . '</div>
                        <div class="metric-description">All equipment items</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Available Equipment</div>
                        <div class="metric-value" style="color: #059669;">' . ($reportData['data']['availableEquipment'] ?? 0) . '</div>
                        <div class="metric-description">Ready for rental</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Total Requests</div>
                        <div class="metric-value" style="color: #1e40af;">' . ($reportData['data']['totalRequests'] ?? 0) . '</div>
                        <div class="metric-description">All time requests</div>
                    </div>
                </div>
            </div>
        </div>';

            // Add equipment list if available
            if (!empty($reportData['data']['equipmentList'])) {
                $html .= '
        <div class="section">
            <div class="section-header">Equipment Inventory</div>
            <div class="section-content">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Equipment Name</th>
                            <th>Type</th>
                            <th>Value</th>
                            <th>Daily Rate</th>
                            <th>Total Requests</th>
                            <th>Period Revenue</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>';

                foreach ($reportData['data']['equipmentList'] as $equipment) {
                    $html .= '
                        <tr>
                            <td>' . htmlspecialchars($equipment['name']) . '</td>
                            <td>' . htmlspecialchars(ucfirst($equipment['type'])) . '</td>
                            <td>K' . number_format($equipment['value'], 2) . '</td>
                            <td>K' . number_format($equipment['rental_cost_per_day'], 2) . '</td>
                            <td>' . $equipment['total_requests'] . '</td>
                            <td>K' . number_format($equipment['period_revenue'], 2) . '</td>
                            <td>' . ($equipment['is_available'] ? 'Available' : 'Not Available') . '</td>
                        </tr>';
                }

                $html .= '
                    </tbody>
                </table>
            </div>
        </div>';
            }
        }

        $html .= '
    </div>

    <div class="footer">
        <p>This report was generated by Siyamphanje Cooperative Management System</p>
        <p>Generated on ' . $reportData['dateGenerated'] . ' | Period: ' . $reportData['period'] . '</p>
        <p>© ' . date('Y') . ' Siyamphanje Agricultural Cooperative Society</p>
    </div>
</body>
</html>';

        return $html;
    }

    public function downloadReport(Request $request)
    {
        $request->validate([
            'reportType' => 'required|string',
            'format' => 'required|in:pdf,excel,csv',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
        ]);

        $reportType = $request->input('reportType');
        $format = $request->input('format');
        $startDate = $request->input('startDate') ? Carbon::parse($request->input('startDate')) : null;
        $endDate = $request->input('endDate') ? Carbon::parse($request->input('endDate')) : null;

        $reportData = $this->getReportData($reportType, $startDate, $endDate);

        switch ($format) {
            case 'pdf':
                // Ensure you have barryvdh/laravel-dompdf installed: composer require barryvdh/laravel-dompdf
                // And a view file for your PDF report, e.g., resources/views/reports/pdf_template.blade.php
                // Make sure to import Pdf facade: use Barryvdh\DomPDF\Facade\Pdf;
                return \Barryvdh\DomPDF\Facade\Pdf::loadView('reports.pdf_template', ['report' => $reportData])->download($reportType . '_report.pdf');
            case 'excel':
                // Ensure you have maatwebsite/excel installed: composer require maatwebsite/excel
                // You would also need to create an Export class, e.g., app/Exports/ReportsExport.php
                // Make sure to import Excel facade: use Maatwebsite\Excel\Facades\Excel;
                return \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ReportsExport($reportData), $reportType . '_report.xlsx');
            case 'csv':
                // Ensure you have maatwebsite/excel installed: composer require maatwebsite/excel
                // You can use the same Export class as for Excel, just change the format.
                // Make sure to import Excel facade: use Maatwebsite\Excel\Facades\Excel;
                return \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ReportsExport($reportData), $reportType . '_report.csv', \Maatwebsite\Excel\Excel::CSV);
            default:
                return response()->json(['error' => 'Invalid format'], 400);
        }
    }

    private function getMonthlyContributions()
    {
        $months = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = [
                'month' => $date->format('M Y'),
                'amount' => Contribution::whereYear('contribution_date', $date->year)
                    ->whereMonth('contribution_date', $date->month)
                    ->sum('amount'),
                'count' => Contribution::whereYear('contribution_date', $date->year)
                    ->whereMonth('contribution_date', $date->month)
                    ->count(),
            ];
        }
        return $months;
    }

    private function getLoanPerformance()
    {
        return [
            'total_disbursed' => Loan::whereIn('status', ['disbursed', 'partial_repayment', 'completed'])->sum('amount'),
            'total_repaid' => \App\Models\LoanRepayment::sum('amount'),
            'default_rate' => 0, // Calculate based on overdue loans
            'average_loan_size' => Loan::avg('amount'),
            'completion_rate' => Loan::where('status', 'completed')->count() / max(Loan::count(), 1) * 100,
        ];
    }

    private function getMemberGrowth()
    {
        $months = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = [
                'month' => $date->format('M Y'),
                'new_members' => Farmer::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'total_members' => Farmer::where('created_at', '<=', $date->endOfMonth())->count(),
            ];
        }
        return $months;
    }

    private function getFinancialTrends()
    {
        $months = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = [
                'month' => $date->format('M Y'),
                'savings' => SavingsAccount::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->sum('balance'),
                'loans' => Loan::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->sum('amount'),
                'contributions' => Contribution::whereYear('contribution_date', $date->year)
                    ->whereMonth('contribution_date', $date->month)
                    ->sum('amount'),
            ];
        }
        return $months;
    }
}
