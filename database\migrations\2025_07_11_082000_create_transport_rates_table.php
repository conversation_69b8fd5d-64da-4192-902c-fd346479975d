<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transport_rates', function (Blueprint $table) {
            $table->id();
            $table->string('region_name'); // e.g., "Zone A", "Zone B", "Lilongwe District"
            $table->decimal('distance_km', 8, 2); // Distance in kilometers
            $table->decimal('rate_per_ton', 8, 2); // Transport cost per metric ton
            $table->decimal('fuel_surcharge', 8, 2)->default(0); // Additional fuel costs
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index(['region_name', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transport_rates');
    }
};
