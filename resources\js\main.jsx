import React from 'react'
import { createRoot } from 'react-dom/client'
import { createInertiaApp } from '@inertiajs/react'
import { InertiaProgress } from '@inertiajs/progress'
import { Ziggy } from './ziggy'
import '@fortawesome/fontawesome-free/css/all.min.css';
// import './index.css'
import '../css/app.css';

InertiaProgress.init()

createInertiaApp({
  resolve: (name) => {
    const pages = import.meta.glob('./pages/**/*.jsx', { eager: true })
    return pages[`./pages/${name}.jsx`]
  },
  setup({ el, App, props }) {
    const root = createRoot(el)
    root.render(<App {...props} />)
    window.Ziggy = Ziggy
  },
})