<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('file_name');
            $table->string('file_path');
            $table->string('file_type'); // pdf, doc, image, etc.
            $table->integer('file_size'); // in bytes
            $table->enum('category', ['forms', 'policies', 'reports', 'training', 'general']);
            $table->enum('access_level', ['public', 'members_only', 'admin_only'])->default('members_only');
            $table->foreignId('uploaded_by')->constrained('users');
            $table->integer('download_count')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
