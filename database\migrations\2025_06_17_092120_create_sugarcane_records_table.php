<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sugarcane_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('farmer_id')->constrained();
            $table->foreignId('farmer_crop_id')->constrained();
            $table->decimal('expected_yield', 8, 2);
            $table->decimal('actual_yield', 8, 2)->nullable();
            $table->decimal('sugar_content', 5, 2)->nullable();
            $table->date('record_date');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sugarcane_records');
    }
};
