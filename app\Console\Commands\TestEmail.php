<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\WelcomeNewMember;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email} {name?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email to verify MailerSend integration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $name = $this->argument('name') ?? 'Test User';

        $this->info("Sending test welcome email to: {$email}");
        $this->info("Name: {$name}");
        $this->info("Using Laravel Mail with MailerSend");

        try {
            Mail::to($email)->send(new WelcomeNewMember(
                $name,
                $email,
                'TestPassword123',
                url('/login')
            ));

            $this->info("✅ Email sent successfully using Laravel Mail!");
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Email failed to send!");
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
    }
}
