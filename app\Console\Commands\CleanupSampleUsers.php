<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Farmer;
use App\Models\Contribution;
use App\Models\Loan;
use App\Models\LoanRepayment;
use App\Models\SavingsAccount;
use App\Models\EquipmentRequest;
use App\Models\MeetingAttendance;
use App\Models\Feedback;
use App\Models\Document;
use Illuminate\Support\Facades\DB;

class CleanupSampleUsers extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cleanup:sample-users {--force : Force deletion without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Remove sample/test users and their related data from the database';

    /**
     * Essential users that should never be deleted
     */
    protected $essentialEmails = [
        '<EMAIL>',
        '<EMAIL>', // Admin user from AdminSeeder
        '<EMAIL>',           // Member user from MemberSeeder
        '<EMAIL>',            // Staff user from StaffSeeder
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Sample Users Cleanup Tool');
        $this->info('================================');

        // Get all users except essential ones
        $sampleUsers = User::whereNotIn('email', $this->essentialEmails)->get();

        if ($sampleUsers->isEmpty()) {
            $this->info('✅ No sample users found to delete.');
            return 0;
        }

        $this->info("Found {$sampleUsers->count()} sample users to delete:");
        $this->table(
            ['ID', 'Name', 'Email', 'Role', 'Created'],
            $sampleUsers->map(function ($user) {
                return [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->role->name ?? 'No Role',
                    $user->created_at->format('Y-m-d H:i:s')
                ];
            })->toArray()
        );

        // Show what will be deleted
        $this->showDeletionSummary($sampleUsers);

        // Confirm deletion
        if (!$this->option('force')) {
            if (!$this->confirm('Are you sure you want to delete these sample users and ALL their related data?')) {
                $this->info('❌ Operation cancelled.');
                return 0;
            }
        }

        // Perform deletion
        $this->deleteSampleUsers($sampleUsers);

        $this->info('✅ Sample users cleanup completed successfully!');
        return 0;
    }

    /**
     * Show summary of what will be deleted
     */
    protected function showDeletionSummary($sampleUsers)
    {
        $this->info("\n📊 Data that will be deleted:");

        $farmerIds = $sampleUsers->pluck('farmer.id')->filter();
        $userIds = $sampleUsers->pluck('id');

        $counts = [
            'Farmer Profiles' => Farmer::whereIn('user_id', $userIds)->count(),
            'Contributions' => Contribution::whereIn('farmer_id', $farmerIds)->count(),
            'Loans' => Loan::whereIn('farmer_id', $farmerIds)->count(),
            'Loan Repayments' => LoanRepayment::whereHas('loan', function($q) use ($farmerIds) {
                $q->whereIn('farmer_id', $farmerIds);
            })->count(),
            'Savings Accounts' => SavingsAccount::whereIn('farmer_id', $farmerIds)->count(),
            'Equipment Requests' => EquipmentRequest::whereIn('user_id', $userIds)->count(),
            'Meeting Attendances' => MeetingAttendance::whereIn('farmer_id', $farmerIds)->count(),
            'Feedback Submissions' => Feedback::whereIn('submitted_by', $userIds)->count(),
            'Documents' => Document::whereIn('uploaded_by', $userIds)->count(),
        ];

        foreach ($counts as $type => $count) {
            if ($count > 0) {
                $this->line("  • {$type}: {$count}");
            }
        }

        $this->warn("\n⚠️  This action cannot be undone!");
        $this->info("Essential users will be preserved:");
        foreach ($this->essentialEmails as $email) {
            $this->line("  • {$email}");
        }
    }

    /**
     * Delete sample users and their related data
     */
    protected function deleteSampleUsers($sampleUsers)
    {
        $this->info("\n🗑️  Starting deletion process...");

        DB::beginTransaction();

        try {
            $deletedCount = 0;

            foreach ($sampleUsers as $user) {
                $this->line("Deleting user: {$user->name} ({$user->email})");

                // Get farmer profile if exists
                $farmer = $user->farmer;

                if ($farmer) {
                    // Delete farmer-related data
                    $this->deleteFarmerData($farmer);
                }

                // Delete user-related data
                $this->deleteUserData($user);

                // Delete the user
                $user->delete();
                $deletedCount++;

                $this->line("  ✓ Deleted user and related data");
            }

            DB::commit();
            $this->info("\n✅ Successfully deleted {$deletedCount} sample users and their related data.");

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("\n❌ Error during deletion: " . $e->getMessage());
            $this->error("All changes have been rolled back.");
            return 1;
        }
    }

    /**
     * Delete farmer-related data
     */
    protected function deleteFarmerData($farmer)
    {
        // Delete contributions
        Contribution::where('farmer_id', $farmer->id)->delete();

        // Delete loan repayments first (foreign key constraint)
        $loanIds = Loan::where('farmer_id', $farmer->id)->pluck('id');
        LoanRepayment::whereIn('loan_id', $loanIds)->delete();

        // Delete loans
        Loan::where('farmer_id', $farmer->id)->delete();

        // Delete savings accounts
        SavingsAccount::where('farmer_id', $farmer->id)->delete();

        // Delete meeting attendances
        MeetingAttendance::where('farmer_id', $farmer->id)->delete();

        // Delete the farmer profile
        $farmer->delete();
    }

    /**
     * Delete user-related data
     */
    protected function deleteUserData($user)
    {
        // Delete equipment requests
        EquipmentRequest::where('user_id', $user->id)->delete();

        // Delete feedback submissions
        Feedback::where('submitted_by', $user->id)->delete();

        // Delete documents uploaded by user
        Document::where('uploaded_by', $user->id)->delete();

        // Note: We don't delete records where user is referenced as admin/staff
        // (like approved_by, received_by, etc.) to maintain data integrity
    }
}
