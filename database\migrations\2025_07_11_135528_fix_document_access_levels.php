<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix any existing documents with old access level values
        DB::table('documents')
            ->where('access_level', 'members_only')
            ->update(['access_level' => 'member']);

        DB::table('documents')
            ->where('access_level', 'admin_only')
            ->update(['access_level' => 'admin']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the changes
        DB::table('documents')
            ->where('access_level', 'member')
            ->update(['access_level' => 'members_only']);

        DB::table('documents')
            ->where('access_level', 'admin')
            ->update(['access_level' => 'admin_only']);
    }
};
