<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('equipment_requests', function (Blueprint $table) {
            $table->decimal('rental_cost_per_day', 8, 2)->nullable()->after('purpose');
            $table->decimal('total_rental_cost', 10, 2)->nullable()->after('rental_cost_per_day');
            $table->enum('payment_status', ['pending', 'paid', 'overdue'])->default('pending')->after('total_rental_cost');
            $table->timestamp('payment_date')->nullable()->after('payment_status');
            $table->text('admin_notes')->nullable()->after('rejection_reason');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('equipment_requests', function (Blueprint $table) {
            $table->dropColumn(['rental_cost_per_day', 'total_rental_cost', 'payment_status', 'payment_date', 'admin_notes']);
        });
    }
};
