import React from 'react';
import { Head, useForm, Link, usePage } from '@inertiajs/react';

const Login = ({ status, canResetPassword }) => {
  const { csrf_token } = usePage().props;
  const { data, setData, post, processing, errors, reset } = useForm({
    email: '',
    password: '',
    remember: false,
  });

  // Define paths directly
  const paths = {
    login: '/login',
    passwordRequest: '/forgot-password',
    dashboard: {
      admin: '/dashboard/admin',
      staff: '/dashboard/staff',
      member: '/dashboard/member'
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    post(paths.login, {
      onError: (errors) => {
        console.error('Login errors:', errors);
        // If it's a CSRF error, try to refresh the page
        if (errors.message && errors.message.includes('expired')) {
          alert('Session expired. Please refresh the page and try again.');
          window.location.reload();
        }
      },
      onSuccess: () => {
        reset('password');
      }
    });
  };

  return (
    <div className="min-h-screen bg-green-50 flex items-center justify-center p-4">
      <Head title="Login" />
      
      <div className="w-full max-w-md bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header with cooperative branding */}
        <div className="bg-green-600 py-6 px-8 text-center">
          <h1 className="text-3xl font-bold text-white mb-1">Siyamphanje</h1>
          <p className="text-green-100 font-medium">Agricultural Cooperative</p>
        </div>
        
        {/* Login Form */}
        <form onSubmit={handleSubmit} className="p-8 space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={data.email}
              onChange={e => setData('email', e.target.value)}
              className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
              required
              autoComplete="username"
              autoFocus
            />
            {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={data.password}
              onChange={e => setData('password', e.target.value)}
              className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.password ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="••••••••"
              required
              autoComplete="current-password"
            />
            {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember"
                name="remember"
                type="checkbox"
                checked={data.remember}
                onChange={e => setData('remember', e.target.checked)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="remember" className="ml-2 block text-sm text-gray-700">
                Remember me
              </label>
            </div>
            
            <div className="text-sm">
              <Link
                href={paths.passwordRequest}
                className="text-green-600 hover:text-green-800 hover:underline"
              >
                Forgot password?
              </Link>
            </div>
          </div>
          
          <div>
            <button
              type="submit"
              disabled={processing}
              className={`w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-md transition duration-200 flex items-center justify-center ${
                processing ? 'opacity-75 cursor-not-allowed' : ''
              }`}
            >
              {processing ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Logging in...
                </>
              ) : (
                'Login'
              )}
            </button>
          </div>
        </form>
        
        {/* Footer */}
        <div className="bg-gray-50 px-8 py-4 text-center text-sm text-gray-600">
          © {new Date().getFullYear()} Siyamphanje Agricultural Cooperative. All rights reserved.
        </div>
      </div>
    </div>
  );
};

export default Login;