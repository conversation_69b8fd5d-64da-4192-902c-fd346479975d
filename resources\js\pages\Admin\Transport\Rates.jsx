import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function TransportRates({ auth, rates }) {
    const [showAddModal, setShowAddModal] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm({
        region_name: '',
        distance_km: '',
        rate_per_ton: '',
        fuel_surcharge: '0',
        description: '',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/admin/transport/rates', {
            onSuccess: () => {
                setShowAddModal(false);
                reset();
            }
        });
    };

    return (
        <AdminLayout user={auth.user}>
            <Head title="Transport Rates" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div className="p-6 border-b border-gray-200">
                            <div className="flex justify-between items-center">
                                <div>
                                    <Link
                                        href="/admin/transport"
                                        className="text-green-600 hover:text-green-800 mb-2 inline-flex items-center"
                                    >
                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M15 19l-7-7 7-7" clipRule="evenodd" />
                                        </svg>
                                        Back to Transport
                                    </Link>
                                    <h2 className="text-2xl font-bold text-gray-900">💰 Transport Rates</h2>
                                    <p className="text-gray-600">Manage transport pricing by region and distance</p>
                                </div>
                                <button
                                    onClick={() => setShowAddModal(true)}
                                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                                >
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                    </svg>
                                    Add Rate
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Rates Table */}
                    <div className="bg-white rounded-lg shadow overflow-hidden">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Region</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Distance</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Base Rate</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fuel Surcharge</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Rate</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {rates.map((rate) => (
                                        <tr key={rate.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{rate.region_name}</div>
                                                    <div className="text-sm text-gray-500">{rate.description}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">{rate.distance_km} km</div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">MWK {rate.rate_per_ton?.toLocaleString()}/ton</div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">MWK {rate.fuel_surcharge?.toLocaleString()}</div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-green-600">
                                                    MWK {(parseFloat(rate.rate_per_ton) + parseFloat(rate.fuel_surcharge)).toLocaleString()}/ton
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                                    rate.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {rate.is_active ? 'Active' : 'Inactive'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button className="text-green-600 hover:text-green-900 mr-3">
                                                    Edit
                                                </button>
                                                <button className="text-red-600 hover:text-red-900">
                                                    {rate.is_active ? 'Deactivate' : 'Activate'}
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {rates.length === 0 && (
                            <div className="text-center py-12">
                                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <h3 className="mt-2 text-sm font-medium text-gray-900">No transport rates found</h3>
                                <p className="mt-1 text-sm text-gray-500">Get started by adding your first transport rate.</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Add Rate Modal */}
            {showAddModal && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-medium text-gray-900">Add New Transport Rate</h3>
                                <button
                                    onClick={() => setShowAddModal(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </button>
                            </div>

                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Region Name *</label>
                                        <input
                                            type="text"
                                            value={data.region_name}
                                            onChange={(e) => setData('region_name', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            placeholder="e.g., Zone A (0-20km)"
                                            required
                                        />
                                        {errors.region_name && <p className="mt-1 text-sm text-red-600">{errors.region_name}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Distance (km) *</label>
                                        <input
                                            type="number"
                                            step="0.1"
                                            value={data.distance_km}
                                            onChange={(e) => setData('distance_km', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            placeholder="e.g., 15.0"
                                            required
                                        />
                                        {errors.distance_km && <p className="mt-1 text-sm text-red-600">{errors.distance_km}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Rate per Ton (MWK) *</label>
                                        <input
                                            type="number"
                                            step="0.01"
                                            value={data.rate_per_ton}
                                            onChange={(e) => setData('rate_per_ton', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            placeholder="e.g., 2500.00"
                                            required
                                        />
                                        {errors.rate_per_ton && <p className="mt-1 text-sm text-red-600">{errors.rate_per_ton}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Fuel Surcharge (MWK)</label>
                                        <input
                                            type="number"
                                            step="0.01"
                                            value={data.fuel_surcharge}
                                            onChange={(e) => setData('fuel_surcharge', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            placeholder="e.g., 200.00"
                                        />
                                        {errors.fuel_surcharge && <p className="mt-1 text-sm text-red-600">{errors.fuel_surcharge}</p>}
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Description</label>
                                    <textarea
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        rows={3}
                                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                        placeholder="e.g., Short distance transport to Illovo Sugar Mill"
                                    />
                                </div>

                                {/* Rate Preview */}
                                {data.rate_per_ton && (
                                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <h4 className="text-sm font-medium text-green-800 mb-2">Rate Preview</h4>
                                        <div className="text-sm text-green-700">
                                            <p>Base Rate: MWK {parseFloat(data.rate_per_ton || 0).toLocaleString()}/ton</p>
                                            <p>Fuel Surcharge: MWK {parseFloat(data.fuel_surcharge || 0).toLocaleString()}</p>
                                            <p className="font-medium">Total Rate: MWK {(parseFloat(data.rate_per_ton || 0) + parseFloat(data.fuel_surcharge || 0)).toLocaleString()}/ton</p>
                                        </div>
                                    </div>
                                )}

                                <div className="flex justify-end space-x-3 pt-4">
                                    <button
                                        type="button"
                                        onClick={() => setShowAddModal(false)}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={processing}
                                        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                                    >
                                        {processing ? 'Adding...' : 'Add Rate'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}
        </AdminLayout>
    );
}
