<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('equipment', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type');
            $table->string('model')->nullable();
            $table->string('serial_number')->nullable()->unique();
            $table->date('purchase_date')->nullable();
            $table->decimal('purchase_price', 10, 2)->nullable();
            $table->decimal('daily_rental_rate', 8, 2)->default(0);
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'maintenance', 'retired', 'damaged'])->default('active');
            $table->enum('condition', ['excellent', 'good', 'fair', 'poor'])->default('good');
            $table->string('location')->nullable();
            $table->string('maintenance_schedule')->nullable();
            $table->date('last_maintenance_date')->nullable();
            $table->date('next_maintenance_date')->nullable();
            $table->string('image_url')->nullable();
            $table->json('specifications')->nullable();
            $table->boolean('operator_required')->default(false);
            $table->boolean('transport_available')->default(false);
            $table->decimal('transport_rate', 8, 2)->default(0);
            $table->decimal('operator_rate', 8, 2)->default(0);
            $table->enum('availability_status', ['available', 'rented', 'maintenance', 'reserved'])->default('available');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['status', 'availability_status']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('equipment');
    }
};
