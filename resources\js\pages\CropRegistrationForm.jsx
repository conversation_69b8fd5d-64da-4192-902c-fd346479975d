import React, { useState, useEffect } from 'react';
import MemberLayout from '@/Layouts/MemberLayout';
import { Head, usePage } from '@inertiajs/react';
import { Inertia } from '@inertiajs/inertia';

export default function CropRegistrationForm({ auth, crops }) {
    const [formData, setFormData] = useState({
        crop_id: '',
        area_planted: '',
        planting_date: '',
        expected_harvest_date: '',
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        Inertia.post(route('crops.store'), formData, {
            onSuccess: () => {
                alert('Crop registered successfully!');
                setFormData({ crop_id: '', area_planted: '', planting_date: '', expected_harvest_date: '' });
            },
            onError: (errors) => {
                console.error('Error registering crop:', errors);
                alert('Error registering crop. Please check console for details.');
            },
        });
    };

    return (
        <MemberLayout user={auth.user} header="Register New Crop">
            <Head title="Register Crop" />
            <div className="container mx-auto p-4">
                <h1 className="text-2xl font-bold mb-4">Register New Crop</h1>
                <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md">
                    <div className="mb-4">
                        <label htmlFor="crop_id" className="block text-gray-700 text-sm font-bold mb-2">Crop Type</label>
                        <select
                            name="crop_id"
                            id="crop_id"
                            value={formData.crop_id}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            required
                        >
                            <option value="">Select a crop</option>
                            {crops.map(crop => (
                                <option key={crop.id} value={crop.id}>{crop.name} ({crop.variety})</option>
                            ))}
                        </select>
                    </div>
                    <div className="mb-4">
                        <label htmlFor="area_planted" className="block text-gray-700 text-sm font-bold mb-2">Area Planted (acres)</label>
                        <input
                            type="number"
                            name="area_planted"
                            id="area_planted"
                            value={formData.area_planted}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            step="0.01"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="planting_date" className="block text-gray-700 text-sm font-bold mb-2">Planting Date</label>
                        <input
                            type="date"
                            name="planting_date"
                            id="planting_date"
                            value={formData.planting_date}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            required
                        />
                    </div>
                    <div className="mb-6">
                        <label htmlFor="expected_harvest_date" className="block text-gray-700 text-sm font-bold mb-2">Expected Harvest Date</label>
                        <input
                            type="date"
                            name="expected_harvest_date"
                            id="expected_harvest_date"
                            value={formData.expected_harvest_date}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            required
                        />
                    </div>
                    <div className="flex items-center justify-between">
                        <button
                            type="submit"
                            className="bg-amber-500 hover:bg-amber-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Register Crop
                        </button>
                    </div>
                </form>
            </div>
        </MemberLayout>
    );
}