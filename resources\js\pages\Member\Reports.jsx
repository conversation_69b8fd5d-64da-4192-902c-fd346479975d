import React, { useState } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

const MemberReports = ({ auth, financialSummary, recentTransactions, loanSummary, cropSummary }) => {
  const [activeTab, setActiveTab] = useState('financial');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'ZMW',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-ZM', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'deposit':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'withdrawal':
        return (
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'loan':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  const tabs = [
    { id: 'financial', name: 'Financial Summary', icon: '💰' },
    { id: 'transactions', name: 'Transaction History', icon: '📊' },
    { id: 'loans', name: 'Loan Summary', icon: '🏦' },
    { id: 'crops', name: 'Crop Reports', icon: '🌾' },
  ];

  return (
    <MemberLayout user={auth.user} header="My Reports">
      <Head title="My Reports - Siyamphanje Cooperative" />
      
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-green-50 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">My Reports</h1>
                <p className="mt-2 text-gray-600">View your financial reports and activity summary</p>
              </div>
              <Link
                href="/dashboard/member"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Total Savings</h3>
                  <p className="text-3xl font-bold text-green-600">
                    {formatCurrency(financialSummary?.total_savings || 0)}
                  </p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Active Loans</h3>
                  <p className="text-3xl font-bold text-blue-600">
                    {formatCurrency(loanSummary?.total_active_loans || 0)}
                  </p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-purple-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Contributions</h3>
                  <p className="text-3xl font-bold text-purple-600">
                    {formatCurrency(financialSummary?.total_contributions || 0)}
                  </p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-yellow-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Registered Crops</h3>
                  <p className="text-3xl font-bold text-yellow-600">
                    {cropSummary?.total_crops || 0}
                  </p>
                  <p className="text-sm text-gray-500">{cropSummary?.total_area || 0} hectares</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Reports Tabs */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {/* Financial Summary */}
              {activeTab === 'financial' && (
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Financial Summary</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Savings Breakdown */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">Savings Accounts</h4>
                      {financialSummary?.savings_accounts?.length > 0 ? (
                        <div className="space-y-3">
                          {financialSummary.savings_accounts.map((account) => (
                            <div key={account.id} className="flex justify-between items-center">
                              <div>
                                <p className="font-medium text-gray-900">{account.account_type}</p>
                                <p className="text-sm text-gray-600">#{account.account_number}</p>
                              </div>
                              <p className="text-lg font-bold text-green-600">{formatCurrency(account.balance)}</p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500">No savings accounts found.</p>
                      )}
                    </div>

                    {/* Monthly Summary */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">This Month</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Deposits:</span>
                          <span className="font-medium text-green-600">
                            {formatCurrency(financialSummary?.monthly_deposits || 0)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Withdrawals:</span>
                          <span className="font-medium text-red-600">
                            {formatCurrency(financialSummary?.monthly_withdrawals || 0)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Contributions:</span>
                          <span className="font-medium text-purple-600">
                            {formatCurrency(financialSummary?.monthly_contributions || 0)}
                          </span>
                        </div>
                        <div className="flex justify-between border-t border-gray-300 pt-3">
                          <span className="font-medium text-gray-900">Net Change:</span>
                          <span className={`font-bold ${
                            (financialSummary?.monthly_deposits || 0) - (financialSummary?.monthly_withdrawals || 0) >= 0
                              ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {formatCurrency(
                              (financialSummary?.monthly_deposits || 0) - (financialSummary?.monthly_withdrawals || 0)
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Transaction History */}
              {activeTab === 'transactions' && (
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Recent Transactions</h3>
                  
                  {recentTransactions && recentTransactions.length > 0 ? (
                    <div className="space-y-4">
                      {recentTransactions.map((transaction) => (
                        <div key={transaction.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            {getTransactionIcon(transaction.type)}
                            <div className="ml-4">
                              <p className="font-medium text-gray-900">{transaction.description}</p>
                              <p className="text-sm text-gray-600">{formatDate(transaction.created_at)}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`text-lg font-bold ${
                              transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {transaction.type === 'deposit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                            </p>
                            <p className="text-sm text-gray-500">{transaction.reference_number}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Transactions</h3>
                      <p className="text-gray-600">You haven't made any transactions yet.</p>
                    </div>
                  )}
                </div>
              )}

              {/* Loan Summary */}
              {activeTab === 'loans' && (
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Loan Summary</h3>
                  
                  {loanSummary?.loans && loanSummary.loans.length > 0 ? (
                    <div className="space-y-6">
                      {loanSummary.loans.map((loan) => (
                        <div key={loan.id} className="border border-gray-200 rounded-lg p-6">
                          <div className="flex justify-between items-start mb-4">
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900">{loan.purpose}</h4>
                              <p className="text-sm text-gray-600">Loan #{loan.id}</p>
                            </div>
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                              loan.status === 'completed' ? 'bg-green-100 text-green-800' :
                              loan.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                              loan.status === 'disbursed' ? 'bg-purple-100 text-purple-800' :
                              loan.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {loan.status}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <p className="text-sm text-gray-600">Loan Amount</p>
                              <p className="font-semibold text-gray-900">{formatCurrency(loan.amount)}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Interest Rate</p>
                              <p className="font-semibold text-gray-900">{loan.interest_rate}%</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Term</p>
                              <p className="font-semibold text-gray-900">{loan.term_months} months</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Monthly Payment</p>
                              <p className="font-semibold text-gray-900">{formatCurrency(loan.monthly_payment)}</p>
                            </div>
                          </div>
                          
                          {loan.status === 'disbursed' && (
                            <div className="mt-4 bg-blue-50 rounded-lg p-4">
                              <div className="flex justify-between items-center">
                                <span className="text-blue-700">Outstanding Balance:</span>
                                <span className="font-bold text-blue-900">{formatCurrency(loan.outstanding_balance)}</span>
                              </div>
                              <div className="flex justify-between items-center mt-2">
                                <span className="text-blue-700">Next Payment Due:</span>
                                <span className="font-medium text-blue-900">{formatDate(loan.next_payment_date)}</span>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Loans</h3>
                      <p className="text-gray-600">You haven't applied for any loans yet.</p>
                      <Link
                        href="/loans/apply"
                        className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                      >
                        Apply for Loan
                      </Link>
                    </div>
                  )}
                </div>
              )}

              {/* Crop Reports */}
              {activeTab === 'crops' && (
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Crop Reports</h3>
                  
                  {cropSummary?.crops && cropSummary.crops.length > 0 ? (
                    <div className="space-y-6">
                      {cropSummary.crops.map((crop) => (
                        <div key={crop.id} className="border border-gray-200 rounded-lg p-6">
                          <div className="flex justify-between items-start mb-4">
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900">{crop.crop_name}</h4>
                              <p className="text-sm text-gray-600">{crop.variety || 'No variety specified'}</p>
                            </div>
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                              {crop.crop_type}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <p className="text-sm text-gray-600">Area Planted</p>
                              <p className="font-semibold text-gray-900">{crop.area_planted} hectares</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Planting Date</p>
                              <p className="font-semibold text-gray-900">{formatDate(crop.planting_date)}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Expected Harvest</p>
                              <p className="font-semibold text-gray-900">{formatDate(crop.expected_harvest_date)}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Estimated Value</p>
                              <p className="font-semibold text-gray-900">{formatCurrency(crop.estimated_value)}</p>
                            </div>
                          </div>
                          
                          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-600">Planting Method</p>
                              <p className="font-medium text-gray-900">{crop.planting_method}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Irrigation Method</p>
                              <p className="font-medium text-gray-900">{crop.irrigation_method}</p>
                            </div>
                          </div>
                          
                          {crop.location_description && (
                            <div className="mt-4">
                              <p className="text-sm text-gray-600">Location</p>
                              <p className="font-medium text-gray-900">{crop.location_description}</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Crops Registered</h3>
                      <p className="text-gray-600">You haven't registered any crops yet.</p>
                      <Link
                        href="/crops/register"
                        className="mt-4 inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                      >
                        Register Crops
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MemberLayout>
  );
};

export default MemberReports;
