<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class StaffSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create staff role if it doesn't exist
        $staffRole = Role::firstOrCreate(
            ['name' => 'staff'],
            ['description' => 'Staff1']
        );

        // Create staff user if it doesn't exist
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Staff User',
                'password' => Hash::make('cosmas23'), // Change this to a secure password!
                'role_id' => $staffRole->id,
                'is_active' => true,
            ]
        );
    }
}
