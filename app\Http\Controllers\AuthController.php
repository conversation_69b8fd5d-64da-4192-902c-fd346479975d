<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth; // Added missing import
use Illuminate\Support\Facades\Log;

class AuthController extends Controller // Renamed to match route
{
    // app/Http/Controllers/AuthController.php



public function login(Request $request)
{
    try {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (!Auth::attempt($credentials)) {
            return back()->withErrors(['email' => 'Invalid credentials'])->withInput();
        }

        $request->session()->regenerate();

        $user = Auth::user();
        $role = $user->role->name;


        // Redirect based on role
         return match($role) {
            'admin' => redirect()->route('dashboard.admin'),
            'staff' => redirect()->route('dashboard.staff'),
            'member' => redirect()->route('dashboard.member'),
            default => $this->logout($request),
        };
    } catch (\Exception $e) {
        Log::error('Login error: ' . $e->getMessage());
        return back()->withErrors(['email' => 'An error occurred during login. Please try again.'])->withInput();
    }
}

    public function logout(Request $request)
    {
        // Clear any session security data
        $request->session()->forget(['user_agent', 'ip_address', 'login_time']);

        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        // Redirect to login page with success message
        return redirect()->route('login')->with('success', 'You have been logged out successfully.');
    }
}