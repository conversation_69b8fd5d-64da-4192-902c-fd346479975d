<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Loan extends Model
{
    use HasFactory;

    protected $fillable = [
        'farmer_id',
        'amount',
        'interest_rate',
        'term_months',
        'purpose',
        'status',
        'application_date',
        'approval_date',
        'disbursement_date',
        'approved_by',
        'rejection_reason'
    ];

    public function farmer()
    {
        return $this->belongsTo(Farmer::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function repayments()
    {
        return $this->hasMany(LoanRepayment::class);
    }
}
