<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\SmsNotification;
use App\Models\User;
use App\Models\Farmer;
use App\Services\SmsService;
use Illuminate\Support\Facades\Log;

class SmsController extends Controller
{
    protected $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->middleware('admin');
        $this->smsService = $smsService;
    }

    /**
     * Display SMS management dashboard.
     */
    public function index(Request $request)
    {
        $status = $request->get('status', 'all');
        $type = $request->get('type', 'all');
        $search = $request->get('search');

        $query = SmsNotification::with(['sender', 'recipient'])
            ->orderBy('created_at', 'desc');

        if ($status && $status !== 'all') {
            $query->where('status', $status);
        }

        if ($type && $type !== 'all') {
            $query->where('type', $type);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('phone_number', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $smsNotifications = $query->paginate(20)->through(function ($sms) {
            return [
                'id' => $sms->id,
                'phone_number' => $sms->phone_number,
                'formatted_phone' => $sms->formatted_phone,
                'message' => $sms->message,
                'truncated_message' => $sms->getTruncatedMessageAttribute(50),
                'type' => $sms->type,
                'status' => $sms->status,
                'status_color' => $sms->status_color,
                'type_color' => $sms->type_color,
                'sent_by' => $sms->sender->name ?? 'System',
                'recipient_name' => $sms->recipient->name ?? 'Unknown',
                'sent_at' => $sms->sent_at ? $sms->sent_at->format('Y-m-d H:i:s') : null,
                'delivered_at' => $sms->delivered_at ? $sms->delivered_at->format('Y-m-d H:i:s') : null,
                'cost' => $sms->cost,
                'error_message' => $sms->error_message,
                'created_at' => $sms->created_at->format('Y-m-d H:i:s'),
            ];
        });

        // Get statistics
        $stats = [
            'today' => $this->smsService->getStatistics('today'),
            'month' => $this->smsService->getStatistics('month'),
            'total' => $this->smsService->getStatistics('all'),
        ];

        return Inertia::render('Admin/SmsManagement', [
            'smsNotifications' => $smsNotifications,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'type' => $type,
                'search' => $search,
            ],
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Send individual SMS.
     */
    public function sendSms(Request $request)
    {
        $validated = $request->validate([
            'phone_number' => 'required|string',
            'message' => 'required|string|max:1000',
            'type' => 'required|string|in:general,announcement,reminder,custom',
            'recipient_user_id' => 'nullable|exists:users,id',
        ]);

        $result = $this->smsService->sendSms(
            $validated['phone_number'],
            $validated['message'],
            $validated['type'],
            $validated['recipient_user_id']
        );

        if ($result['success']) {
            return redirect()->back()->with('success', 'SMS sent successfully!');
        } else {
            return redirect()->back()->with('error', 'Failed to send SMS: ' . $result['error']);
        }
    }

    /**
     * Send bulk SMS to members.
     */
    public function sendBulkSms(Request $request)
    {
        $validated = $request->validate([
            'message' => 'required|string|max:1000',
            'type' => 'required|string|in:general,announcement,reminder,custom',
            'recipient_type' => 'required|string|in:all_members,active_members,selected_members',
            'selected_members' => 'nullable|array',
            'selected_members.*' => 'exists:users,id',
        ]);

        // Get recipients based on type
        $recipients = $this->getRecipients($validated['recipient_type'], $validated['selected_members'] ?? []);

        if (empty($recipients)) {
            return redirect()->back()->with('error', 'No recipients found.');
        }

        // Send bulk SMS
        $results = $this->smsService->sendBulkSms($recipients, $validated['message'], $validated['type']);

        // Count results
        $successful = collect($results)->where('success', true)->count();
        $failed = collect($results)->where('success', false)->count();

        $message = "Bulk SMS completed: {$successful} sent successfully";
        if ($failed > 0) {
            $message .= ", {$failed} failed";
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Send meeting reminder SMS.
     */
    public function sendMeetingReminder(Request $request)
    {
        $validated = $request->validate([
            'meeting_id' => 'required|exists:meetings,id',
            'recipient_type' => 'required|string|in:all_members,attending_only',
        ]);

        $meeting = \App\Models\Meeting::with(['attendances.farmer.user'])->findOrFail($validated['meeting_id']);

        $recipients = [];
        if ($validated['recipient_type'] === 'all_members') {
            // Send to all members
            $recipients = User::whereHas('farmer')->with('farmer')->get()->map(function ($user) {
                return [
                    'phone' => $user->phone,
                    'user_id' => $user->id,
                ];
            })->toArray();
        } else {
            // Send only to those who are attending
            $recipients = $meeting->attendances
                ->where('response_status', 'attending')
                ->map(function ($attendance) {
                    return [
                        'phone' => $attendance->farmer->user->phone,
                        'user_id' => $attendance->farmer->user->id,
                    ];
                })->toArray();
        }

        if (empty($recipients)) {
            return redirect()->back()->with('error', 'No recipients found for meeting reminder.');
        }

        // Send meeting reminder SMS
        $results = [];
        foreach ($recipients as $recipient) {
            $result = $this->smsService->sendMeetingReminderSms(
                $recipient['phone'],
                $meeting->title,
                $meeting->meeting_date,
                $meeting->meeting_time,
                $meeting->location,
                $recipient['user_id']
            );
            $results[] = $result;
        }

        $successful = collect($results)->where('success', true)->count();
        $failed = collect($results)->where('success', false)->count();

        $message = "Meeting reminders sent: {$successful} successful";
        if ($failed > 0) {
            $message .= ", {$failed} failed";
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Get SMS details.
     */
    public function show($id)
    {
        $sms = SmsNotification::with(['sender', 'recipient'])->findOrFail($id);

        return Inertia::render('Admin/SmsDetails', [
            'sms' => $sms,
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Resend failed SMS.
     */
    public function resend($id)
    {
        $sms = SmsNotification::findOrFail($id);

        if ($sms->status !== 'failed') {
            return redirect()->back()->with('error', 'Only failed SMS can be resent.');
        }

        $result = $this->smsService->sendSms(
            $sms->phone_number,
            $sms->message,
            $sms->type,
            $sms->recipient_user_id,
            $sms->metadata
        );

        if ($result['success']) {
            return redirect()->back()->with('success', 'SMS resent successfully!');
        } else {
            return redirect()->back()->with('error', 'Failed to resend SMS: ' . $result['error']);
        }
    }

    /**
     * Get recipients based on type.
     */
    protected function getRecipients($type, $selectedMembers = [])
    {
        switch ($type) {
            case 'all_members':
                return User::whereHas('farmer')->get()->map(function ($user) {
                    return [
                        'phone' => $user->phone,
                        'user_id' => $user->id,
                    ];
                })->toArray();

            case 'active_members':
                return User::whereHas('farmer', function ($query) {
                    $query->where('status', 'active');
                })->get()->map(function ($user) {
                    return [
                        'phone' => $user->phone,
                        'user_id' => $user->id,
                    ];
                })->toArray();

            case 'selected_members':
                return User::whereIn('id', $selectedMembers)->get()->map(function ($user) {
                    return [
                        'phone' => $user->phone,
                        'user_id' => $user->id,
                    ];
                })->toArray();

            default:
                return [];
        }
    }

    /**
     * Get SMS statistics for dashboard.
     */
    public function getStatistics()
    {
        return response()->json([
            'today' => $this->smsService->getStatistics('today'),
            'month' => $this->smsService->getStatistics('month'),
            'total' => $this->smsService->getStatistics('all'),
        ]);
    }

    /**
     * Export SMS logs.
     */
    public function export(Request $request)
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'nullable|string|in:pending,sent,failed,delivered',
            'type' => 'nullable|string',
        ]);

        $query = SmsNotification::with(['sender', 'recipient']);

        if ($validated['start_date']) {
            $query->whereDate('created_at', '>=', $validated['start_date']);
        }

        if ($validated['end_date']) {
            $query->whereDate('created_at', '<=', $validated['end_date']);
        }

        if ($validated['status']) {
            $query->where('status', $validated['status']);
        }

        if ($validated['type']) {
            $query->where('type', $validated['type']);
        }

        $smsLogs = $query->orderBy('created_at', 'desc')->get();

        // Generate CSV
        $filename = 'sms_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($smsLogs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID', 'Phone Number', 'Message', 'Type', 'Status', 
                'Sent By', 'Recipient', 'Sent At', 'Delivered At', 
                'Cost', 'Provider', 'Error Message'
            ]);

            // CSV data
            foreach ($smsLogs as $sms) {
                fputcsv($file, [
                    $sms->id,
                    $sms->phone_number,
                    $sms->message,
                    $sms->type,
                    $sms->status,
                    $sms->sender->name ?? 'System',
                    $sms->recipient->name ?? 'Unknown',
                    $sms->sent_at ? $sms->sent_at->format('Y-m-d H:i:s') : '',
                    $sms->delivered_at ? $sms->delivered_at->format('Y-m-d H:i:s') : '',
                    $sms->cost ?? 0,
                    $sms->provider ?? '',
                    $sms->error_message ?? '',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
