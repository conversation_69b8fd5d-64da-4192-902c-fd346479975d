import React, { useState } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function CreateContribution({ auth, members }) {
    const { data, setData, post, processing, errors } = useForm({
        member_id: '',
        amount: '',
        type: 'monthly',
        description: '',
        payment_method: 'cash',
        reference_number: '',
        contribution_date: new Date().toISOString().split('T')[0],
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/admin/contributions');
    };

    const contributionTypes = [
        { value: 'monthly', label: 'Monthly Contribution' },
        { value: 'special', label: 'Special Contribution' },
        { value: 'emergency', label: 'Emergency Fund' },
        { value: 'development', label: 'Development Fund' },
    ];

    const paymentMethods = [
        { value: 'cash', label: 'Cash' },
        { value: 'bank_transfer', label: 'Bank Transfer' },
        { value: 'mobile_money', label: 'Mobile Money' },
    ];

    return (
        <AdminLayout auth={auth}>
            <Head title="Record Contribution" />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                        <div className="p-6 sm:p-8">
                            {/* Header */}
                            <div className="flex items-center justify-between mb-8">
                                <div>
                                    <h1 className="text-3xl font-bold text-gray-900">Record Contribution</h1>
                                    <p className="mt-2 text-gray-600">Add a new member contribution to the system</p>
                                </div>
                                <Link
                                    href="/admin/contributions"
                                    className="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 transition ease-in-out duration-150"
                                >
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                    </svg>
                                    Back to Contributions
                                </Link>
                            </div>

                            {/* Form */}
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Member Selection */}
                                    <div className="md:col-span-2">
                                        <label htmlFor="member_id" className="block text-sm font-medium text-gray-700 mb-2">
                                            Select Member <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            id="member_id"
                                            value={data.member_id}
                                            onChange={(e) => setData('member_id', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        >
                                            <option value="">Choose a member...</option>
                                            {members.map((member) => (
                                                <option key={member.id} value={member.id}>
                                                    {member.name} ({member.email})
                                                    {member.farm_size && ` - ${member.farm_size}`}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.member_id && <p className="mt-1 text-sm text-red-600">{errors.member_id}</p>}
                                    </div>

                                    {/* Amount */}
                                    <div>
                                        <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                                            Amount (MW) <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="number"
                                            id="amount"
                                            step="0.01"
                                            min="0.01"
                                            value={data.amount}
                                            onChange={(e) => setData('amount', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            placeholder="0.00"
                                            required
                                        />
                                        {errors.amount && <p className="mt-1 text-sm text-red-600">{errors.amount}</p>}
                                    </div>

                                    {/* Contribution Type */}
                                    <div>
                                        <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                                            Contribution Type <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            id="type"
                                            value={data.type}
                                            onChange={(e) => setData('type', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        >
                                            {contributionTypes.map((type) => (
                                                <option key={type.value} value={type.value}>
                                                    {type.label}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.type && <p className="mt-1 text-sm text-red-600">{errors.type}</p>}
                                    </div>

                                    {/* Payment Method */}
                                    <div>
                                        <label htmlFor="payment_method" className="block text-sm font-medium text-gray-700 mb-2">
                                            Payment Method <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            id="payment_method"
                                            value={data.payment_method}
                                            onChange={(e) => setData('payment_method', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        >
                                            {paymentMethods.map((method) => (
                                                <option key={method.value} value={method.value}>
                                                    {method.label}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.payment_method && <p className="mt-1 text-sm text-red-600">{errors.payment_method}</p>}
                                    </div>

                                    {/* Reference Number */}
                                    <div>
                                        <label htmlFor="reference_number" className="block text-sm font-medium text-gray-700 mb-2">
                                            Reference Number
                                        </label>
                                        <input
                                            type="text"
                                            id="reference_number"
                                            value={data.reference_number}
                                            onChange={(e) => setData('reference_number', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            placeholder="Receipt/Transaction number"
                                        />
                                        {errors.reference_number && <p className="mt-1 text-sm text-red-600">{errors.reference_number}</p>}
                                    </div>

                                    {/* Contribution Date */}
                                    <div>
                                        <label htmlFor="contribution_date" className="block text-sm font-medium text-gray-700 mb-2">
                                            Contribution Date <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="date"
                                            id="contribution_date"
                                            value={data.contribution_date}
                                            onChange={(e) => setData('contribution_date', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        />
                                        {errors.contribution_date && <p className="mt-1 text-sm text-red-600">{errors.contribution_date}</p>}
                                    </div>

                                    {/* Description */}
                                    <div className="md:col-span-2">
                                        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                                            Description/Notes
                                        </label>
                                        <textarea
                                            id="description"
                                            rows={3}
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            placeholder="Additional notes about this contribution..."
                                        />
                                        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                                    </div>
                                </div>

                                {/* Submit Button */}
                                <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                    <Link
                                        href="/admin/contributions"
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </Link>
                                    <button
                                        type="submit"
                                        disabled={processing}
                                        className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {processing ? 'Recording...' : 'Record Contribution'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
