<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('documents', function (Blueprint $table) {
            // First, update any existing data to use new values
            DB::statement("UPDATE documents SET access_level = 'member' WHERE access_level = 'members_only'");
            DB::statement("UPDATE documents SET access_level = 'admin' WHERE access_level = 'admin_only'");

            // Change the column from ENUM to VARCHAR
            $table->string('access_level', 50)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('documents', function (Blueprint $table) {
            // Revert data back to old values
            DB::statement("UPDATE documents SET access_level = 'members_only' WHERE access_level = 'member'");
            DB::statement("UPDATE documents SET access_level = 'admin_only' WHERE access_level = 'admin'");

            // Change back to ENUM
            $table->enum('access_level', ['public', 'members_only', 'admin_only'])->default('members_only')->change();
        });
    }
};
