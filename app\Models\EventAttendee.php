<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EventAttendee extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'user_id',
        'response_status',
        'attended',
        'comments',
        'responded_at',
    ];

    protected $casts = [
        'attended' => 'boolean',
        'responded_at' => 'datetime',
    ];

    /**
     * Get the event.
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the farmer name.
     */
    public function getFarmerNameAttribute()
    {
        return $this->user->farmer->name ?? $this->user->name;
    }
}
