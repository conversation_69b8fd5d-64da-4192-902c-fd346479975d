<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;
use App\Models\User;

class PreventDashboardConflicts
{
    /**
     * Handle an incoming request to prevent dashboard conflicts
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply to dashboard routes
        if (!$request->is('dashboard*')) {
            return $next($request);
        }

        // Check if user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please log in to access the dashboard.');
        }

        $user = auth()->user();
        
        // Get fresh user data from database to avoid cache issues
        $freshUser = User::with('role')->find($user->id);
        
        if (!$freshUser || !$freshUser->role) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Invalid user session. Please log in again.');
        }

        // Check for role-route mismatch
        $currentRoute = $request->route()->getName();
        $userRole = $freshUser->role->name;
        
        $expectedRoute = match($userRole) {
            'admin' => 'dashboard.admin',
            'staff' => 'dashboard.staff',
            'member' => 'dashboard.member',
            default => null,
        };

        // If there's a mismatch, log it and redirect
        if ($expectedRoute && $currentRoute !== $expectedRoute && $currentRoute !== 'dashboard') {
            Log::warning('Dashboard route mismatch detected', [
                'user_id' => $freshUser->id,
                'user_email' => $freshUser->email,
                'user_role' => $userRole,
                'current_route' => $currentRoute,
                'expected_route' => $expectedRoute,
                'ip_address' => $request->ip(),
                'session_id' => session()->getId(),
                'timestamp' => now(),
            ]);

            // Clear any potential session conflicts
            session()->regenerate();
            
            // Redirect to correct dashboard
            return redirect()->route($expectedRoute)->with('info', 'Redirected to your appropriate dashboard.');
        }

        return $next($request);
    }
}
