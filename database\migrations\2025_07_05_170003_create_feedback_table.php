<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feedback', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('message');
            $table->enum('type', ['suggestion', 'complaint', 'compliment', 'question', 'other']);
            $table->enum('category', ['services', 'meetings', 'equipment', 'loans', 'general', 'other']);
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['pending', 'under_review', 'resolved', 'closed'])->default('pending');
            $table->foreignId('submitted_by')->constrained('users');
            $table->foreignId('assigned_to')->nullable()->constrained('users');
            $table->text('admin_response')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->boolean('is_anonymous')->default(false);
            $table->json('attachments')->nullable(); // for file attachments
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feedback');
    }
};
