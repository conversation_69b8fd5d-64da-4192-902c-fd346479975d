import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function Settings({ auth, settings, systemStats, users, roles }) {
    const [activeTab, setActiveTab] = useState('cooperative');

    const { data: cooperativeData, setData: setCooperativeData, post: postCooperative, processing: processingCooperative, errors: cooperativeErrors } = useForm({
        name: settings?.cooperative?.name || '',
        description: settings?.cooperative?.description || '',
        address: settings?.cooperative?.address || '',
        phone: settings?.cooperative?.phone || '',
        email: settings?.cooperative?.email || '',
        registration_number: settings?.cooperative?.registration_number || '',
    });

    const { data: financialData, setData: setFinancialData, post: postFinancial, processing: processingFinancial, errors: financialErrors } = useForm({
        default_interest_rate: settings?.financial?.default_interest_rate || 5.0,
        minimum_loan_amount: settings?.financial?.minimum_loan_amount || 1000,
        maximum_loan_amount: settings?.financial?.maximum_loan_amount || 100000,
        minimum_savings_balance: settings?.financial?.minimum_savings_balance || 100,
        contribution_frequency: settings?.financial?.contribution_frequency || 'monthly',
        late_payment_penalty: settings?.financial?.late_payment_penalty || 2.0,
    });

    const { data: systemData, setData: setSystemData, post: postSystem, processing: processingSystem, errors: systemErrors } = useForm({
        maintenance_mode: settings?.system?.maintenance_mode || false,
        allow_registration: settings?.system?.allow_registration || true,
        require_approval: settings?.system?.require_approval || true,
        backup_frequency: settings?.system?.backup_frequency || 'daily',
        session_timeout: settings?.system?.session_timeout || 120,
    });

    const { data: notificationData, setData: setNotificationData, post: postNotification, processing: processingNotification, errors: notificationErrors } = useForm({
        email_notifications: settings?.notifications?.email_notifications || true,
        sms_notifications: settings?.notifications?.sms_notifications || false,
        loan_reminders: settings?.notifications?.loan_reminders || true,
        contribution_reminders: settings?.notifications?.contribution_reminders || true,
        equipment_notifications: settings?.notifications?.equipment_notifications || true,
    });

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-ZM', {
            style: 'currency',
            currency: 'MW',
            minimumFractionDigits: 0
        }).format(amount || 0);
    };

    const submitCooperativeSettings = (e) => {
        e.preventDefault();
        postCooperative('/admin/settings/cooperative');
    };

    const submitFinancialSettings = (e) => {
        e.preventDefault();
        postFinancial('/admin/settings/financial');
    };

    const submitSystemSettings = (e) => {
        e.preventDefault();
        postSystem('/admin/settings/system');
    };

    const submitNotificationSettings = (e) => {
        e.preventDefault();
        postNotification('/admin/settings/notifications');
    };

    const tabs = [
        { id: 'cooperative', name: 'Cooperative Info', icon: '🏢' },
        { id: 'financial', name: 'Financial Settings', icon: '💰' },
        { id: 'system', name: 'System Settings', icon: '⚙️' },
        { id: 'notifications', name: 'Notifications', icon: '🔔' },
        { id: 'users', name: 'User Management', icon: '👥' },
    ];

    return (
        <AdminLayout auth={auth}>
            <Head title="System Settings - Admin" />
            
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-7xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">System Settings</h1>
                                <p className="mt-2 text-gray-600">Configure system settings and preferences</p>
                            </div>
                            <Link
                                href="/dashboard/admin"
                                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                ← Back to Dashboard
                            </Link>
                        </div>
                    </div>

                    {/* System Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-blue-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">Total Users</h3>
                                    <p className="text-3xl font-bold text-blue-600">{systemStats?.total_users || 0}</p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-green-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">Total Farmers</h3>
                                    <p className="text-3xl font-bold text-green-600">{systemStats?.total_farmers || 0}</p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-purple-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">Database Size</h3>
                                    <p className="text-3xl font-bold text-purple-600">{systemStats?.database_size || 'N/A'}</p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-yellow-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">Last Backup</h3>
                                    <p className="text-lg font-bold text-yellow-600">{systemStats?.last_backup || 'N/A'}</p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-indigo-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">System Uptime</h3>
                                    <p className="text-3xl font-bold text-indigo-600">{systemStats?.system_uptime || 'N/A'}</p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Settings Tabs */}
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div className="border-b border-gray-200">
                            <nav className="-mb-px flex space-x-8 px-6">
                                {tabs.map((tab) => (
                                    <button
                                        key={tab.id}
                                        onClick={() => setActiveTab(tab.id)}
                                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                            activeTab === tab.id
                                                ? 'border-green-500 text-green-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                    >
                                        <span className="mr-2">{tab.icon}</span>
                                        {tab.name}
                                    </button>
                                ))}
                            </nav>
                        </div>

                        <div className="p-6">
                            {/* Cooperative Settings */}
                            {activeTab === 'cooperative' && (
                                <div>
                                    <h3 className="text-xl font-bold text-gray-900 mb-6">Cooperative Information</h3>
                                    <form onSubmit={submitCooperativeSettings} className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Cooperative Name <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    value={cooperativeData.name}
                                                    onChange={(e) => setCooperativeData('name', e.target.value)}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                    required
                                                />
                                                {cooperativeErrors.name && (
                                                    <p className="mt-1 text-sm text-red-600">{cooperativeErrors.name}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Registration Number <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    value={cooperativeData.registration_number}
                                                    onChange={(e) => setCooperativeData('registration_number', e.target.value)}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                    required
                                                />
                                                {cooperativeErrors.registration_number && (
                                                    <p className="mt-1 text-sm text-red-600">{cooperativeErrors.registration_number}</p>
                                                )}
                                            </div>

                                            <div className="md:col-span-2">
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Description <span className="text-red-500">*</span>
                                                </label>
                                                <textarea
                                                    value={cooperativeData.description}
                                                    onChange={(e) => setCooperativeData('description', e.target.value)}
                                                    rows={3}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                    required
                                                />
                                                {cooperativeErrors.description && (
                                                    <p className="mt-1 text-sm text-red-600">{cooperativeErrors.description}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Address <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    value={cooperativeData.address}
                                                    onChange={(e) => setCooperativeData('address', e.target.value)}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                    required
                                                />
                                                {cooperativeErrors.address && (
                                                    <p className="mt-1 text-sm text-red-600">{cooperativeErrors.address}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Phone Number <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="tel"
                                                    value={cooperativeData.phone}
                                                    onChange={(e) => setCooperativeData('phone', e.target.value)}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                    required
                                                />
                                                {cooperativeErrors.phone && (
                                                    <p className="mt-1 text-sm text-red-600">{cooperativeErrors.phone}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Email Address <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="email"
                                                    value={cooperativeData.email}
                                                    onChange={(e) => setCooperativeData('email', e.target.value)}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                    required
                                                />
                                                {cooperativeErrors.email && (
                                                    <p className="mt-1 text-sm text-red-600">{cooperativeErrors.email}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="flex justify-end">
                                            <button
                                                type="submit"
                                                disabled={processingCooperative}
                                                className="bg-green-600 text-white py-2 px-6 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                                            >
                                                {processingCooperative ? 'Saving...' : 'Save Cooperative Settings'}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
