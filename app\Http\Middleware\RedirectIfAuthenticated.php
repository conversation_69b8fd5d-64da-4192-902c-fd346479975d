<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                $user = Auth::guard($guard)->user();

                // Load role if not already loaded
                if (!$user->role) {
                    $user = $user->load('role');
                }

                // Redirect to appropriate dashboard based on role
                if ($user->role) {
                    return match($user->role->name) {
                        'admin' => redirect()->route('dashboard.admin'),
                        'staff' => redirect()->route('dashboard.staff'),
                        'member' => redirect()->route('dashboard.member'),
                        default => redirect(RouteServiceProvider::HOME),
                    };
                }

                // Fallback to home if no role
                return redirect(RouteServiceProvider::HOME);
            }
        }

        return $next($request);
    }
}
