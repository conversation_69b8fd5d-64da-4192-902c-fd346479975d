<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use App\Models\EquipmentRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;

class EquipmentController extends Controller
{
    /**
     * Display a listing of equipment for admin
     */
    public function adminIndex()
    {
        $equipment = Equipment::with(['equipmentRequests' => function($query) {
            $query->whereIn('status', ['pending', 'approved'])
                  ->where('end_date', '>=', now());
        }])->orderBy('created_at', 'desc')->get();

        $stats = [
            'total' => Equipment::count(),
            'available' => Equipment::available()->count(),
            'rented' => Equipment::where('availability_status', 'rented')->count(),
            'maintenance' => Equipment::where('availability_status', 'maintenance')->count(),
        ];

        $typeOptions = [
            'tractor' => 'Tractor',
            'harvester' => 'Harvester',
            'planter' => 'Planter',
            'cultivator' => 'Cultivator',
            'sprayer' => 'Sprayer',
            'irrigation' => 'Irrigation',
            'tools' => 'Tools',
            'processing' => 'Processing',
            'transport' => 'Transport'
        ];

        $statusOptions = [
            'available' => 'Available',
            'rented' => 'Rented',
            'maintenance' => 'Maintenance',
            'reserved' => 'Reserved'
        ];

        $conditionOptions = [
            'excellent' => 'Excellent',
            'good' => 'Good',
            'fair' => 'Fair',
            'poor' => 'Poor'
        ];

        return Inertia::render('Admin/Equipment', [
            'equipment' => $equipment,
            'stats' => $stats,
            'typeOptions' => $typeOptions,
            'statusOptions' => $statusOptions,
            'conditionOptions' => $conditionOptions,
        ]);
    }

    /**
     * Store a newly created equipment with comprehensive validation
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:tractor,harvester,planter,cultivator,sprayer,irrigation,tools,processing,transport',
            'model' => 'nullable|string|max:255',
            'serial_number' => 'nullable|string|max:255|unique:equipment',
            'purchase_date' => 'nullable|date|before_or_equal:today',
            'purchase_price' => 'nullable|numeric|min:0',
            'daily_rental_rate' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:1000',
            'condition' => 'required|string|in:excellent,good,fair,poor',
            'location' => 'nullable|string|max:255',
            'operator_required' => 'boolean',
            'operator_rate' => 'nullable|numeric|min:0',
            'transport_available' => 'boolean',
            'transport_rate' => 'nullable|numeric|min:0',
            'maintenance_schedule' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|string|in:available,rented,maintenance,reserved',
        ], [
            'type.in' => 'Please select a valid equipment type.',
            'condition.in' => 'Please select a valid condition.',
            'status.in' => 'Please select a valid status.',
            'daily_rental_rate.required' => 'Daily rental rate is required.',
            'daily_rental_rate.min' => 'Daily rental rate must be at least 0.',
            'purchase_date.before_or_equal' => 'Purchase date cannot be in the future.',
        ]);

        // Business logic: If operator is required, operator rate must be provided
        if ($validated['operator_required'] && empty($validated['operator_rate'])) {
            return back()->withErrors(['operator_rate' => 'Operator rate is required when operator is needed.']);
        }

        // Business logic: If transport is available, transport rate must be provided
        if ($validated['transport_available'] && empty($validated['transport_rate'])) {
            return back()->withErrors(['transport_rate' => 'Transport rate is required when transport is available.']);
        }

        // Business logic: Equipment in poor condition should not be available for rent
        if ($validated['condition'] === 'poor' && $validated['status'] === 'available') {
            $validated['status'] = 'maintenance';
        }

        // Set availability_status to match status for backward compatibility
        $validated['availability_status'] = $validated['status'];

        $equipment = Equipment::create($validated);

        Log::info('Equipment created', [
            'equipment_id' => $equipment->id,
            'name' => $equipment->name,
            'created_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Equipment added successfully!');
    }

    /**
     * Update the specified equipment with business logic
     */
    public function update(Request $request, Equipment $equipment)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:tractor,harvester,planter,cultivator,sprayer,irrigation,tools,processing,transport',
            'model' => 'nullable|string|max:255',
            'serial_number' => 'nullable|string|max:255|unique:equipment,serial_number,' . $equipment->id,
            'purchase_date' => 'nullable|date|before_or_equal:today',
            'purchase_price' => 'nullable|numeric|min:0',
            'daily_rental_rate' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:1000',
            'condition' => 'required|string|in:excellent,good,fair,poor',
            'location' => 'nullable|string|max:255',
            'operator_required' => 'boolean',
            'operator_rate' => 'nullable|numeric|min:0',
            'transport_available' => 'boolean',
            'transport_rate' => 'nullable|numeric|min:0',
            'maintenance_schedule' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|string|in:available,rented,maintenance,reserved',
        ]);

        // Business logic: Check if equipment is currently rented before changing status
        $hasActiveRentals = EquipmentRequest::where('equipment_id', $equipment->id)
            ->whereIn('status', ['approved'])
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->exists();

        if ($hasActiveRentals && $validated['status'] !== 'rented') {
            return back()->withErrors(['status' => 'Cannot change status - equipment is currently rented.']);
        }

        // Business logic: If operator is required, operator rate must be provided
        if ($validated['operator_required'] && empty($validated['operator_rate'])) {
            return back()->withErrors(['operator_rate' => 'Operator rate is required when operator is needed.']);
        }

        // Business logic: If transport is available, transport rate must be provided
        if ($validated['transport_available'] && empty($validated['transport_rate'])) {
            return back()->withErrors(['transport_rate' => 'Transport rate is required when transport is available.']);
        }

        // Business logic: Equipment in poor condition should not be available for rent
        if ($validated['condition'] === 'poor' && $validated['status'] === 'available') {
            $validated['status'] = 'maintenance';
        }

        $equipment->update($validated);

        Log::info('Equipment updated', [
            'equipment_id' => $equipment->id,
            'name' => $equipment->name,
            'updated_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Equipment updated successfully!');
    }

    /**
     * Update equipment status with business logic validation
     */
    public function updateStatus(Request $request, Equipment $equipment)
    {
        $validated = $request->validate([
            'status' => 'required|string|in:available,rented,maintenance,reserved',
        ]);

        $newStatus = $validated['status'];
        $currentStatus = $equipment->status;

        // Business logic: Check if equipment is currently rented
        $hasActiveRentals = EquipmentRequest::where('equipment_id', $equipment->id)
            ->whereIn('status', ['approved'])
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->exists();

        // Business logic: Cannot change from rented status if there are active rentals
        if ($hasActiveRentals && $currentStatus === 'rented' && $newStatus !== 'rented') {
            return back()->withErrors(['status' => 'Cannot change status - equipment is currently rented to a member.']);
        }

        // Business logic: Cannot set to available if equipment is in poor condition
        if ($newStatus === 'available' && $equipment->condition === 'poor') {
            return back()->withErrors(['status' => 'Equipment in poor condition cannot be made available. Please repair first.']);
        }

        // Business logic: Auto-update related rental requests when equipment goes to maintenance
        if ($newStatus === 'maintenance' && $currentStatus !== 'maintenance') {
            // Cancel pending requests for this equipment
            EquipmentRequest::where('equipment_id', $equipment->id)
                ->where('status', 'pending')
                ->update([
                    'status' => 'cancelled',
                    'rejection_reason' => 'Equipment moved to maintenance',
                    'admin_notes' => 'Automatically cancelled due to equipment maintenance'
                ]);
        }

        $equipment->update(['status' => $newStatus]);

        Log::info('Equipment status updated', [
            'equipment_id' => $equipment->id,
            'old_status' => $currentStatus,
            'new_status' => $newStatus,
            'updated_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Equipment status updated successfully!');
    }

    /**
     * Remove the specified equipment
     */
    public function destroy(Equipment $equipment)
    {
        // Check if equipment has active requests
        $activeRequests = $equipment->activeRequests()->count();

        if ($activeRequests > 0) {
            return redirect()->back()->with('error', 'Cannot delete equipment with active requests.');
        }

        $equipmentName = $equipment->name;
        $equipment->delete();

        Log::info('Equipment deleted', [
            'equipment_name' => $equipmentName,
            'deleted_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Equipment deleted successfully!');
    }

    /**
     * Get available equipment for member requests with business logic
     */
    public function getAvailable()
    {
        // Get equipment that is available for rental
        $availableEquipment = Equipment::where(function($query) {
                $query->where('status', 'available')
                      ->orWhere('availability_status', 'available');
            })
            ->where('condition', '!=', 'poor')
            ->orderBy('name')
            ->get()
            ->map(function ($equipment) {
                return [
                    'id' => $equipment->id,
                    'name' => $equipment->name,
                    'type' => $equipment->type,
                    'model' => $equipment->model,
                    'description' => $equipment->description,
                    'daily_rental_rate' => (float) $equipment->daily_rental_rate,
                    'condition' => $equipment->condition,
                    'operator_required' => (bool) $equipment->operator_required,
                    'operator_rate' => (float) ($equipment->operator_rate ?? 0),
                    'transport_available' => (bool) $equipment->transport_available,
                    'transport_rate' => (float) ($equipment->transport_rate ?? 0),
                    'location' => $equipment->location,
                ];
            })
            ->values();

        Log::info('Available equipment for members', [
            'count' => $availableEquipment->count(),
            'equipment' => $availableEquipment->pluck('name')
        ]);

        return response()->json($availableEquipment);
    }

    /**
     * Update equipment availability status
     */
    public function updateAvailability(Request $request, Equipment $equipment)
    {
        $validated = $request->validate([
            'availability_status' => 'required|in:available,rented,maintenance,reserved',
        ]);

        $equipment->update($validated);

        Log::info('Equipment availability updated', [
            'equipment_id' => $equipment->id,
            'availability_status' => $validated['availability_status'],
            'updated_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Equipment availability updated successfully!');
    }
}
