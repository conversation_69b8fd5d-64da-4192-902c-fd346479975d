import React from 'react';
import { InertiaProgress } from '@inertiajs/progress';
import AppRoutes from './routes/AppRoutes';
import { ThemeProvider } from './contexts/ThemeContext';

// Initialize Inertia progress bar (optional but recommended)
InertiaProgress.init();

function App() {
  return (
    <ThemeProvider>
      {/* Your main application layout wrapper */}
      <AppRoutes />
    </ThemeProvider>
  );
}

export default App;