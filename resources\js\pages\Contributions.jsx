import React from 'react';
import { Head } from '@inertiajs/react'
const Contributions = () => {
  const contributions = [
    { id: 1, date: '2023-06-15', amount: 50000, type: 'Monthly', status: 'Paid' },
    { id: 2, date: '2023-05-15', amount: 50000, type: 'Monthly', status: 'Paid' },
    { id: 3, date: '2023-04-15', amount: 50000, type: 'Monthly', status: 'Paid' },
    { id: 4, date: '2023-03-15', amount: 50000, type: 'Monthly', status: 'Paid' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-green-800">Member Contributions</h1>
          <button className="bg-green-700 hover:bg-green-800 text-white px-4 py-2 rounded-lg transition-colors">
            Make New Contribution
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="grid grid-cols-5 bg-green-100 p-4 font-semibold text-green-800">
            <div>Date</div>
            <div>Amount</div>
            <div>Type</div>
            <div>Status</div>
            <div>Receipt</div>
          </div>
          
          {contributions.map((contribution) => (
            <div key={contribution.id} className="grid grid-cols-5 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors">
              <div>{contribution.date}</div>
              <div className="font-medium">K {contribution.amount.toLocaleString()}</div>
              <div>{contribution.type}</div>
              <div>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  contribution.status === 'Paid' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {contribution.status}
                </span>
              </div>
              <div>
                <button className="text-blue-600 hover:text-blue-800 hover:underline">
                  Download
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Total Contributions</h3>
            <p className="text-3xl font-bold text-green-700">K 200,000</p>
            <p className="text-sm text-gray-500 mt-2">All time contributions</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">This Year</h3>
            <p className="text-3xl font-bold text-green-700">K 150,000</p>
            <p className="text-sm text-gray-500 mt-2">2023 contributions</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Pending</h3>
            <p className="text-3xl font-bold text-yellow-600">K 50,000</p>
            <p className="text-sm text-gray-500 mt-2">Unpaid contributions</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contributions;