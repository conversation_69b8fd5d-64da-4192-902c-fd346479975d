<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MeetingAttendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'meeting_id',
        'farmer_id',
        'attended',
        'response_status',
        'comments'
    ];

    public function meeting()
    {
        return $this->belongsTo(Meeting::class);
    }

    public function farmer()
    {
        return $this->belongsTo(Farmer::class);
    }
}
