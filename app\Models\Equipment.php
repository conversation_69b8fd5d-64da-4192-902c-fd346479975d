<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Equipment extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'model',
        'serial_number',
        'purchase_date',
        'purchase_price',
        'daily_rental_rate',
        'description',
        'status',
        'condition',
        'location',
        'maintenance_schedule',
        'last_maintenance_date',
        'next_maintenance_date',
        'image_url',
        'specifications',
        'operator_required',
        'transport_available',
        'transport_rate',
        'operator_rate',
        'availability_status',
        'notes'
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'last_maintenance_date' => 'date',
        'next_maintenance_date' => 'date',
        'purchase_price' => 'decimal:2',
        'daily_rental_rate' => 'decimal:2',
        'transport_rate' => 'decimal:2',
        'operator_rate' => 'decimal:2',
        'operator_required' => 'boolean',
        'transport_available' => 'boolean',
        'specifications' => 'array'
    ];

    // Equipment status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_MAINTENANCE = 'maintenance';
    const STATUS_RETIRED = 'retired';
    const STATUS_DAMAGED = 'damaged';

    // Availability status constants
    const AVAILABILITY_AVAILABLE = 'available';
    const AVAILABILITY_RENTED = 'rented';
    const AVAILABILITY_MAINTENANCE = 'maintenance';
    const AVAILABILITY_RESERVED = 'reserved';

    // Condition constants
    const CONDITION_EXCELLENT = 'excellent';
    const CONDITION_GOOD = 'good';
    const CONDITION_FAIR = 'fair';
    const CONDITION_POOR = 'poor';

    /**
     * Get equipment requests for this equipment
     */
    public function equipmentRequests()
    {
        return $this->hasMany(EquipmentRequest::class, 'equipment_id');
    }

    /**
     * Get active equipment requests
     */
    public function activeRequests()
    {
        return $this->equipmentRequests()
            ->whereIn('status', ['pending', 'approved'])
            ->where('end_date', '>=', now());
    }

    /**
     * Check if equipment is currently available
     */
    public function isAvailable()
    {
        return $this->availability_status === self::AVAILABILITY_AVAILABLE
            && $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Get equipment type options
     */
    public static function getTypeOptions()
    {
        return [
            'tractor' => 'Tractor',
            'harvester' => 'Harvester',
            'planter' => 'Planter',
            'cultivator' => 'Cultivator',
            'sprayer' => 'Sprayer',
            'irrigation' => 'Irrigation Equipment',
            'tools' => 'Hand Tools',
            'processing' => 'Processing Equipment',
            'transport' => 'Transport Vehicle',
            'other' => 'Other'
        ];
    }

    /**
     * Get status options
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_MAINTENANCE => 'Under Maintenance',
            self::STATUS_RETIRED => 'Retired',
            self::STATUS_DAMAGED => 'Damaged'
        ];
    }

    /**
     * Get availability status options
     */
    public static function getAvailabilityOptions()
    {
        return [
            self::AVAILABILITY_AVAILABLE => 'Available',
            self::AVAILABILITY_RENTED => 'Currently Rented',
            self::AVAILABILITY_MAINTENANCE => 'Under Maintenance',
            self::AVAILABILITY_RESERVED => 'Reserved'
        ];
    }

    /**
     * Get condition options
     */
    public static function getConditionOptions()
    {
        return [
            self::CONDITION_EXCELLENT => 'Excellent',
            self::CONDITION_GOOD => 'Good',
            self::CONDITION_FAIR => 'Fair',
            self::CONDITION_POOR => 'Poor'
        ];
    }

    /**
     * Scope for available equipment
     */
    public function scopeAvailable($query)
    {
        return $query->where('availability_status', self::AVAILABILITY_AVAILABLE)
            ->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for active equipment
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Business Logic: Check if equipment is available for rental
     */
    public function isAvailableForRental()
    {
        return $this->status === 'available' &&
               $this->condition !== 'poor' &&
               !$this->hasActiveRentals();
    }

    /**
     * Business Logic: Check if equipment has active rentals
     */
    public function hasActiveRentals()
    {
        return $this->equipmentRequests()
            ->whereIn('status', ['approved'])
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->exists();
    }

    /**
     * Business Logic: Get next available date for rental
     */
    public function getNextAvailableDate()
    {
        $lastRental = $this->equipmentRequests()
            ->whereIn('status', ['approved'])
            ->where('end_date', '>=', now())
            ->orderBy('end_date', 'desc')
            ->first();

        return $lastRental ? $lastRental->end_date->addDay() : now();
    }

    /**
     * Business Logic: Calculate total rental cost
     */
    public function calculateRentalCost($startDate, $endDate, $includeOperator = false, $includeTransport = false)
    {
        $start = \Carbon\Carbon::parse($startDate);
        $end = \Carbon\Carbon::parse($endDate);
        $days = $start->diffInDays($end) + 1;

        $total = $this->daily_rental_rate * $days;

        if ($includeOperator && $this->operator_required && $this->operator_rate) {
            $total += $this->operator_rate * $days;
        }

        if ($includeTransport && $this->transport_available && $this->transport_rate) {
            $total += $this->transport_rate;
        }

        return $total;
    }

    /**
     * Business Logic: Check if equipment needs maintenance
     */
    public function needsMaintenance()
    {
        // Equipment in poor condition needs maintenance
        if ($this->condition === 'poor') {
            return true;
        }

        // Check if maintenance is overdue based on schedule
        if ($this->maintenance_schedule && $this->last_maintenance_date) {
            $lastMaintenance = \Carbon\Carbon::parse($this->last_maintenance_date);
            $scheduleMonths = (int) filter_var($this->maintenance_schedule, FILTER_SANITIZE_NUMBER_INT);

            if ($scheduleMonths > 0) {
                return $lastMaintenance->addMonths($scheduleMonths)->isPast();
            }
        }

        return false;
    }

    /**
     * Business Logic: Get equipment utilization rate
     */
    public function getUtilizationRate($months = 12)
    {
        $totalDays = now()->subMonths($months)->diffInDays(now());

        $rentedDays = $this->equipmentRequests()
            ->whereIn('status', ['approved', 'completed'])
            ->where('start_date', '>=', now()->subMonths($months))
            ->get()
            ->sum(function ($request) {
                return \Carbon\Carbon::parse($request->start_date)
                    ->diffInDays(\Carbon\Carbon::parse($request->end_date)) + 1;
            });

        return $totalDays > 0 ? ($rentedDays / $totalDays) * 100 : 0;
    }
}
