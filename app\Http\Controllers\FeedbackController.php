<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Feedback;
use App\Models\User;

class FeedbackController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display feedback for members.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Members can only see their own feedback
        $feedback = Feedback::with(['assignedAdmin'])
            ->where('submitted_by', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->through(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->title,
                    'message' => $item->message,
                    'type' => $item->type,
                    'category' => $item->category,
                    'priority' => $item->priority,
                    'status' => $item->status,
                    'admin_response' => $item->admin_response,
                    'responded_at' => $item->responded_at,
                    'assigned_to' => $item->assignedAdmin->name ?? null,
                    'created_at' => $item->created_at,
                    'priority_color' => $item->priority_color,
                    'status_color' => $item->status_color,
                    'type_color' => $item->type_color,
                    'is_pending' => $item->is_pending,
                    'is_resolved' => $item->is_resolved,
                ];
            });

        $stats = [
            'total_submitted' => Feedback::where('submitted_by', $user->id)->count(),
            'pending' => Feedback::where('submitted_by', $user->id)->where('status', 'pending')->count(),
            'resolved' => Feedback::where('submitted_by', $user->id)->whereIn('status', ['resolved', 'closed'])->count(),
        ];

        return Inertia::render('Feedback/Index', [
            'feedback' => $feedback,
            'stats' => $stats,
            'auth' => ['user' => $user]
        ]);
    }

    /**
     * Display admin feedback management.
     */
    public function adminIndex(Request $request)
    {
        // Role check is now handled by middleware

        $status = $request->get('status');
        $type = $request->get('type');
        $priority = $request->get('priority');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Feedback::with(['submitter.farmer', 'assignedAdmin']);

        // Filter by status
        if ($status && $status !== 'all') {
            $query->where('status', $status);
        }

        // Filter by type
        if ($type && $type !== 'all') {
            $query->where('type', $type);
        }

        // Filter by priority
        if ($priority && $priority !== 'all') {
            $query->where('priority', $priority);
        }

        // Sorting
        $query->orderBy($sortBy, $sortOrder);

        $feedback = $query->paginate(20);

        $stats = [
            'total_feedback' => Feedback::count(),
            'pending' => Feedback::where('status', 'pending')->count(),
            'under_review' => Feedback::where('status', 'under_review')->count(),
            'resolved' => Feedback::where('status', 'resolved')->count(),
            'urgent' => Feedback::where('priority', 'urgent')->count(),
        ];

        return Inertia::render('Admin/Feedback', [
            'feedback' => $feedback,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'type' => $type,
                'priority' => $priority,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
            ],
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Store new feedback.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'type' => 'required|in:suggestion,complaint,compliment,question,other',
            'category' => 'required|in:services,meetings,equipment,loans,general,other',
            'priority' => 'required|in:low,medium,high,urgent',
            'is_anonymous' => 'boolean',
        ]);

        Feedback::create([
            'title' => $request->title,
            'message' => $request->message,
            'type' => $request->type,
            'category' => $request->category,
            'priority' => $request->priority,
            'is_anonymous' => $request->boolean('is_anonymous', false),
            'submitted_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Your feedback has been submitted successfully!');
    }

    /**
     * Update feedback status and response (Admin only).
     */
    public function update(Request $request, $id)
    {
        // Role check is now handled by middleware

        $feedback = Feedback::findOrFail($id);

        $request->validate([
            'status' => 'required|in:pending,under_review,resolved,closed',
            'admin_response' => 'nullable|string|max:2000',
            'priority' => 'required|in:low,medium,high,urgent',
        ]);

        $updateData = [
            'status' => $request->status,
            'priority' => $request->priority,
            'assigned_to' => auth()->id(),
        ];

        if ($request->admin_response) {
            $updateData['admin_response'] = $request->admin_response;
            $updateData['responded_at'] = now();
        }

        $feedback->update($updateData);

        return redirect()->back()->with('success', 'Feedback updated successfully!');
    }

    /**
     * Show specific feedback details.
     */
    public function show($id)
    {
        $feedback = Feedback::with(['submitter.farmer', 'assignedAdmin'])
            ->findOrFail($id);

        // Check permissions
        $user = auth()->user();
        if ($user->role->name !== 'admin' && $feedback->submitted_by !== $user->id) {
            abort(403, 'Unauthorized');
        }

        $feedbackData = [
            'id' => $feedback->id,
            'title' => $feedback->title,
            'message' => $feedback->message,
            'type' => $feedback->type,
            'category' => $feedback->category,
            'priority' => $feedback->priority,
            'status' => $feedback->status,
            'submitter_name' => $feedback->submitter_name,
            'admin_response' => $feedback->admin_response,
            'responded_at' => $feedback->responded_at,
            'assigned_to' => $feedback->assignedAdmin->name ?? null,
            'created_at' => $feedback->created_at,
            'priority_color' => $feedback->priority_color,
            'status_color' => $feedback->status_color,
            'type_color' => $feedback->type_color,
            'is_pending' => $feedback->is_pending,
            'is_resolved' => $feedback->is_resolved,
            'is_anonymous' => $feedback->is_anonymous,
        ];

        return Inertia::render('Feedback/Show', [
            'feedback' => $feedbackData,
            'auth' => ['user' => $user]
        ]);
    }

    /**
     * Delete feedback (Admin only).
     */
    public function destroy($id)
    {
        // Role check is now handled by middleware

        $feedback = Feedback::findOrFail($id);
        $feedback->delete();

        return redirect()->back()->with('success', 'Feedback deleted successfully!');
    }
}
