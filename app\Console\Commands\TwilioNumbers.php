<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TwilioNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twilio:numbers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List available Twilio phone numbers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $accountSid = config('sms.providers.twilio.account_sid');
        $authToken = config('sms.providers.twilio.auth_token');

        if (!$accountSid || !$authToken) {
            $this->error('Twilio credentials not configured in .env file');
            return 1;
        }

        $this->info('Fetching your Twilio phone numbers...');

        try {
            $url = "https://api.twilio.com/2010-04-01/Accounts/{$accountSid}/IncomingPhoneNumbers.json";
            
            $response = Http::withBasicAuth($accountSid, $authToken)->get($url);

            if ($response->successful()) {
                $data = $response->json();
                $numbers = $data['incoming_phone_numbers'] ?? [];

                if (empty($numbers)) {
                    $this->warn('No phone numbers found in your Twilio account.');
                    $this->info('For trial accounts, you may need to:');
                    $this->info('1. Verify your phone number in Twilio Console');
                    $this->info('2. Use a Twilio test number like +***********');
                    $this->info('3. Or purchase a phone number for production use');
                } else {
                    $this->info('Available Twilio phone numbers:');
                    $this->table(
                        ['Phone Number', 'Friendly Name', 'Capabilities'],
                        collect($numbers)->map(function ($number) {
                            $capabilities = [];
                            if ($number['capabilities']['sms']) $capabilities[] = 'SMS';
                            if ($number['capabilities']['voice']) $capabilities[] = 'Voice';
                            
                            return [
                                $number['phone_number'],
                                $number['friendly_name'] ?? 'N/A',
                                implode(', ', $capabilities)
                            ];
                        })->toArray()
                    );

                    $this->info('');
                    $this->info('To use one of these numbers, update your .env file:');
                    $this->info('TWILIO_FROM_NUMBER=' . $numbers[0]['phone_number']);
                }
            } else {
                $this->error('Failed to fetch Twilio numbers: ' . $response->body());
            }
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }

        return 0;
    }
}
