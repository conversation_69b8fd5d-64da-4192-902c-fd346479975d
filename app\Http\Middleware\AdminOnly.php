<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class AdminOnly
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please log in to access this page.');
        }

        $user = auth()->user();

        // Load the user's role if not already loaded
        $user = \App\Models\User::with('role')->find($user->id);

        // Check if user has a role
        if (!$user->role) {
            return redirect()->route('login')->with('error', 'Your account does not have a valid role. Please contact the administrator.');
        }

        // Check if user is admin
        if ($user->role->name !== 'admin') {
            // Log the unauthorized access attempt
            Log::warning('Unauthorized admin access attempt', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_role' => $user->role->name,
                'requested_url' => $request->url(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return redirect()->route('login')->with('error', 'Unauthorized access. Admin privileges required.');
        }

        return $next($request);
    }
}
