<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contributions', function (Blueprint $table) {
            // Drop the existing farmer_id column if it exists
            if (Schema::hasColumn('contributions', 'farmer_id')) {
                $table->dropForeign(['farmer_id']);
                $table->dropColumn('farmer_id');
            }
        });

        Schema::table('contributions', function (Blueprint $table) {
            // Add farmer_id as nullable
            $table->foreignId('farmer_id')->nullable()->after('member_id')->constrained('farmers')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contributions', function (Blueprint $table) {
            $table->dropForeign(['farmer_id']);
            $table->dropColumn('farmer_id');
        });

        Schema::table('contributions', function (Blueprint $table) {
            // Recreate the original farmer_id column (not nullable)
            $table->foreignId('farmer_id')->after('member_id')->constrained('farmers')->onDelete('cascade');
        });
    }
};
