import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function Reports({ auth, stats, recentData, reportData }) {
    const [generatedReport, setGeneratedReport] = useState(reportData || null);
    const [isGenerating, setIsGenerating] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        reportType: 'financial',
        startDate: '',
        endDate: '',
        format: 'pdf',
        date_range: 'last_month',
    });

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-ZM', {
            style: 'currency',
            currency: 'MW',
            minimumFractionDigits: 0
        }).format(amount || 0);
    };

    const formatPercentage = (value) => {
        return `${(value || 0).toFixed(1)}%`;
    };

    const generateReport = (e) => {
        e.preventDefault();
        setIsGenerating(true);
        setGeneratedReport(null);

        // Set date range based on selection
        let startDate = data.startDate;
        let endDate = data.endDate;

        if (data.date_range !== 'custom') {
            const now = new Date();
            const ranges = {
                'last_week': { days: 7 },
                'last_month': { days: 30 },
                'last_quarter': { days: 90 },
                'last_year': { days: 365 }
            };

            const range = ranges[data.date_range];
            if (range) {
                endDate = now.toISOString().split('T')[0];
                startDate = new Date(now.getTime() - (range.days * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];
            }
        }

        // If PDF format is selected, handle differently
        if (data.format === 'pdf') {
            // Create a form and submit it to download PDF
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/reports/generate';
            form.target = '_blank'; // Open in new tab

            // Add CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
            }

            // Add form data
            const formData = {
                reportType: data.reportType,
                startDate: startDate,
                endDate: endDate,
                format: 'pdf'
            };

            Object.keys(formData).forEach(key => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = formData[key];
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);

            setIsGenerating(false);
        } else {
            // For preview, use Inertia
            post('/admin/reports/generate', {
                data: {
                    reportType: data.reportType,
                    startDate: startDate,
                    endDate: endDate,
                    format: data.format
                },
                onSuccess: (response) => {
                    setGeneratedReport(response.props.reportData || { message: 'Report generated successfully!' });
                    setIsGenerating(false);
                },
                onError: (errors) => {
                    console.error('Report generation failed:', errors);
                    setIsGenerating(false);
                },
                onFinish: () => {
                    setIsGenerating(false);
                }
            });
        }
    };

    const reportTypes = [
        { value: 'financial', label: 'Financial Summary', description: 'Comprehensive financial overview' },
        { value: 'members', label: 'Member Report', description: 'Member statistics and demographics' },
        { value: 'loans', label: 'Loan Performance', description: 'Loan disbursement and repayment analysis' },
        { value: 'contributions', label: 'Contributions Report', description: 'Member contribution tracking' },
        { value: 'equipment', label: 'Equipment Usage', description: 'Equipment rental and utilization' },
    ];

    return (
        <AdminLayout auth={auth}>
            <Head title="Reports & Analytics - Admin" />
            
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-7xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
                                <p className="mt-2 text-gray-600">Generate comprehensive reports and view analytics</p>
                            </div>
                            <Link
                                href="/dashboard/admin"
                                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                ← Back to Dashboard
                            </Link>
                        </div>
                    </div>

                    {/* Quick Stats Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-blue-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">Total Members</h3>
                                    <p className="text-3xl font-bold text-blue-600">{stats?.members?.total || 0}</p>
                                    <p className="text-sm text-gray-500">
                                        {stats?.members?.new_this_month || 0} new this month
                                    </p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-green-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">Total Savings</h3>
                                    <p className="text-3xl font-bold text-green-600">
                                        {formatCurrency(stats?.financial?.total_savings || 0)}
                                    </p>
                                    <p className="text-sm text-gray-500">Member savings balance</p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-purple-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">Outstanding Loans</h3>
                                    <p className="text-3xl font-bold text-purple-600">
                                        {formatCurrency(stats?.financial?.outstanding_loans || 0)}
                                    </p>
                                    <p className="text-sm text-gray-500">Active loan balance</p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-yellow-500">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-gray-600">Pending Actions</h3>
                                    <p className="text-3xl font-bold text-yellow-600">
                                        {(stats?.activities?.pending_loans || 0) + (stats?.activities?.pending_equipment_requests || 0)}
                                    </p>
                                    <p className="text-sm text-gray-500">Loans & equipment requests</p>
                                </div>
                                <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Report Generation */}
                        <div className="lg:col-span-1">
                            <div className="bg-white rounded-xl shadow-lg p-6">
                                <h3 className="text-xl font-bold text-gray-900 mb-6">Generate Report</h3>
                                
                                <form onSubmit={generateReport} className="space-y-6">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Report Type
                                        </label>
                                        <select
                                            value={data.reportType}
                                            onChange={(e) => setData('reportType', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        >
                                            {reportTypes.map((type) => (
                                                <option key={type.value} value={type.value}>
                                                    {type.label}
                                                </option>
                                            ))}
                                        </select>
                                        <p className="mt-1 text-sm text-gray-500">
                                            {reportTypes.find(t => t.value === data.reportType)?.description}
                                        </p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Date Range
                                        </label>
                                        <select
                                            value={data.date_range}
                                            onChange={(e) => setData('date_range', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        >
                                            <option value="last_week">Last Week</option>
                                            <option value="last_month">Last Month</option>
                                            <option value="last_quarter">Last Quarter</option>
                                            <option value="last_year">Last Year</option>
                                            <option value="custom">Custom Range</option>
                                        </select>
                                    </div>

                                    {data.date_range === 'custom' && (
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Start Date
                                                </label>
                                                <input
                                                    type="date"
                                                    value={data.startDate}
                                                    onChange={(e) => setData('startDate', e.target.value)}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    End Date
                                                </label>
                                                <input
                                                    type="date"
                                                    value={data.endDate}
                                                    onChange={(e) => setData('endDate', e.target.value)}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                />
                                            </div>
                                        </div>
                                    )}

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Export Format
                                        </label>
                                        <select
                                            value={data.format}
                                            onChange={(e) => setData('format', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        >
                                            <option value="pdf">PDF Document</option>
                                            <option value="excel">Excel Spreadsheet</option>
                                            <option value="csv">CSV File</option>
                                        </select>
                                    </div>

                                    <button
                                        type="submit"
                                        disabled={processing || isGenerating}
                                        className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {(processing || isGenerating) ? (
                                            <div className="flex items-center justify-center">
                                                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                Generating Report...
                                            </div>
                                        ) : (
                                            'Generate Report'
                                        )}
                                    </button>
                                </form>

                                {/* Display Errors */}
                                {errors && Object.keys(errors).length > 0 && (
                                    <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                                        <div className="text-sm text-red-600">
                                            <strong>Error generating report:</strong>
                                            <ul className="mt-2 list-disc list-inside">
                                                {Object.values(errors).map((error, index) => (
                                                    <li key={index}>{error}</li>
                                                ))}
                                            </ul>
                                        </div>
                                    </div>
                                )}

                                {/* Display Generated Report */}
                                {generatedReport && (
                                    <div className="mt-6">
                                        <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
                                            <div className="text-sm text-green-600">
                                                <strong>✅ Report Generated Successfully!</strong>
                                                <p className="mt-1">{generatedReport.message}</p>
                                            </div>
                                        </div>

                                        {/* Professional Report Preview */}
                                        <div className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden">
                                            {/* Report Header */}
                                            <div className="bg-gradient-to-r from-green-600 to-green-700 px-8 py-6 text-white">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <h1 className="text-2xl font-bold">Siyamphanje Cooperative</h1>
                                                        <p className="text-green-100 mt-1">Agricultural Cooperative Society</p>
                                                    </div>
                                                    <div className="text-right">
                                                        <div className="text-sm text-green-100">Report Generated</div>
                                                        <div className="text-lg font-semibold">{generatedReport.dateGenerated}</div>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Report Title Section */}
                                            <div className="px-8 py-6 bg-gray-50 border-b border-gray-200">
                                                <h2 className="text-xl font-bold text-gray-900 mb-2">{generatedReport.title}</h2>
                                                <div className="flex items-center space-x-6 text-sm text-gray-600">
                                                    <div className="flex items-center">
                                                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                                        </svg>
                                                        Period: {generatedReport.period}
                                                    </div>
                                                    <div className="flex items-center">
                                                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                                        </svg>
                                                        Generated at: {generatedReport.generatedAt}
                                                    </div>
                                                    <div className="flex items-center">
                                                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                                        </svg>
                                                        Report Type: {generatedReport.reportType?.toUpperCase()}
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Report Content */}
                                            <div className="px-8 py-6">
                                                {generatedReport.reportType === 'financial' && (
                                                    <div className="space-y-8">
                                                        {/* Executive Summary */}
                                                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
                                                            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                                                                <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                                Executive Summary
                                                            </h3>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Financial Health Status</div>
                                                                    <div className={`text-2xl font-bold ${(generatedReport.data?.netBalance || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                                        {(generatedReport.data?.netBalance || 0) >= 0 ? 'POSITIVE' : 'NEGATIVE'}
                                                                    </div>
                                                                    <div className="text-sm text-gray-500">
                                                                        Net Balance: {formatCurrency(generatedReport.data?.netBalance || 0)}
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Total Assets</div>
                                                                    <div className="text-2xl font-bold text-blue-600">
                                                                        {formatCurrency((generatedReport.data?.totalSavings || 0) + (generatedReport.data?.totalContributions || 0))}
                                                                    </div>
                                                                    <div className="text-sm text-gray-500">
                                                                        Savings + Contributions
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Financial Metrics */}
                                                        <div>
                                                            <h3 className="text-lg font-bold text-gray-900 mb-6 flex items-center">
                                                                <svg className="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                                                </svg>
                                                                Financial Breakdown
                                                            </h3>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                                {/* Assets Section */}
                                                                <div className="bg-white border-2 border-green-200 rounded-lg p-6 shadow-sm">
                                                                    <div className="flex items-center justify-between mb-4">
                                                                        <h4 className="font-semibold text-green-800">ASSETS</h4>
                                                                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                                            <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                                                                            </svg>
                                                                        </div>
                                                                    </div>
                                                                    <div className="space-y-3">
                                                                        <div className="flex justify-between items-center">
                                                                            <span className="text-sm text-gray-600">Member Savings</span>
                                                                            <span className="font-semibold text-green-700">
                                                                                {formatCurrency(generatedReport.data?.totalSavings || 0)}
                                                                            </span>
                                                                        </div>
                                                                        <div className="flex justify-between items-center">
                                                                            <span className="text-sm text-gray-600">Contributions</span>
                                                                            <span className="font-semibold text-green-700">
                                                                                {formatCurrency(generatedReport.data?.totalContributions || 0)}
                                                                            </span>
                                                                        </div>
                                                                        <div className="border-t border-green-200 pt-2">
                                                                            <div className="flex justify-between items-center">
                                                                                <span className="font-medium text-green-800">Total Assets</span>
                                                                                <span className="font-bold text-lg text-green-800">
                                                                                    {formatCurrency((generatedReport.data?.totalSavings || 0) + (generatedReport.data?.totalContributions || 0))}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                {/* Liabilities Section */}
                                                                <div className="bg-white border-2 border-purple-200 rounded-lg p-6 shadow-sm">
                                                                    <div className="flex items-center justify-between mb-4">
                                                                        <h4 className="font-semibold text-purple-800">LIABILITIES</h4>
                                                                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                                                            <svg className="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                                                                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                                                            </svg>
                                                                        </div>
                                                                    </div>
                                                                    <div className="space-y-3">
                                                                        <div className="flex justify-between items-center">
                                                                            <span className="text-sm text-gray-600">Outstanding Loans</span>
                                                                            <span className="font-semibold text-purple-700">
                                                                                {formatCurrency(generatedReport.data?.totalLoans || 0)}
                                                                            </span>
                                                                        </div>
                                                                        <div className="flex justify-between items-center">
                                                                            <span className="text-sm text-gray-600">Operating Expenses</span>
                                                                            <span className="font-semibold text-purple-700">
                                                                                {formatCurrency(generatedReport.data?.totalExpenses || 0)}
                                                                            </span>
                                                                        </div>
                                                                        <div className="border-t border-purple-200 pt-2">
                                                                            <div className="flex justify-between items-center">
                                                                                <span className="font-medium text-purple-800">Total Liabilities</span>
                                                                                <span className="font-bold text-lg text-purple-800">
                                                                                    {formatCurrency((generatedReport.data?.totalLoans || 0) + (generatedReport.data?.totalExpenses || 0))}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                {/* Net Position */}
                                                                <div className={`bg-white border-2 rounded-lg p-6 shadow-sm ${(generatedReport.data?.netBalance || 0) >= 0 ? 'border-green-200' : 'border-red-200'}`}>
                                                                    <div className="flex items-center justify-between mb-4">
                                                                        <h4 className={`font-semibold ${(generatedReport.data?.netBalance || 0) >= 0 ? 'text-green-800' : 'text-red-800'}`}>NET POSITION</h4>
                                                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${(generatedReport.data?.netBalance || 0) >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                                                                            <svg className={`w-4 h-4 ${(generatedReport.data?.netBalance || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`} fill="currentColor" viewBox="0 0 20 20">
                                                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                            </svg>
                                                                        </div>
                                                                    </div>
                                                                    <div className="text-center">
                                                                        <div className={`text-3xl font-bold mb-2 ${(generatedReport.data?.netBalance || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                                            {formatCurrency(generatedReport.data?.netBalance || 0)}
                                                                        </div>
                                                                        <div className="text-sm text-gray-500">
                                                                            Assets - Liabilities
                                                                        </div>
                                                                        <div className={`text-xs mt-2 px-2 py-1 rounded-full ${(generatedReport.data?.netBalance || 0) >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                                                            {(generatedReport.data?.netBalance || 0) >= 0 ? 'Healthy Financial Position' : 'Requires Attention'}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}

                                                {generatedReport.reportType === 'members' && (
                                                    <div className="space-y-8">
                                                        {/* Member Overview */}
                                                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
                                                            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                                                                <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                                                                </svg>
                                                                Membership Overview
                                                            </h3>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Total Membership</div>
                                                                    <div className="text-3xl font-bold text-blue-600">{generatedReport.data?.totalMembers || 0}</div>
                                                                    <div className="text-sm text-gray-500">Registered Members</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Active Rate</div>
                                                                    <div className="text-3xl font-bold text-green-600">
                                                                        {(generatedReport.data?.totalMembers || 0) > 0 ? Math.round(((generatedReport.data?.activeMembers || 0) / (generatedReport.data?.totalMembers || 1)) * 100) : 0}%
                                                                    </div>
                                                                    <div className="text-sm text-gray-500">Member Participation</div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Detailed Member Statistics */}
                                                        <div>
                                                            <h3 className="text-lg font-bold text-gray-900 mb-6 flex items-center">
                                                                <svg className="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                </svg>
                                                                Member Statistics
                                                            </h3>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                                                <div className="bg-white border-2 border-blue-200 rounded-lg p-6 text-center shadow-sm">
                                                                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-3xl font-bold text-blue-800 mb-2">{generatedReport.data?.totalMembers || 0}</div>
                                                                    <div className="text-sm font-medium text-blue-600">Total Members</div>
                                                                    <div className="text-xs text-gray-500 mt-1">All registered members</div>
                                                                </div>

                                                                <div className="bg-white border-2 border-green-200 rounded-lg p-6 text-center shadow-sm">
                                                                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-3xl font-bold text-green-800 mb-2">{generatedReport.data?.activeMembers || 0}</div>
                                                                    <div className="text-sm font-medium text-green-600">Active Members</div>
                                                                    <div className="text-xs text-gray-500 mt-1">Currently participating</div>
                                                                </div>

                                                                <div className="bg-white border-2 border-red-200 rounded-lg p-6 text-center shadow-sm">
                                                                    <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-3xl font-bold text-red-800 mb-2">{generatedReport.data?.inactiveMembers || 0}</div>
                                                                    <div className="text-sm font-medium text-red-600">Inactive Members</div>
                                                                    <div className="text-xs text-gray-500 mt-1">Not currently active</div>
                                                                </div>

                                                                <div className="bg-white border-2 border-purple-200 rounded-lg p-6 text-center shadow-sm">
                                                                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-3xl font-bold text-purple-800 mb-2">{generatedReport.data?.newMembersThisPeriod || 0}</div>
                                                                    <div className="text-sm font-medium text-purple-600">New This Period</div>
                                                                    <div className="text-xs text-gray-500 mt-1">Recent additions</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}

                                                {generatedReport.reportType === 'loans' && (
                                                    <div className="space-y-8">
                                                        {/* Loan Performance Overview */}
                                                        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-lg border border-purple-200">
                                                            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                                                                <svg className="w-5 h-5 mr-2 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                                                </svg>
                                                                Loan Portfolio Overview
                                                            </h3>
                                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Total Loans</div>
                                                                    <div className="text-3xl font-bold text-purple-600">{generatedReport.data?.totalLoansCount || 0}</div>
                                                                    <div className="text-sm text-gray-500">Loan applications</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Portfolio Value</div>
                                                                    <div className="text-3xl font-bold text-blue-600">
                                                                        {formatCurrency(generatedReport.data?.totalLoans || 0)}
                                                                    </div>
                                                                    <div className="text-sm text-gray-500">Total disbursed</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Repayment Rate</div>
                                                                    <div className="text-3xl font-bold text-green-600">
                                                                        {generatedReport.data?.repaymentRate || 0}%
                                                                    </div>
                                                                    <div className="text-sm text-gray-500">Fully paid loans</div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Detailed Loan Metrics */}
                                                        <div>
                                                            <h3 className="text-lg font-bold text-gray-900 mb-6 flex items-center">
                                                                <svg className="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                </svg>
                                                                Loan Performance Metrics
                                                            </h3>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                                                <div className="bg-white border-2 border-blue-200 rounded-lg p-6 shadow-sm">
                                                                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-center">
                                                                        <div className="text-2xl font-bold text-blue-800 mb-2">
                                                                            {formatCurrency(generatedReport.data?.totalLoans || 0)}
                                                                        </div>
                                                                        <div className="text-sm font-medium text-blue-600">Total Disbursed</div>
                                                                        <div className="text-xs text-gray-500 mt-1">All loans issued</div>
                                                                    </div>
                                                                </div>

                                                                <div className="bg-white border-2 border-green-200 rounded-lg p-6 shadow-sm">
                                                                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-center">
                                                                        <div className="text-2xl font-bold text-green-800 mb-2">
                                                                            {formatCurrency(generatedReport.data?.totalRepayments || 0)}
                                                                        </div>
                                                                        <div className="text-sm font-medium text-green-600">Total Repayments</div>
                                                                        <div className="text-xs text-gray-500 mt-1">Payments received</div>
                                                                    </div>
                                                                </div>

                                                                <div className="bg-white border-2 border-purple-200 rounded-lg p-6 shadow-sm">
                                                                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-center">
                                                                        <div className="text-2xl font-bold text-purple-800 mb-2">
                                                                            {formatCurrency(generatedReport.data?.outstandingBalance || 0)}
                                                                        </div>
                                                                        <div className="text-sm font-medium text-purple-600">Outstanding</div>
                                                                        <div className="text-xs text-gray-500 mt-1">Amount pending</div>
                                                                    </div>
                                                                </div>

                                                                <div className="bg-white border-2 border-yellow-200 rounded-lg p-6 shadow-sm">
                                                                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-center">
                                                                        <div className="text-2xl font-bold text-yellow-800 mb-2">
                                                                            {formatCurrency(generatedReport.data?.averageLoanSize || 0)}
                                                                        </div>
                                                                        <div className="text-sm font-medium text-yellow-600">Average Size</div>
                                                                        <div className="text-xs text-gray-500 mt-1">Per loan</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Repayment Status Breakdown */}
                                                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                                                            <h4 className="text-md font-semibold text-gray-800 mb-4">Repayment Status Breakdown</h4>
                                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                <div className="text-center p-4 bg-green-50 rounded-lg">
                                                                    <div className="text-2xl font-bold text-green-600">{generatedReport.data?.fullyPaidLoans || 0}</div>
                                                                    <div className="text-sm text-green-700">Fully Paid</div>
                                                                </div>
                                                                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                                                                    <div className="text-2xl font-bold text-yellow-600">{generatedReport.data?.partialPaymentLoans || 0}</div>
                                                                    <div className="text-sm text-yellow-700">Partial Payment</div>
                                                                </div>
                                                                <div className="text-center p-4 bg-red-50 rounded-lg">
                                                                    <div className="text-2xl font-bold text-red-600">{generatedReport.data?.noPaymentLoans || 0}</div>
                                                                    <div className="text-sm text-red-700">No Payment</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}

                                                {generatedReport.reportType === 'contributions' && (
                                                    <div className="space-y-6">
                                                        <h4 className="text-md font-semibold text-gray-800 mb-4">Contribution Analysis</h4>
                                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                            <div className="bg-green-50 p-4 rounded-lg">
                                                                <div className="text-sm text-green-600 font-medium">Total Contributions</div>
                                                                <div className="text-2xl font-bold text-green-800">
                                                                    {formatCurrency(generatedReport.data?.totalContributions || 0)}
                                                                </div>
                                                            </div>
                                                            <div className="bg-blue-50 p-4 rounded-lg">
                                                                <div className="text-sm text-blue-600 font-medium">Number of Contributors</div>
                                                                <div className="text-2xl font-bold text-blue-800">
                                                                    {generatedReport.data?.numberOfContributors || 0}
                                                                </div>
                                                            </div>
                                                            <div className="bg-purple-50 p-4 rounded-lg">
                                                                <div className="text-sm text-purple-600 font-medium">Average Contribution</div>
                                                                <div className="text-2xl font-bold text-purple-800">
                                                                    {formatCurrency(generatedReport.data?.averageContribution || 0)}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}

                                                {generatedReport.reportType === 'equipment' && (
                                                    <div className="space-y-8">
                                                        {/* Equipment Overview */}
                                                        <div className="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-lg border border-orange-200">
                                                            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                                                                <svg className="w-5 h-5 mr-2 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                                                </svg>
                                                                Equipment Portfolio Overview
                                                            </h3>
                                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Total Equipment</div>
                                                                    <div className="text-3xl font-bold text-orange-600">{generatedReport.data?.totalEquipment || 0}</div>
                                                                    <div className="text-sm text-gray-500">Available: {generatedReport.data?.availableEquipment || 0}</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Portfolio Value</div>
                                                                    <div className="text-3xl font-bold text-blue-600">
                                                                        {formatCurrency(generatedReport.data?.totalEquipmentValue || 0)}
                                                                    </div>
                                                                    <div className="text-sm text-gray-500">Total asset value</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm text-gray-600 mb-2">Rental Revenue</div>
                                                                    <div className="text-3xl font-bold text-green-600">
                                                                        {formatCurrency(generatedReport.data?.rentalRevenue || 0)}
                                                                    </div>
                                                                    <div className="text-sm text-gray-500">Period earnings</div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Equipment Usage Metrics */}
                                                        <div>
                                                            <h3 className="text-lg font-bold text-gray-900 mb-6 flex items-center">
                                                                <svg className="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                </svg>
                                                                Equipment Usage Statistics
                                                            </h3>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                                                <div className="bg-white border-2 border-orange-200 rounded-lg p-6 text-center shadow-sm">
                                                                    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-3xl font-bold text-orange-800 mb-2">{generatedReport.data?.totalEquipment || 0}</div>
                                                                    <div className="text-sm font-medium text-orange-600">Total Equipment</div>
                                                                    <div className="text-xs text-gray-500 mt-1">All equipment items</div>
                                                                </div>

                                                                <div className="bg-white border-2 border-green-200 rounded-lg p-6 text-center shadow-sm">
                                                                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-3xl font-bold text-green-800 mb-2">{generatedReport.data?.availableEquipment || 0}</div>
                                                                    <div className="text-sm font-medium text-green-600">Available</div>
                                                                    <div className="text-xs text-gray-500 mt-1">Ready for rental</div>
                                                                </div>

                                                                <div className="bg-white border-2 border-blue-200 rounded-lg p-6 text-center shadow-sm">
                                                                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-3xl font-bold text-blue-800 mb-2">{generatedReport.data?.totalRequests || 0}</div>
                                                                    <div className="text-sm font-medium text-blue-600">Total Requests</div>
                                                                    <div className="text-xs text-gray-500 mt-1">All time requests</div>
                                                                </div>

                                                                <div className="bg-white border-2 border-purple-200 rounded-lg p-6 text-center shadow-sm">
                                                                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                        <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div className="text-3xl font-bold text-purple-800 mb-2">
                                                                        {formatCurrency(generatedReport.data?.rentalRevenue || 0)}
                                                                    </div>
                                                                    <div className="text-sm font-medium text-purple-600">Revenue</div>
                                                                    <div className="text-xs text-gray-500 mt-1">Period earnings</div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Equipment Performance */}
                                                        {generatedReport.data?.mostRequestedEquipment && (
                                                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                                                <h4 className="text-md font-semibold text-gray-800 mb-4">Equipment Performance Highlights</h4>
                                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                                    <div className="bg-blue-50 p-4 rounded-lg">
                                                                        <div className="text-sm text-blue-600 font-medium mb-2">Most Requested Equipment</div>
                                                                        <div className="text-lg font-bold text-blue-800">
                                                                            {generatedReport.data.mostRequestedEquipment.name}
                                                                        </div>
                                                                        <div className="text-sm text-gray-600">
                                                                            {generatedReport.data.mostRequestedEquipment.total_requests} requests
                                                                        </div>
                                                                    </div>
                                                                    {generatedReport.data?.highestRevenueEquipment && (
                                                                        <div className="bg-green-50 p-4 rounded-lg">
                                                                            <div className="text-sm text-green-600 font-medium mb-2">Highest Revenue Generator</div>
                                                                            <div className="text-lg font-bold text-green-800">
                                                                                {generatedReport.data.highestRevenueEquipment.name}
                                                                            </div>
                                                                            <div className="text-sm text-gray-600">
                                                                                {formatCurrency(generatedReport.data.highestRevenueEquipment.period_revenue)} earned
                                                                            </div>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </div>

                                            {/* Print/Export Actions */}
                                            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
                                                <div className="flex justify-between items-center">
                                                    <div className="text-sm text-gray-500">
                                                        Report generated at: {generatedReport.generatedAt}
                                                    </div>
                                                    <div className="flex space-x-3">
                                                        <button
                                                            onClick={() => window.print()}
                                                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                                        >
                                                            🖨️ Print Report
                                                        </button>
                                                        <button
                                                            onClick={() => setGeneratedReport(null)}
                                                            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                                                        >
                                                            Generate New Report
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Analytics Dashboard */}
                        <div className="lg:col-span-2">
                            <div className="space-y-8">
                                {/* Monthly Contributions Chart */}
                                <div className="bg-white rounded-xl shadow-lg p-6">
                                    <h3 className="text-xl font-bold text-gray-900 mb-6">Monthly Contributions</h3>
                                    <div className="space-y-4">
                                        {recentData?.monthly_contributions?.map((month, index) => (
                                            <div key={index} className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="flex justify-between items-center mb-1">
                                                        <span className="text-sm font-medium text-gray-700">{month.month}</span>
                                                        <span className="text-sm font-bold text-green-600">
                                                            {formatCurrency(month.amount)}
                                                        </span>
                                                    </div>
                                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                                        <div 
                                                            className="bg-green-600 h-2 rounded-full" 
                                                            style={{ 
                                                                width: `${Math.min((month.amount / Math.max(...(recentData?.monthly_contributions?.map(m => m.amount) || [1]))) * 100, 100)}%` 
                                                            }}
                                                        ></div>
                                                    </div>
                                                    <span className="text-xs text-gray-500">{month.count} contributions</span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Loan Performance */}
                                <div className="bg-white rounded-xl shadow-lg p-6">
                                    <h3 className="text-xl font-bold text-gray-900 mb-6">Loan Performance</h3>
                                    <div className="grid grid-cols-2 gap-6">
                                        <div className="text-center">
                                            <div className="text-3xl font-bold text-blue-600">
                                                {formatCurrency(recentData?.loan_performance?.total_disbursed || 0)}
                                            </div>
                                            <div className="text-sm text-gray-500">Total Disbursed</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-3xl font-bold text-green-600">
                                                {formatCurrency(recentData?.loan_performance?.total_repaid || 0)}
                                            </div>
                                            <div className="text-sm text-gray-500">Total Repaid</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-3xl font-bold text-purple-600">
                                                {formatCurrency(recentData?.loan_performance?.average_loan_size || 0)}
                                            </div>
                                            <div className="text-sm text-gray-500">Average Loan Size</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-3xl font-bold text-yellow-600">
                                                {formatPercentage(recentData?.loan_performance?.completion_rate || 0)}
                                            </div>
                                            <div className="text-sm text-gray-500">Completion Rate</div>
                                        </div>
                                    </div>
                                </div>

                                {/* Member Growth */}
                                <div className="bg-white rounded-xl shadow-lg p-6">
                                    <h3 className="text-xl font-bold text-gray-900 mb-6">Member Growth (Last 6 Months)</h3>
                                    <div className="space-y-3">
                                        {recentData?.member_growth?.slice(-6).map((month, index) => (
                                            <div key={index} className="flex items-center justify-between">
                                                <span className="text-sm font-medium text-gray-700">{month.month}</span>
                                                <div className="flex items-center space-x-4">
                                                    <span className="text-sm text-green-600">+{month.new_members} new</span>
                                                    <span className="text-sm font-bold text-gray-900">{month.total_members} total</span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
