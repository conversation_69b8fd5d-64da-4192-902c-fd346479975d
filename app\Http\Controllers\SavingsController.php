<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SavingsAccount;
use App\Models\Contribution;
use Inertia\Inertia;

class SavingsController extends Controller
{
    public function showDepositForm()
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile associated with this user.']);
        }

        $savingsAccounts = SavingsAccount::where('farmer_id', $farmer->id)->get();

        return Inertia::render('Savings/Deposit', [
            'auth' => ['user' => $user],
            'savingsAccounts' => $savingsAccounts
        ]);
    }

    public function deposit(Request $request)
    {
        $request->validate([
            'savings_account_id' => 'required|exists:savings_accounts,id',
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|string',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile associated with this user.']);
        }

        $savingsAccount = SavingsAccount::where('id', $request->savings_account_id)
            ->where('farmer_id', $farmer->id)
            ->firstOrFail();

        // Update account balance
        $savingsAccount->balance += $request->amount;
        $savingsAccount->save();

        // Create transaction record (if you have a SavingsTransaction model)
        try {
            \App\Models\SavingsTransaction::create([
                'savings_account_id' => $savingsAccount->id,
                'transaction_type' => 'deposit',
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'reference_number' => $request->reference_number,
                'notes' => $request->notes,
                'processed_by' => $user->id,
            ]);
        } catch (\Exception $e) {
            // If SavingsTransaction model doesn't exist, continue without it
        }

        return redirect()->back()->with('success', 'Deposit of ' . number_format($request->amount) . ' MW successful!');
    }

    public function store(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:255',
        ]);

        $user = $request->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile associated with this user.']);
        }

        $savingsAccount = SavingsAccount::firstOrCreate(
            ['farmer_id' => $farmer->id],
            ['account_number' => 'SA-' . uniqid(), 'balance' => 0, 'opening_date' => now(), 'status' => 'active']
        );

        $savingsAccount->balance += $request->amount;
        $savingsAccount->save();

        Contribution::create([
            'farmer_id' => $farmer->id,
            'amount' => $request->amount,
            'contribution_date' => now(),
            'payment_method' => 'deposit', // Or from request if applicable
            'notes' => $request->description,
            'recorded_by' => $user->id,
        ]);

        return redirect()->back()->with('success', 'Deposit successful!');
    }
}
