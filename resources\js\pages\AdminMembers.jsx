import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';

import { Table, Space, Button, Tag, Input, Card } from 'antd';
import { PlusOutlined, SearchOutlined, EditOutlined } from '@ant-design/icons';

export default function MemberManagement({ auth, members }) {
    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            sorter: (a, b) => a.name.localeCompare(b.name),
        },
        {
            title: 'Email',
            dataIndex: 'email',
            key: 'email',
        },
        {
            title: 'Farm Size',
            dataIndex: 'land_size',
            key: 'land_size',
            render: (size) => `${size} acres`,
            sorter: (a, b) => a.land_size - b.land_size,
        },
        // {
        //     title: 'Illovo Ref',
        //     dataIndex: 'illovo_reference',
        //     key: 'illovo_reference',
        // },
        {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            render: (status) => (
                <Tag color={status === 'active' ? 'green' : 'orange'}>
                    {status.toUpperCase()}
                </Tag>
            ),
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (_, record) => (
                <Space size="middle">
                    <Link href={route('admin.members.edit', record.id)}>
                        <Button icon={<EditOutlined />}>Edit</Button>
                    </Link>
                </Space>
            ),
        },
    ];

    return (
        <>
            user={auth.user}
            header={<h2 className="text-xl font-semibold">Member Management</h2>}
        
            <Head title="Manage Members" />
            
            <div className="space-y-6">
                <Card>
                    <div className="flex justify-between items-center mb-6">
                        <Input 
                            placeholder="Search members..." 
                            prefix={<SearchOutlined />} 
                            style={{ width: 300 }}
                        />
                        <Link href={route('admin.members.create')}>
                            <Button type="primary" icon={<PlusOutlined />}>
                                Add New Member
                            </Button>
                        </Link>
                    </div>

                    <Table 
                        columns={columns} 
                        dataSource={members} 
                        rowKey="id"
                        pagination={{ pageSize: 10 }}
                        bordered
                    />
                </Card>
            </div>
        </>
    );
}