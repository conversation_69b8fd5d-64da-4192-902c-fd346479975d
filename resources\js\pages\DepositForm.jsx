import React, { useState } from 'react';
import MemberLayout from '@/Layouts/MemberLayout';
import { Head, usePage } from '@inertiajs/react';
import { Inertia } from '@inertiajs/inertia';

export default function DepositForm({ auth }) {
    const [formData, setFormData] = useState({
        amount: '',
        description: '',
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        Inertia.post(route('savings.deposit'), formData, {
            onSuccess: () => {
                alert('Deposit submitted successfully!');
                setFormData({ amount: '', description: '' });
            },
            onError: (errors) => {
                console.error('Error submitting deposit:', errors);
                alert('Error submitting deposit. Please check console for details.');
            },
        });
    };

    return (
        <MemberLayout user={auth.user} header="Make a Deposit">
            <Head title="Make a Deposit" />
            <div className="container mx-auto p-4">
                <h1 className="text-2xl font-bold mb-4">Make a Deposit</h1>
                <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md">
                    <div className="mb-4">
                        <label htmlFor="amount" className="block text-gray-700 text-sm font-bold mb-2">Deposit Amount</label>
                        <input
                            type="number"
                            name="amount"
                            id="amount"
                            value={formData.amount}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="description" className="block text-gray-700 text-sm font-bold mb-2">Description (Optional)</label>
                        <textarea
                            name="description"
                            id="description"
                            value={formData.description}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            rows="4"
                        ></textarea>
                    </div>
                    <div className="flex items-center justify-between">
                        <button
                            type="submit"
                            className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Submit Deposit
                        </button>
                    </div>
                </form>
            </div>
        </MemberLayout>
    );
}