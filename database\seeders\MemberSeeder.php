<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\Role;
use App\Models\User;

class MemberSeeder extends Seeder
{
    public function run(): void
    {
        // Create member  role if it doesn't exist
        $memberRole = Role::firstOrCreate(
            ['name' => 'member'],
            ['description' => 'Member1']
        );

        // Create member user if it doesn't exist
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Member User',
                'password' => Hash::make('cosmas23'), // Change this to a secure password!
                'role_id' => $memberRole->id,
                'is_active' => true,
            ]
        );
    }
}
