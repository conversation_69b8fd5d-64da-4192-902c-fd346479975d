<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class SessionSecurity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();
            
            // Store user agent and IP in session for security
            $sessionUserAgent = session('user_agent');
            $sessionIpAddress = session('ip_address');
            $currentUserAgent = $request->userAgent();
            $currentIpAddress = $request->ip();

            // If this is the first request, store the user agent and IP
            if (!$sessionUserAgent || !$sessionIpAddress) {
                session([
                    'user_agent' => $currentUserAgent,
                    'ip_address' => $currentIpAddress,
                    'login_time' => now(),
                ]);
            } else {
                // Check for session hijacking attempts
                $userAgentChanged = $sessionUserAgent !== $currentUserAgent;
                $ipChanged = $sessionIpAddress !== $currentIpAddress;

                if ($userAgentChanged || $ipChanged) {
                    Log::warning('Potential session hijacking detected', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'session_user_agent' => $sessionUserAgent,
                        'current_user_agent' => $currentUserAgent,
                        'session_ip' => $sessionIpAddress,
                        'current_ip' => $currentIpAddress,
                        'user_agent_changed' => $userAgentChanged,
                        'ip_changed' => $ipChanged,
                        'url' => $request->url(),
                    ]);

                    // Force logout for security
                    auth()->logout();
                    session()->invalidate();
                    session()->regenerateToken();

                    return redirect()->route('login')->with('error', 'Session security violation detected. Please log in again.');
                }
            }

            // Regenerate session ID periodically for security
            $loginTime = session('login_time');
            if ($loginTime && now()->diffInMinutes($loginTime) > 60) {
                session()->regenerate();
                session(['login_time' => now()]);
            }
        }

        return $next($request);
    }
}
