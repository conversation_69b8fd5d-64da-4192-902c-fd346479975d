<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('farm_equipment', function (Blueprint $table) {
            $table->decimal('rental_cost_per_day', 8, 2)->nullable()->after('value');
            $table->boolean('available_for_rent')->default(true)->after('is_available');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('farm_equipment', function (Blueprint $table) {
            $table->dropColumn(['rental_cost_per_day', 'available_for_rent']);
        });
    }
};
