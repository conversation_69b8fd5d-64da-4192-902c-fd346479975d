<?php

namespace App\Services;

use App\Models\SmsNotification;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class SmsService
{
    protected $provider;
    protected $apiKey;
    protected $username;
    protected $senderId;

    public function __construct()
    {
        $this->provider = config('sms.default_provider', 'africastalking');
        $this->apiKey = config('sms.providers.africastalking.api_key');
        $this->username = config('sms.providers.africastalking.username');
        $this->senderId = config('sms.providers.africastalking.sender_id', 'SIYAMPHANJE');
    }

    /**
     * Send SMS notification.
     */
    public function sendSms($phoneNumber, $message, $type = 'general', $recipientUserId = null, $metadata = null)
    {
        // Create SMS record
        $sms = SmsNotification::create([
            'phone_number' => $this->formatPhoneNumber($phoneNumber),
            'message' => $message,
            'type' => $type,
            'status' => 'pending',
            'provider' => $this->provider,
            'sent_by' => auth()->id(),
            'recipient_user_id' => $recipientUserId,
            'metadata' => $metadata,
        ]);

        try {
            // Send SMS based on provider
            $result = $this->sendViaPrimaryProvider($sms);
            
            if ($result['success']) {
                $sms->markAsSent($result['message_id'], $result['response']);
                Log::info('SMS sent successfully', [
                    'sms_id' => $sms->id,
                    'phone' => $phoneNumber,
                    'type' => $type
                ]);
                return ['success' => true, 'sms_id' => $sms->id, 'message' => 'SMS sent successfully'];
            } else {
                $sms->markAsFailed($result['error'], $result['response']);
                Log::error('SMS sending failed', [
                    'sms_id' => $sms->id,
                    'phone' => $phoneNumber,
                    'error' => $result['error']
                ]);
                return ['success' => false, 'error' => $result['error']];
            }
        } catch (\Exception $e) {
            $sms->markAsFailed($e->getMessage());
            Log::error('SMS sending exception', [
                'sms_id' => $sms->id,
                'phone' => $phoneNumber,
                'exception' => $e->getMessage()
            ]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send password SMS to new member.
     */
    public function sendPasswordSms($phoneNumber, $email, $password, $memberName, $userId = null)
    {
        $message = "Welcome to Siyamphanje Cooperative, {$memberName}! Your login details:\nEmail: {$email}\nPassword: {$password}\nLogin at: " . url('/login') . "\nPlease change your password after first login.";

        return $this->sendSms(
            $phoneNumber,
            $message,
            'password',
            $userId,
            ['email' => $email, 'member_name' => $memberName]
        );
    }

    /**
     * Send meeting reminder SMS.
     */
    public function sendMeetingReminderSms($phoneNumber, $meetingTitle, $meetingDate, $meetingTime, $location, $userId = null)
    {
        $message = "Meeting Reminder: {$meetingTitle}\nDate: {$meetingDate}\nTime: {$meetingTime}\nLocation: {$location}\nPlease confirm your attendance. - Siyamphanje Cooperative";
        
        return $this->sendSms(
            $phoneNumber, 
            $message, 
            'meeting', 
            $userId,
            ['meeting_title' => $meetingTitle, 'meeting_date' => $meetingDate]
        );
    }

    /**
     * Send general announcement SMS.
     */
    public function sendAnnouncementSms($phoneNumber, $title, $content, $userId = null)
    {
        $message = "ANNOUNCEMENT: {$title}\n{$content}\n- Siyamphanje Cooperative";
        
        return $this->sendSms(
            $phoneNumber, 
            $message, 
            'announcement', 
            $userId,
            ['title' => $title]
        );
    }

    /**
     * Send welcome SMS to new member.
     */
    public function sendWelcomeSms($phoneNumber, $memberName, $userId = null)
    {
        $message = "Welcome to Siyamphanje Cooperative, {$memberName}! We're excited to have you as a member. You'll receive your login details shortly. For support, contact us at " . config('app.support_phone', '+265-XXX-XXXX') . ".";

        return $this->sendSms(
            $phoneNumber,
            $message,
            'welcome',
            $userId,
            ['member_name' => $memberName]
        );
    }

    /**
     * Send bulk SMS to multiple recipients.
     */
    public function sendBulkSms($recipients, $message, $type = 'general')
    {
        $results = [];
        
        foreach ($recipients as $recipient) {
            $phoneNumber = is_array($recipient) ? $recipient['phone'] : $recipient;
            $userId = is_array($recipient) ? ($recipient['user_id'] ?? null) : null;
            
            $result = $this->sendSms($phoneNumber, $message, $type, $userId);
            $results[] = [
                'phone' => $phoneNumber,
                'success' => $result['success'],
                'error' => $result['error'] ?? null
            ];
        }
        
        return $results;
    }

    /**
     * Send SMS via primary provider (Africa's Talking simulation).
     */
    protected function sendViaPrimaryProvider($sms)
    {
        // For development/testing, we'll simulate the API call
        // In production, replace this with actual API calls
        
        if ($this->provider === 'mailersend') {
            return $this->sendViaMailerSend($sms);
        } elseif ($this->provider === 'africastalking') {
            return $this->sendViaAfricasTalking($sms);
        } elseif ($this->provider === 'twilio') {
            return $this->sendViaTwilio($sms);
        } else {
            return $this->simulateSmsProvider($sms);
        }
    }

    /**
     * Send SMS via MailerSend.
     */
    protected function sendViaMailerSend($sms)
    {
        try {
            $apiKey = config('sms.providers.mailersend.api_key');
            $smsFrom = config('sms.providers.mailersend.sms_from');

            if (!$apiKey) {
                return [
                    'success' => false,
                    'error' => 'MailerSend API key not configured',
                    'response' => null
                ];
            }

            // MailerSend SMS API endpoint
            $url = 'https://api.mailersend.com/v1/sms';

            // Prepare the data
            $data = [
                'from' => $smsFrom,
                'to' => [$sms->phone_number],
                'text' => $sms->message,
            ];

            // Make the API call
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest',
            ])->post($url, $data);

            if ($response->successful()) {
                $responseData = $response->json();

                return [
                    'success' => true,
                    'message_id' => $responseData['data']['id'] ?? uniqid('ms_'),
                    'response' => $response->body()
                ];
            } else {
                $errorData = $response->json();
                $errorMessage = $errorData['message'] ?? 'MailerSend API error';

                // Handle specific error cases
                if ($response->status() === 422) {
                    $errors = $errorData['errors'] ?? [];
                    if (!empty($errors)) {
                        $errorMessage = collect($errors)->flatten()->first() ?? $errorMessage;
                    }
                }

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'response' => $response->body()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'MailerSend service error: ' . $e->getMessage(),
                'response' => null
            ];
        }
    }

    /**
     * Send SMS via Africa's Talking (simulated for now).
     */
    protected function sendViaAfricasTalking($sms)
    {
        // Simulate API call for development
        // In production, use actual Africa's Talking API
        
        try {
            // Simulate success/failure based on phone number for testing
            $isTestFailure = str_contains($sms->phone_number, '999');
            
            if ($isTestFailure) {
                return [
                    'success' => false,
                    'error' => 'Invalid phone number',
                    'response' => 'Simulated failure for testing'
                ];
            }
            
            // Simulate successful response
            $messageId = 'AT_' . uniqid();
            
            return [
                'success' => true,
                'message_id' => $messageId,
                'response' => json_encode([
                    'SMSMessageData' => [
                        'Message' => 'Sent to 1/1 Total Cost: MWK 50',
                        'Recipients' => [
                            [
                                'statusCode' => 101,
                                'number' => $sms->phone_number,
                                'status' => 'Success',
                                'cost' => 'MWK 50',
                                'messageId' => $messageId
                            ]
                        ]
                    ]
                ])
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'response' => null
            ];
        }
    }

    /**
     * Send SMS via Twilio.
     */
    protected function sendViaTwilio($sms)
    {
        try {
            $accountSid = config('sms.providers.twilio.account_sid');
            $authToken = config('sms.providers.twilio.auth_token');
            $fromNumber = config('sms.providers.twilio.from_number');

            if (!$accountSid || !$authToken || !$fromNumber) {
                return [
                    'success' => false,
                    'error' => 'Twilio credentials not configured',
                    'response' => null
                ];
            }

            // Twilio API endpoint
            $url = "https://api.twilio.com/2010-04-01/Accounts/{$accountSid}/Messages.json";

            // Prepare the data
            $data = [
                'From' => $fromNumber,
                'To' => $sms->phone_number,
                'Body' => $sms->message,
            ];

            // For alphanumeric sender ID (if supported in region)
            if (!str_starts_with($fromNumber, '+')) {
                $data['From'] = $fromNumber; // Use as alphanumeric sender ID
            }

            // Make the API call using Laravel's HTTP client
            $response = Http::withBasicAuth($accountSid, $authToken)
                ->asForm()
                ->post($url, $data);

            if ($response->successful()) {
                $responseData = $response->json();

                return [
                    'success' => true,
                    'message_id' => $responseData['sid'] ?? null,
                    'response' => $response->body()
                ];
            } else {
                $errorData = $response->json();
                return [
                    'success' => false,
                    'error' => $errorData['message'] ?? 'Twilio API error',
                    'response' => $response->body()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Twilio service error: ' . $e->getMessage(),
                'response' => null
            ];
        }
    }

    /**
     * Simulate SMS provider for testing.
     */
    protected function simulateSmsProvider($sms)
    {
        // Simulate random success/failure for testing
        $success = rand(1, 10) > 2; // 80% success rate
        
        if ($success) {
            return [
                'success' => true,
                'message_id' => 'SIM_' . uniqid(),
                'response' => 'Simulated successful SMS delivery'
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Simulated network error',
                'response' => 'Simulated failure response'
            ];
        }
    }

    /**
     * Format phone number for Malawian numbers.
     */
    protected function formatPhoneNumber($phoneNumber)
    {
        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Handle Malawian phone number formats
        if (strlen($phone) === 9 && (substr($phone, 0, 1) === '8' || substr($phone, 0, 1) === '9')) {
            // Format: 8XXXXXXXX or 9XXXXXXXX -> +2658XXXXXXXX or +2659XXXXXXXX
            return '+265' . $phone;
        } elseif (strlen($phone) === 10 && substr($phone, 0, 2) === '08') {
            // Format: 08XXXXXXXX -> +2658XXXXXXXX
            return '+265' . substr($phone, 1);
        } elseif (strlen($phone) === 10 && substr($phone, 0, 2) === '09') {
            // Format: 09XXXXXXXX -> +2659XXXXXXXX
            return '+265' . substr($phone, 1);
        } elseif (strlen($phone) === 12 && substr($phone, 0, 3) === '265') {
            // Format: 2658XXXXXXXX -> +2658XXXXXXXX
            return '+' . $phone;
        } elseif (strlen($phone) === 13 && substr($phone, 0, 4) === '+265') {
            // Already in correct format
            return $phone;
        }

        // Default: assume it's a 9-digit number without country code
        return '+265' . $phone;
    }

    /**
     * Get SMS statistics.
     */
    public function getStatistics($period = 'month')
    {
        $query = SmsNotification::query();
        
        if ($period === 'today') {
            $query->today();
        } elseif ($period === 'month') {
            $query->thisMonth();
        }
        
        return [
            'total' => $query->count(),
            'sent' => $query->sent()->count(),
            'delivered' => $query->delivered()->count(),
            'failed' => $query->failed()->count(),
            'pending' => $query->pending()->count(),
            'total_cost' => $query->sum('cost') ?? 0,
        ];
    }
}
