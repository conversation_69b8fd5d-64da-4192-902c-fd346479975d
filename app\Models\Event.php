<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'event_date',
        'start_time',
        'end_time',
        'location',
        'agenda',
        'max_attendees',
        'status',
        'created_by',
        'is_public',
        'notes',
    ];

    protected $casts = [
        'event_date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_public' => 'boolean',
        'max_attendees' => 'integer',
    ];

    /**
     * Get the user who created the event.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the attendees for the event.
     */
    public function attendees()
    {
        return $this->hasMany(EventAttendee::class);
    }

    /**
     * Get users who are attending the event.
     */
    public function attendingUsers()
    {
        return $this->belongsToMany(User::class, 'event_attendees')
                    ->wherePivot('response_status', 'attending')
                    ->withPivot(['attended', 'comments', 'responded_at'])
                    ->withTimestamps();
    }

    /**
     * Check if event is upcoming.
     */
    public function getIsUpcomingAttribute()
    {
        return $this->event_date >= Carbon::today();
    }

    /**
     * Check if event is past.
     */
    public function getIsPastAttribute()
    {
        return $this->event_date < Carbon::today();
    }

    /**
     * Get formatted event date and time.
     */
    public function getFormattedDateTimeAttribute()
    {
        return $this->event_date->format('M j, Y') . ' at ' . $this->start_time->format('g:i A');
    }

    /**
     * Get attendance statistics.
     */
    public function getAttendanceStatsAttribute()
    {
        $total = $this->attendees()->count();
        $attending = $this->attendees()->where('response_status', 'attending')->count();
        $notAttending = $this->attendees()->where('response_status', 'not_attending')->count();
        $maybe = $this->attendees()->where('response_status', 'maybe')->count();
        $pending = $this->attendees()->where('response_status', 'pending')->count();

        return [
            'total' => $total,
            'attending' => $attending,
            'not_attending' => $notAttending,
            'maybe' => $maybe,
            'pending' => $pending,
        ];
    }

    /**
     * Check if event has available spots.
     */
    public function hasAvailableSpots()
    {
        if (!$this->max_attendees) {
            return true;
        }

        $attendingCount = $this->attendees()->where('response_status', 'attending')->count();
        return $attendingCount < $this->max_attendees;
    }
}
