import React, { useState } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

const SavingsDeposit = ({ auth, savingsAccounts }) => {
  const [selectedAccount, setSelectedAccount] = useState(null);

  const { data, setData, post, processing, errors, reset } = useForm({
    savings_account_id: '',
    amount: '',
    payment_method: 'cash',
    reference_number: '',
    notes: '',
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'MW',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    post('/savings/deposit', {
      onSuccess: () => {
        reset();
        setSelectedAccount(null);
      }
    });
  };

  const handleAccountSelect = (account) => {
    setSelectedAccount(account);
    setData('savings_account_id', account.id);
  };

  return (
    <MemberLayout user={auth.user} header="Make Deposit">
      <Head title="Make Deposit - Siyamphanje Cooperative" />
      
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Make Deposit</h1>
                <p className="mt-2 text-gray-600">Add money to your savings account</p>
              </div>
              <Link
                href="/dashboard/member"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Savings Accounts */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Select Account</h2>
              
              {savingsAccounts && savingsAccounts.length > 0 ? (
                <div className="space-y-4">
                  {savingsAccounts.map((account) => (
                    <div
                      key={account.id}
                      onClick={() => handleAccountSelect(account)}
                      className={`border-2 rounded-xl p-6 cursor-pointer transition-all duration-300 ${
                        selectedAccount?.id === account.id
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-green-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{account.account_type}</h3>
                          <p className="text-sm text-gray-600">Account #{account.account_number}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-green-600">
                            {formatCurrency(account.balance)}
                          </p>
                          <p className="text-sm text-gray-500">Current Balance</p>
                        </div>
                      </div>
                      
                      {selectedAccount?.id === account.id && (
                        <div className="mt-4 pt-4 border-t border-green-200">
                          <div className="flex items-center text-green-600">
                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span className="text-sm font-medium">Selected for deposit</span>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Savings Accounts</h3>
                  <p className="text-gray-600">You need to create a savings account first.</p>
                  <Link
                    href="/savings/create"
                    className="mt-4 inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Create Account
                  </Link>
                </div>
              )}
            </div>

            {/* Deposit Form */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Deposit Details</h2>
              
              {selectedAccount ? (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                      Deposit Amount (MW) <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      id="amount"
                      step="0.01"
                      min="1"
                      value={data.amount}
                      onChange={(e) => setData('amount', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      placeholder="Enter amount to deposit"
                      required
                    />
                    {errors.amount && <p className="mt-1 text-sm text-red-600">{errors.amount}</p>}
                  </div>

                  <div>
                    <label htmlFor="payment_method" className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Method <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="payment_method"
                      value={data.payment_method}
                      onChange={(e) => setData('payment_method', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      required
                    >
                      <option value="cash">Cash</option>
                      <option value="bank_transfer">Bank Transfer</option>
                      <option value="mobile_money">Mobile Money</option>
                      <option value="check">Check</option>
                    </select>
                    {errors.payment_method && <p className="mt-1 text-sm text-red-600">{errors.payment_method}</p>}
                  </div>

                  <div>
                    <label htmlFor="reference_number" className="block text-sm font-medium text-gray-700 mb-2">
                      Reference Number
                    </label>
                    <input
                      type="text"
                      id="reference_number"
                      value={data.reference_number}
                      onChange={(e) => setData('reference_number', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      placeholder="Transaction reference (optional)"
                    />
                    {errors.reference_number && <p className="mt-1 text-sm text-red-600">{errors.reference_number}</p>}
                  </div>

                  <div>
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                      Notes
                    </label>
                    <textarea
                      id="notes"
                      value={data.notes}
                      onChange={(e) => setData('notes', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      placeholder="Additional notes (optional)"
                    />
                    {errors.notes && <p className="mt-1 text-sm text-red-600">{errors.notes}</p>}
                  </div>

                  {data.amount && (
                    <div className="bg-green-50 rounded-lg p-4">
                      <h4 className="font-medium text-green-900 mb-2">Deposit Summary</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-green-700">Account:</span>
                          <span className="font-medium text-green-900">{selectedAccount.account_type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-700">Current Balance:</span>
                          <span className="font-medium text-green-900">{formatCurrency(selectedAccount.balance)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-700">Deposit Amount:</span>
                          <span className="font-medium text-green-900">{formatCurrency(data.amount)}</span>
                        </div>
                        <div className="flex justify-between border-t border-green-200 pt-2">
                          <span className="text-green-700 font-medium">New Balance:</span>
                          <span className="font-bold text-green-900 text-lg">
                            {formatCurrency(parseFloat(selectedAccount.balance) + parseFloat(data.amount || 0))}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex space-x-3">
                    <button
                      type="submit"
                      disabled={processing}
                      className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-colors disabled:opacity-50 font-medium"
                    >
                      {processing ? 'Processing...' : 'Make Deposit'}
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        reset();
                        setSelectedAccount(null);
                      }}
                      className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium"
                    >
                      Clear
                    </button>
                  </div>
                </form>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select an Account</h3>
                  <p className="text-gray-600">Please select a savings account to make a deposit.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MemberLayout>
  );
};

export default SavingsDeposit;
