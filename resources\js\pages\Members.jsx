import React from 'react';
import { Head } from '@inertiajs/react';

const Members = ({ members }) => {
  const totalMembers = members.length;
  const activeMembers = members.filter(member => member.status === 'active').length;
  // These values (savings and loans) are not provided by the backend in MemberController.php
  // You will need to fetch or calculate them if you want to display accurate numbers.
  const totalSavings = 0; 
  const totalLoans = 0;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <Head title="Cooperative Members" />
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-indigo-800">Cooperative Members</h1>
          <div className="flex gap-4">
            <button className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-lg transition-colors">
              Add New Member
            </button>
            <button className="bg-white border border-indigo-700 text-indigo-700 hover:bg-indigo-50 px-4 py-2 rounded-lg transition-colors">
              Export List
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div className="grid grid-cols-6 bg-indigo-100 p-4 font-semibold text-indigo-800">
            <div>Member ID</div>
            <div>Name</div>
            <div>Join Date</div>
            <div>Savings</div>
            <div>Loans</div>
            <div>Status</div>
          </div>
          
          {members.map((member) => (
            <div key={member.id} className="grid grid-cols-6 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors">
              <div className="font-medium">AG{member.id.toString().padStart(5, '0')}</div>
              <div className="font-medium">{member.name}</div>
              <div>{member.join_date}</div>
              <div className="text-green-600 font-medium">K {totalSavings.toLocaleString()}</div>
              <div className={totalLoans > 0 ? "text-red-600 font-medium" : "font-medium"}>
                K {totalLoans.toLocaleString()}
              </div>
              <div>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  member.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {member.status}
                </span>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Total Members</h3>
            <p className="text-3xl font-bold text-indigo-700">{totalMembers}</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Active Members</h3>
            <p className="text-3xl font-bold text-green-600">{activeMembers}</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Total Savings</h3>
            <p className="text-3xl font-bold text-green-700">K {totalSavings.toLocaleString()}</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Total Loans</h3>
            <p className="text-3xl font-bold text-red-600">K {totalLoans.toLocaleString()}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Members;