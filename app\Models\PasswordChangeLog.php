<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PasswordChangeLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'change_type',
        'ip_address',
        'user_agent',
        'admin_notified',
        'notified_at',
    ];

    protected $casts = [
        'admin_notified' => 'boolean',
        'notified_at' => 'datetime',
    ];

    /**
     * Get the user that changed their password
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for unnotified password changes
     */
    public function scopeUnnotified($query)
    {
        return $query->where('admin_notified', false);
    }

    /**
     * Mark as notified
     */
    public function markAsNotified()
    {
        $this->update([
            'admin_notified' => true,
            'notified_at' => now(),
        ]);
    }
}
