import React, { useState } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

const LoanApplication = ({ auth, existingLoans, loanSettings }) => {
  const [loanType, setLoanType] = useState('agricultural');

  const { data, setData, post, processing, errors, reset } = useForm({
    amount: '',
    purpose: '',
    term_months: '12',
    loan_type: 'agricultural',
    collateral_description: '',
    monthly_income: '',
    other_loans: '',
    guarantor_name: '',
    guarantor_phone: '',
    guarantor_relationship: '',
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'ZMW',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  const calculateMonthlyPayment = () => {
    if (!data.amount || !data.term_months) return 0;
    const principal = parseFloat(data.amount);
    const months = parseInt(data.term_months);
    const interestRate = (loanSettings?.default_interest_rate || 5) / 100 / 12;
    
    if (interestRate === 0) return principal / months;
    
    const monthlyPayment = principal * (interestRate * Math.pow(1 + interestRate, months)) / 
                          (Math.pow(1 + interestRate, months) - 1);
    return monthlyPayment;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    post('/loans/apply', {
      onSuccess: () => {
        reset();
      }
    });
  };

  const loanTypes = [
    { value: 'agricultural', label: 'Agricultural Loan', description: 'For farming activities, seeds, fertilizers' },
    { value: 'equipment', label: 'Equipment Loan', description: 'For purchasing farm equipment and machinery' },
    { value: 'emergency', label: 'Emergency Loan', description: 'For urgent financial needs' },
    { value: 'business', label: 'Business Loan', description: 'For starting or expanding business activities' },
  ];

  const hasActiveLoans = existingLoans?.some(loan => 
    ['approved', 'disbursed', 'partial_repayment'].includes(loan.status)
  );

  return (
    <MemberLayout user={auth.user} header="Apply for Loan">
      <Head title="Apply for Loan - Siyamphanje Cooperative" />
      
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-green-50 p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Apply for Loan</h1>
                <p className="mt-2 text-gray-600">Submit your loan application for review</p>
              </div>
              <Link
                href="/dashboard/member"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>

          {/* Warning for existing loans */}
          {hasActiveLoans && (
            <div className="mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">Active Loan Notice</h3>
                  <p className="mt-1 text-sm text-yellow-700">
                    You have active loans. Please ensure you can manage additional loan payments before applying.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Loan Application Form */}
            <div className="lg:col-span-2 bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Loan Application Form</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Loan Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Loan Type <span className="text-red-500">*</span>
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {loanTypes.map((type) => (
                      <div
                        key={type.value}
                        onClick={() => {
                          setLoanType(type.value);
                          setData('loan_type', type.value);
                        }}
                        className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-300 ${
                          data.loan_type === type.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-blue-300'
                        }`}
                      >
                        <h4 className="font-medium text-gray-900">{type.label}</h4>
                        <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                      </div>
                    ))}
                  </div>
                  {errors.loan_type && <p className="mt-1 text-sm text-red-600">{errors.loan_type}</p>}
                </div>

                {/* Loan Amount and Term */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                      Loan Amount (ZMW) <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      id="amount"
                      step="100"
                      min={loanSettings?.minimum_loan_amount || 1000}
                      max={loanSettings?.maximum_loan_amount || 100000}
                      value={data.amount}
                      onChange={(e) => setData('amount', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter loan amount"
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Min: {formatCurrency(loanSettings?.minimum_loan_amount || 1000)} - 
                      Max: {formatCurrency(loanSettings?.maximum_loan_amount || 100000)}
                    </p>
                    {errors.amount && <p className="mt-1 text-sm text-red-600">{errors.amount}</p>}
                  </div>

                  <div>
                    <label htmlFor="term_months" className="block text-sm font-medium text-gray-700 mb-2">
                      Loan Term (Months) <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="term_months"
                      value={data.term_months}
                      onChange={(e) => setData('term_months', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="6">6 Months</option>
                      <option value="12">12 Months</option>
                      <option value="18">18 Months</option>
                      <option value="24">24 Months</option>
                      <option value="36">36 Months</option>
                    </select>
                    {errors.term_months && <p className="mt-1 text-sm text-red-600">{errors.term_months}</p>}
                  </div>
                </div>

                {/* Purpose */}
                <div>
                  <label htmlFor="purpose" className="block text-sm font-medium text-gray-700 mb-2">
                    Purpose of Loan <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="purpose"
                    value={data.purpose}
                    onChange={(e) => setData('purpose', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe how you plan to use this loan..."
                    required
                  />
                  {errors.purpose && <p className="mt-1 text-sm text-red-600">{errors.purpose}</p>}
                </div>

                {/* Financial Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="monthly_income" className="block text-sm font-medium text-gray-700 mb-2">
                      Monthly Income (ZMW) <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      id="monthly_income"
                      step="100"
                      min="0"
                      value={data.monthly_income}
                      onChange={(e) => setData('monthly_income', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Your monthly income"
                      required
                    />
                    {errors.monthly_income && <p className="mt-1 text-sm text-red-600">{errors.monthly_income}</p>}
                  </div>

                  <div>
                    <label htmlFor="other_loans" className="block text-sm font-medium text-gray-700 mb-2">
                      Other Monthly Loan Payments (ZMW)
                    </label>
                    <input
                      type="number"
                      id="other_loans"
                      step="100"
                      min="0"
                      value={data.other_loans}
                      onChange={(e) => setData('other_loans', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Other loan payments"
                    />
                    {errors.other_loans && <p className="mt-1 text-sm text-red-600">{errors.other_loans}</p>}
                  </div>
                </div>

                {/* Collateral */}
                <div>
                  <label htmlFor="collateral_description" className="block text-sm font-medium text-gray-700 mb-2">
                    Collateral Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="collateral_description"
                    value={data.collateral_description}
                    onChange={(e) => setData('collateral_description', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe the collateral you can provide (land, equipment, etc.)"
                    required
                  />
                  {errors.collateral_description && <p className="mt-1 text-sm text-red-600">{errors.collateral_description}</p>}
                </div>

                {/* Guarantor Information */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Guarantor Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="guarantor_name" className="block text-sm font-medium text-gray-700 mb-2">
                        Guarantor Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="guarantor_name"
                        value={data.guarantor_name}
                        onChange={(e) => setData('guarantor_name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Full name of guarantor"
                        required
                      />
                      {errors.guarantor_name && <p className="mt-1 text-sm text-red-600">{errors.guarantor_name}</p>}
                    </div>

                    <div>
                      <label htmlFor="guarantor_phone" className="block text-sm font-medium text-gray-700 mb-2">
                        Guarantor Phone <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        id="guarantor_phone"
                        value={data.guarantor_phone}
                        onChange={(e) => setData('guarantor_phone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Guarantor phone number"
                        required
                      />
                      {errors.guarantor_phone && <p className="mt-1 text-sm text-red-600">{errors.guarantor_phone}</p>}
                    </div>

                    <div className="md:col-span-2">
                      <label htmlFor="guarantor_relationship" className="block text-sm font-medium text-gray-700 mb-2">
                        Relationship to Guarantor <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="guarantor_relationship"
                        value={data.guarantor_relationship}
                        onChange={(e) => setData('guarantor_relationship', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., Family member, Friend, Business partner"
                        required
                      />
                      {errors.guarantor_relationship && <p className="mt-1 text-sm text-red-600">{errors.guarantor_relationship}</p>}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={processing}
                    className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-colors disabled:opacity-50 font-medium"
                  >
                    {processing ? 'Submitting...' : 'Submit Application'}
                  </button>
                  <button
                    type="button"
                    onClick={() => reset()}
                    className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium"
                  >
                    Clear Form
                  </button>
                </div>
              </form>
            </div>

            {/* Loan Calculator & Info */}
            <div className="space-y-6">
              {/* Loan Calculator */}
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Loan Calculator</h3>
                
                {data.amount && data.term_months ? (
                  <div className="space-y-4">
                    <div className="bg-blue-50 rounded-lg p-4">
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-blue-700">Loan Amount:</span>
                          <span className="font-medium text-blue-900">{formatCurrency(data.amount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-blue-700">Interest Rate:</span>
                          <span className="font-medium text-blue-900">{loanSettings?.default_interest_rate || 5}% per annum</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-blue-700">Term:</span>
                          <span className="font-medium text-blue-900">{data.term_months} months</span>
                        </div>
                        <div className="flex justify-between border-t border-blue-200 pt-2">
                          <span className="text-blue-700 font-medium">Monthly Payment:</span>
                          <span className="font-bold text-blue-900 text-lg">{formatCurrency(calculateMonthlyPayment())}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">Enter loan amount and term to see payment calculation.</p>
                )}
              </div>

              {/* Loan Requirements */}
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Loan Requirements</h3>
                <ul className="space-y-3 text-sm">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Active cooperative membership</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Valid identification documents</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Proof of income or farming activities</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Collateral or guarantor</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Good repayment history (if applicable)</span>
                  </li>
                </ul>
              </div>

              {/* Existing Loans */}
              {existingLoans && existingLoans.length > 0 && (
                <div className="bg-white rounded-2xl shadow-xl p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Your Existing Loans</h3>
                  <div className="space-y-3">
                    {existingLoans.map((loan) => (
                      <div key={loan.id} className="border border-gray-200 rounded-lg p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium text-gray-900">{formatCurrency(loan.amount)}</p>
                            <p className="text-sm text-gray-600">{loan.purpose}</p>
                          </div>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            loan.status === 'completed' ? 'bg-green-100 text-green-800' :
                            loan.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                            loan.status === 'disbursed' ? 'bg-purple-100 text-purple-800' :
                            loan.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {loan.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MemberLayout>
  );
};

export default LoanApplication;
