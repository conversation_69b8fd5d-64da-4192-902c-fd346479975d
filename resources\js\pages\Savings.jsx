import React from 'react';
import { Head } from '@inertiajs/react';
const savingsData = [
  {
    id: 1,
    member: '<PERSON>',
    amount: 'K 500,000',
    date: '2025-06-01',
    status: 'Confirmed',
  },
  {
    id: 2,
    member: '<PERSON>',
    amount: 'K 300,000',
    date: '2025-06-05',
    status: 'Pending',
  },
  // Add more sample data as needed
];

const Savings = () => {
  return (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-6 text-green-900">Savings</h2>
      <div className="bg-white shadow rounded-lg p-6">
        <table className="min-w-full">
          <thead>
            <tr>
              <th className="px-4 py-2 text-left text-green-700">Member</th>
              <th className="px-4 py-2 text-left text-green-700">Amount</th>
              <th className="px-4 py-2 text-left text-green-700">Date</th>
              <th className="px-4 py-2 text-left text-green-700">Status</th>
            </tr>
          </thead>
          <tbody>
            {savingsData.map((saving) => (
              <tr key={saving.id} className="border-t">
                <td className="px-4 py-2">{saving.member}</td>
                <td className="px-4 py-2">{saving.amount}</td>
                <td className="px-4 py-2">{saving.date}</td>
                <td className="px-4 py-2">
                  <span
                    className={
                      saving.status === 'Confirmed'
                        ? 'text-green-600 font-semibold'
                        : 'text-yellow-600 font-semibold'
                    }
                  >
                    {saving.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Savings;