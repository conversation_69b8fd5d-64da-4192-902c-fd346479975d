<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add missing columns to farmers table
        Schema::table('farmers', function (Blueprint $table) {
            if (!Schema::hasColumn('farmers', 'phone')) {
                $table->string('phone')->nullable()->after('user_id');
            }
            if (!Schema::hasColumn('farmers', 'address')) {
                $table->text('address')->nullable()->after('phone');
            }
            if (!Schema::hasColumn('farmers', 'farm_location')) {
                $table->string('farm_location')->nullable()->after('land_size');
            }
            if (!Schema::hasColumn('farmers', 'crops_grown')) {
                $table->string('crops_grown')->nullable()->after('farm_location');
            }
            if (!Schema::hasColumn('farmers', 'farming_experience')) {
                $table->integer('farming_experience')->nullable()->after('crops_grown');
            }
        });

        // Add missing columns to savings_accounts table
        Schema::table('savings_accounts', function (Blueprint $table) {
            if (!Schema::hasColumn('savings_accounts', 'interest_rate')) {
                $table->decimal('interest_rate', 5, 2)->default(5.0)->after('balance');
            }
        });

        // Add missing columns to loans table
        Schema::table('loans', function (Blueprint $table) {
            if (!Schema::hasColumn('loans', 'approved_date')) {
                $table->timestamp('approved_date')->nullable()->after('application_date');
            }
            if (!Schema::hasColumn('loans', 'disbursed_date')) {
                $table->timestamp('disbursed_date')->nullable()->after('approved_date');
            }
        });

        // Add missing columns to farm_equipment table
        Schema::table('farm_equipment', function (Blueprint $table) {
            if (!Schema::hasColumn('farm_equipment', 'rental_cost_per_day')) {
                $table->decimal('rental_cost_per_day', 10, 2)->nullable()->after('value');
            }
            if (!Schema::hasColumn('farm_equipment', 'available_for_rent')) {
                $table->boolean('available_for_rent')->default(true)->after('is_available');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove added columns from farmers table
        Schema::table('farmers', function (Blueprint $table) {
            $columnsToRemove = [];
            if (Schema::hasColumn('farmers', 'phone')) $columnsToRemove[] = 'phone';
            if (Schema::hasColumn('farmers', 'address')) $columnsToRemove[] = 'address';
            if (Schema::hasColumn('farmers', 'farm_location')) $columnsToRemove[] = 'farm_location';
            if (Schema::hasColumn('farmers', 'crops_grown')) $columnsToRemove[] = 'crops_grown';
            if (Schema::hasColumn('farmers', 'farming_experience')) $columnsToRemove[] = 'farming_experience';

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });

        // Remove added columns from savings_accounts table
        Schema::table('savings_accounts', function (Blueprint $table) {
            if (Schema::hasColumn('savings_accounts', 'interest_rate')) {
                $table->dropColumn('interest_rate');
            }
        });

        // Remove added columns from loans table
        Schema::table('loans', function (Blueprint $table) {
            $columnsToRemove = [];
            if (Schema::hasColumn('loans', 'approved_date')) $columnsToRemove[] = 'approved_date';
            if (Schema::hasColumn('loans', 'disbursed_date')) $columnsToRemove[] = 'disbursed_date';

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });

        // Remove added columns from farm_equipment table
        Schema::table('farm_equipment', function (Blueprint $table) {
            $columnsToRemove = [];
            if (Schema::hasColumn('farm_equipment', 'rental_cost_per_day')) $columnsToRemove[] = 'rental_cost_per_day';
            if (Schema::hasColumn('farm_equipment', 'available_for_rent')) $columnsToRemove[] = 'available_for_rent';

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });
    }
};
