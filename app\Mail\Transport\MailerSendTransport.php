<?php

namespace App\Mail\Transport;

use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Transport\AbstractTransport;
use Symfony\Component\Mime\MessageConverter;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MailerSendTransport extends AbstractTransport
{
    protected $apiKey;

    public function __construct(string $apiKey)
    {
        $this->apiKey = $apiKey;
        parent::__construct();
    }

    protected function doSend(SentMessage $message): void
    {
        $email = MessageConverter::toEmail($message->getOriginalMessage());
        
        $payload = [
            'from' => [
                'email' => $email->getFrom()[0]->getAddress(),
                'name' => $email->getFrom()[0]->getName() ?? 'Siyamphanje Cooperative',
            ],
            'to' => collect($email->getTo())->map(function ($recipient) {
                return [
                    'email' => $recipient->getAddress(),
                    'name' => $recipient->getName() ?? $recipient->getAddress(),
                ];
            })->toArray(),
            'subject' => $email->getSubject(),
        ];

        // Add HTML content
        if ($email->getHtmlBody()) {
            $payload['html'] = $email->getHtmlBody();
        }

        // Add text content
        if ($email->getTextBody()) {
            $payload['text'] = $email->getTextBody();
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest',
            ])->post('https://api.mailersend.com/v1/email', $payload);

            if (!$response->successful()) {
                $error = $response->json();
                Log::error('MailerSend API Error', [
                    'status' => $response->status(),
                    'error' => $error,
                    'payload' => $payload
                ]);
                throw new \Exception('MailerSend API Error: ' . ($error['message'] ?? 'Unknown error'));
            }

            Log::info('Email sent successfully via MailerSend', [
                'to' => $payload['to'],
                'subject' => $payload['subject']
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send email via MailerSend', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            throw $e;
        }
    }

    public function __toString(): string
    {
        return 'mailersend';
    }
}
