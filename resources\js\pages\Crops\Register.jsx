import React, { useState } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

const CropRegistration = ({ auth, existingCrops }) => {
  const [cropType, setCropType] = useState('');

  const { data, setData, post, processing, errors, reset } = useForm({
    crop_name: '',
    crop_type: '',
    variety: '',
    planting_date: '',
    expected_harvest_date: '',
    area_planted: '',
    planting_method: 'direct_seeding',
    irrigation_method: 'rain_fed',
    fertilizer_used: '',
    pesticide_used: '',
    expected_yield: '',
    estimated_value: '',
    location_description: '',
    notes: '',
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'MW',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-ZM');
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    post('/crops/register', {
      onSuccess: () => {
        reset();
        setCropType('');
      }
    });
  };

  const cropTypes = [
    { value: 'cereals', label: 'Cereals', crops: ['Maize', 'Rice', 'Wheat', 'Sorghum', 'Millet'] },
    { value: 'legumes', label: 'Legumes', crops: ['Beans', 'Groundnuts', 'Soybeans', 'Cowpeas', 'Pigeon Peas'] },
    { value: 'vegetables', label: 'Vegetables', crops: ['Tomatoes', 'Onions', 'Cabbage', 'Carrots', 'Spinach'] },
    { value: 'fruits', label: 'Fruits', crops: ['Mangoes', 'Oranges', 'Bananas', 'Avocados', 'Papayas'] },
    { value: 'cash_crops', label: 'Cash Crops', crops: ['Cotton', 'Tobacco', 'Coffee', 'Tea', 'Sugarcane'] },
    { value: 'tubers', label: 'Tubers', crops: ['Sweet Potatoes', 'Cassava', 'Irish Potatoes', 'Yams'] },
  ];

  const plantingMethods = [
    { value: 'direct_seeding', label: 'Direct Seeding' },
    { value: 'transplanting', label: 'Transplanting' },
    { value: 'broadcasting', label: 'Broadcasting' },
    { value: 'drilling', label: 'Drilling' },
  ];

  const irrigationMethods = [
    { value: 'rain_fed', label: 'Rain Fed' },
    { value: 'sprinkler', label: 'Sprinkler Irrigation' },
    { value: 'drip', label: 'Drip Irrigation' },
    { value: 'furrow', label: 'Furrow Irrigation' },
    { value: 'flood', label: 'Flood Irrigation' },
  ];

  return (
    <MemberLayout user={auth.user} header="Register Crops">
      <Head title="Register Crops - Siyamphanje Cooperative" />
      
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-green-50 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Register Crops</h1>
                <p className="mt-2 text-gray-600">Record your farming activities and crop information</p>
              </div>
              <Link
                href="/dashboard/member"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Crop Registration Form */}
            <div className="lg:col-span-2 bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Crop Information</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Crop Type and Name */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="crop_type" className="block text-sm font-medium text-gray-700 mb-2">
                      Crop Category <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="crop_type"
                      value={data.crop_type}
                      onChange={(e) => {
                        setData('crop_type', e.target.value);
                        setCropType(e.target.value);
                        setData('crop_name', ''); // Reset crop name when category changes
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      required
                    >
                      <option value="">Select crop category</option>
                      {cropTypes.map((type) => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                    {errors.crop_type && <p className="mt-1 text-sm text-red-600">{errors.crop_type}</p>}
                  </div>

                  <div>
                    <label htmlFor="crop_name" className="block text-sm font-medium text-gray-700 mb-2">
                      Crop Name <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="crop_name"
                      value={data.crop_name}
                      onChange={(e) => setData('crop_name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      required
                      disabled={!cropType}
                    >
                      <option value="">Select crop</option>
                      {cropType && cropTypes.find(t => t.value === cropType)?.crops.map((crop) => (
                        <option key={crop} value={crop}>{crop}</option>
                      ))}
                    </select>
                    {errors.crop_name && <p className="mt-1 text-sm text-red-600">{errors.crop_name}</p>}
                  </div>
                </div>

                {/* Variety and Area */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="variety" className="block text-sm font-medium text-gray-700 mb-2">
                      Variety/Cultivar
                    </label>
                    <input
                      type="text"
                      id="variety"
                      value={data.variety}
                      onChange={(e) => setData('variety', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      placeholder="e.g., SC627, Hybrid variety"
                    />
                    {errors.variety && <p className="mt-1 text-sm text-red-600">{errors.variety}</p>}
                  </div>

                  <div>
                    <label htmlFor="area_planted" className="block text-sm font-medium text-gray-700 mb-2">
                      Area Planted (hectares) <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      id="area_planted"
                      step="0.1"
                      min="0.1"
                      value={data.area_planted}
                      onChange={(e) => setData('area_planted', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Area in hectares"
                      required
                    />
                    {errors.area_planted && <p className="mt-1 text-sm text-red-600">{errors.area_planted}</p>}
                  </div>
                </div>

                {/* Planting and Harvest Dates */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="planting_date" className="block text-sm font-medium text-gray-700 mb-2">
                      Planting Date <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      id="planting_date"
                      value={data.planting_date}
                      onChange={(e) => setData('planting_date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      required
                    />
                    {errors.planting_date && <p className="mt-1 text-sm text-red-600">{errors.planting_date}</p>}
                  </div>

                  <div>
                    <label htmlFor="expected_harvest_date" className="block text-sm font-medium text-gray-700 mb-2">
                      Expected Harvest Date <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      id="expected_harvest_date"
                      value={data.expected_harvest_date}
                      onChange={(e) => setData('expected_harvest_date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      required
                    />
                    {errors.expected_harvest_date && <p className="mt-1 text-sm text-red-600">{errors.expected_harvest_date}</p>}
                  </div>
                </div>

                {/* Farming Methods */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="planting_method" className="block text-sm font-medium text-gray-700 mb-2">
                      Planting Method <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="planting_method"
                      value={data.planting_method}
                      onChange={(e) => setData('planting_method', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      required
                    >
                      {plantingMethods.map((method) => (
                        <option key={method.value} value={method.value}>{method.label}</option>
                      ))}
                    </select>
                    {errors.planting_method && <p className="mt-1 text-sm text-red-600">{errors.planting_method}</p>}
                  </div>

                  <div>
                    <label htmlFor="irrigation_method" className="block text-sm font-medium text-gray-700 mb-2">
                      Irrigation Method <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="irrigation_method"
                      value={data.irrigation_method}
                      onChange={(e) => setData('irrigation_method', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      required
                    >
                      {irrigationMethods.map((method) => (
                        <option key={method.value} value={method.value}>{method.label}</option>
                      ))}
                    </select>
                    {errors.irrigation_method && <p className="mt-1 text-sm text-red-600">{errors.irrigation_method}</p>}
                  </div>
                </div>

                {/* Inputs Used */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="fertilizer_used" className="block text-sm font-medium text-gray-700 mb-2">
                      Fertilizer Used
                    </label>
                    <input
                      type="text"
                      id="fertilizer_used"
                      value={data.fertilizer_used}
                      onChange={(e) => setData('fertilizer_used', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      placeholder="e.g., NPK 10:20:10, Urea"
                    />
                    {errors.fertilizer_used && <p className="mt-1 text-sm text-red-600">{errors.fertilizer_used}</p>}
                  </div>

                  <div>
                    <label htmlFor="pesticide_used" className="block text-sm font-medium text-gray-700 mb-2">
                      Pesticide/Herbicide Used
                    </label>
                    <input
                      type="text"
                      id="pesticide_used"
                      value={data.pesticide_used}
                      onChange={(e) => setData('pesticide_used', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      placeholder="e.g., Roundup, Atrazine"
                    />
                    {errors.pesticide_used && <p className="mt-1 text-sm text-red-600">{errors.pesticide_used}</p>}
                  </div>
                </div>

                {/* Expected Yield and Value */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="expected_yield" className="block text-sm font-medium text-gray-700 mb-2">
                      Expected Yield (kg/ha)
                    </label>
                    <input
                      type="number"
                      id="expected_yield"
                      step="0.1"
                      min="0"
                      value={data.expected_yield}
                      onChange={(e) => setData('expected_yield', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Expected yield per hectare"
                    />
                    {errors.expected_yield && <p className="mt-1 text-sm text-red-600">{errors.expected_yield}</p>}
                  </div>

                  <div>
                    <label htmlFor="estimated_value" className="block text-sm font-medium text-gray-700 mb-2">
                      Estimated Value (MW)
                    </label>
                    <input
                      type="number"
                      id="estimated_value"
                      step="100"
                      min="0"
                      value={data.estimated_value}
                      onChange={(e) => setData('estimated_value', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Estimated crop value"
                    />
                    {errors.estimated_value && <p className="mt-1 text-sm text-red-600">{errors.estimated_value}</p>}
                  </div>
                </div>

                {/* Location and Notes */}
                <div>
                  <label htmlFor="location_description" className="block text-sm font-medium text-gray-700 mb-2">
                    Location Description <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="location_description"
                    value={data.location_description}
                    onChange={(e) => setData('location_description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                    placeholder="e.g., North field, Plot 5, Near the river"
                    required
                  />
                  {errors.location_description && <p className="mt-1 text-sm text-red-600">{errors.location_description}</p>}
                </div>

                <div>
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Notes
                  </label>
                  <textarea
                    id="notes"
                    value={data.notes}
                    onChange={(e) => setData('notes', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Any additional information about this crop..."
                  />
                  {errors.notes && <p className="mt-1 text-sm text-red-600">{errors.notes}</p>}
                </div>

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={processing}
                    className="flex-1 bg-gradient-to-r from-orange-500 to-amber-500 text-white py-3 px-4 rounded-lg hover:from-orange-600 hover:to-amber-600 transition-colors disabled:opacity-50 font-medium"
                  >
                    {processing ? 'Registering...' : 'Register Crop'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      reset();
                      setCropType('');
                    }}
                    className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium"
                  >
                    Clear Form
                  </button>
                </div>
              </form>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Crop Summary */}
              {data.crop_name && data.area_planted && (
                <div className="bg-white rounded-2xl shadow-xl p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Crop Summary</h3>
                  <div className="space-y-3">
                    <div className="bg-orange-50 rounded-lg p-4">
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-orange-700">Crop:</span>
                          <span className="font-medium text-orange-900">{data.crop_name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-orange-700">Area:</span>
                          <span className="font-medium text-orange-900">{data.area_planted} hectares</span>
                        </div>
                        {data.planting_date && (
                          <div className="flex justify-between">
                            <span className="text-orange-700">Planting:</span>
                            <span className="font-medium text-orange-900">{formatDate(data.planting_date)}</span>
                          </div>
                        )}
                        {data.expected_harvest_date && (
                          <div className="flex justify-between">
                            <span className="text-orange-700">Harvest:</span>
                            <span className="font-medium text-orange-900">{formatDate(data.expected_harvest_date)}</span>
                          </div>
                        )}
                        {data.estimated_value && (
                          <div className="flex justify-between border-t border-orange-200 pt-2">
                            <span className="text-orange-700 font-medium">Est. Value:</span>
                            <span className="font-bold text-orange-900">{formatCurrency(data.estimated_value)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Farming Tips */}
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Farming Tips</h3>
                <ul className="space-y-3 text-sm">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Keep detailed records of all farming activities</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Monitor weather conditions regularly</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Use certified seeds for better yields</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Practice crop rotation for soil health</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Plan for post-harvest handling</span>
                  </li>
                </ul>
              </div>

              {/* Existing Crops */}
              {existingCrops && existingCrops.length > 0 && (
                <div className="bg-white rounded-2xl shadow-xl p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Your Registered Crops</h3>
                  <div className="space-y-3">
                    {existingCrops.slice(0, 5).map((crop) => (
                      <div key={crop.id} className="border border-gray-200 rounded-lg p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium text-gray-900">{crop.crop_name}</p>
                            <p className="text-sm text-gray-600">{crop.area_planted} ha</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500">{formatDate(crop.planting_date)}</p>
                            {crop.estimated_value && (
                              <p className="text-sm font-medium text-green-600">{formatCurrency(crop.estimated_value)}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {existingCrops.length > 5 && (
                      <p className="text-sm text-gray-500 text-center">
                        +{existingCrops.length - 5} more crops
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MemberLayout>
  );
};

export default CropRegistration;
