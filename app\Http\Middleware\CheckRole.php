<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;
use App\Models\User;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please log in to access this page.');
        }

        $user = auth()->user();

        // Load the role relationship
        $user = User::with('role')->find($user->id);

        // Check if user has a role
        if (!$user->role) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Your account does not have a valid role. Please contact the administrator.');
        }

        $userRole = $user->role->name;

        // Check if user's role is in the allowed roles
        if (!in_array($userRole, $roles)) {
            // Log the unauthorized access attempt
            Log::warning('Unauthorized access attempt', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_role' => $userRole,
                'required_roles' => $roles,
                'requested_url' => $request->url(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Redirect based on user's actual role
            switch ($userRole) {
                case 'admin':
                    return redirect()->route('dashboard.admin')->with('error', 'You do not have permission to access that page.');
                case 'staff':
                    return redirect()->route('dashboard.staff')->with('error', 'You do not have permission to access that page.');
                case 'member':
                default:
                    return redirect()->route('dashboard.member')->with('error', 'You do not have permission to access that page.');
            }
        }

        return $next($request);
    }
}
