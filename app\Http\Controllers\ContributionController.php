<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Contribution;
use App\Models\User;
use App\Models\Farmer;
use Illuminate\Support\Facades\Log;

class ContributionController extends Controller
{
    /**
     * Display a listing of contributions for members.
     */
    public function index(Request $request)
    {
        $user = User::with(['role', 'farmer'])->find(auth()->id());

        // Get member's contributions
        $contributions = Contribution::where('member_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Contributions/Index', [
            'contributions' => $contributions,
            'auth' => ['user' => $user]
        ]);
    }

    /**
     * Display a listing of all contributions for admin.
     */
    public function adminIndex(Request $request)
    {
        $search = $request->get('search');
        $type = $request->get('type');
        $status = $request->get('status');

        $query = Contribution::with(['member.farmer'])
            ->orderBy('created_at', 'desc');

        if ($search) {
            $query->whereHas('member', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($type) {
            $query->where('type', $type);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $contributions = $query->paginate(15);

        // Get summary statistics
        $stats = [
            'total_contributions' => Contribution::sum('amount'),
            'monthly_contributions' => Contribution::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
            'pending_contributions' => Contribution::where('status', 'pending')->count(),
            'total_members_contributed' => Contribution::distinct('member_id')->count(),
        ];

        return Inertia::render('Admin/Contributions/Index', [
            'contributions' => $contributions,
            'stats' => $stats,
            'filters' => [
                'search' => $search,
                'type' => $type,
                'status' => $status,
            ],
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Show the form for creating a new contribution.
     */
    public function create()
    {
        // Get all members for the dropdown
        $members = User::with(['role', 'farmer'])
            ->whereHas('role', function ($query) {
                $query->where('name', 'member');
            })
            ->orderBy('name')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'farmer_id' => $user->farmer ? $user->farmer->id : null,
                    'farm_size' => $user->farmer ? $user->farmer->farm_size : null,
                ];
            });

        return Inertia::render('Admin/Contributions/Create', [
            'members' => $members,
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Store a newly created contribution in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'member_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:monthly,special,emergency,development',
            'description' => 'nullable|string|max:500',
            'payment_method' => 'required|in:cash,bank_transfer,mobile_money',
            'reference_number' => 'nullable|string|max:100',
            'contribution_date' => 'required|date',
        ]);

        // Get the member's farmer_id
        $member = User::with('farmer')->find($validated['member_id']);
        $farmer_id = $member->farmer ? $member->farmer->id : null;

        // Create the contribution
        $contribution = Contribution::create([
            'member_id' => $validated['member_id'],
            'farmer_id' => $farmer_id,
            'amount' => $validated['amount'],
            'type' => $validated['type'],
            'description' => $validated['description'],
            'payment_method' => $validated['payment_method'],
            'reference_number' => $validated['reference_number'],
            'contribution_date' => $validated['contribution_date'],
            'status' => 'confirmed', // Admin-created contributions are automatically confirmed
            'recorded_by' => auth()->id(),
        ]);

        Log::info('Contribution recorded by admin', [
            'contribution_id' => $contribution->id,
            'member_id' => $validated['member_id'],
            'amount' => $validated['amount'],
            'recorded_by' => auth()->id(),
        ]);

        return redirect()->route('admin.contributions.index')
            ->with('success', 'Contribution recorded successfully!');
    }

    /**
     * Display the specified contribution.
     */
    public function show($id)
    {
        $contribution = Contribution::with(['member.farmer', 'recordedBy'])
            ->findOrFail($id);

        return Inertia::render('Admin/Contributions/Show', [
            'contribution' => $contribution,
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Show the form for editing the specified contribution.
     */
    public function edit($id)
    {
        $contribution = Contribution::with(['member.farmer'])
            ->findOrFail($id);

        $members = User::with(['role', 'farmer'])
            ->whereHas('role', function ($query) {
                $query->where('name', 'member');
            })
            ->orderBy('name')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'farmer_id' => $user->farmer ? $user->farmer->id : null,
                    'farm_size' => $user->farmer ? $user->farmer->farm_size : null,
                ];
            });

        return Inertia::render('Admin/Contributions/Edit', [
            'contribution' => $contribution,
            'members' => $members,
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Update the specified contribution in storage.
     */
    public function update(Request $request, $id)
    {
        $contribution = Contribution::findOrFail($id);

        $validated = $request->validate([
            'member_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:monthly,special,emergency,development',
            'description' => 'nullable|string|max:500',
            'payment_method' => 'required|in:cash,bank_transfer,mobile_money',
            'reference_number' => 'nullable|string|max:100',
            'contribution_date' => 'required|date',
            'status' => 'required|in:pending,confirmed,rejected',
        ]);

        $contribution->update($validated);

        Log::info('Contribution updated by admin', [
            'contribution_id' => $contribution->id,
            'updated_by' => auth()->id(),
        ]);

        return redirect()->route('admin.contributions.index')
            ->with('success', 'Contribution updated successfully!');
    }

    /**
     * Remove the specified contribution from storage.
     */
    public function destroy($id)
    {
        $contribution = Contribution::findOrFail($id);

        Log::info('Contribution deleted by admin', [
            'contribution_id' => $contribution->id,
            'member_id' => $contribution->member_id,
            'amount' => $contribution->amount,
            'deleted_by' => auth()->id(),
        ]);

        $contribution->delete();

        return redirect()->route('admin.contributions.index')
            ->with('success', 'Contribution deleted successfully!');
    }
}
