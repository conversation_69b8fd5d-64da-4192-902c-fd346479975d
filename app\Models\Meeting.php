<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Meeting extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'agenda',
        'meeting_date',
        'meeting_time',
        'location',
        'meeting_type',
        'status',
        'max_attendees',
        'created_by',
        'called_by',
        'minutes'
    ];

    public function calledBy()
    {
        return $this->belongsTo(User::class, 'called_by');
    }

    public function attendances()
    {
        return $this->hasMany(MeetingAttendance::class);
    }

    public function attendees()
    {
        return $this->belongsToMany(Farmer::class, 'meeting_attendances')
            ->withPivot('attended', 'comments')
            ->withTimestamps();
    }
}
