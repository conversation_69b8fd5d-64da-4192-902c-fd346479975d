import React, { useState, useEffect } from 'react';
import { useForm } from '@inertiajs/react';
import axios from 'axios';

export default function BorrowEquipmentModal({ isOpen, onClose }) {
    const [availableEquipment, setAvailableEquipment] = useState([]);
    const [selectedEquipment, setSelectedEquipment] = useState(null);
    const [loading, setLoading] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm({
        equipment_id: '',
        start_date: '',
        end_date: '',
        purpose: '',
        operator_needed: false,
        transport_needed: false,
        notes: ''
    });

    useEffect(() => {
        if (isOpen) {
            fetchAvailableEquipment();
        }
    }, [isOpen]);

    const fetchAvailableEquipment = async () => {
        setLoading(true);
        try {
            const response = await axios.get('/equipment/available');
            setAvailableEquipment(response.data);
        } catch (error) {
            console.error('Error fetching equipment:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleEquipmentSelect = (equipment) => {
        console.log('Selected equipment:', equipment); // Debug log
        setSelectedEquipment(equipment);
        setData('equipment_id', equipment.id.toString()); // Ensure it's a string
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        // Debug log to check form data
        console.log('Form data being submitted:', data);
        console.log('Selected equipment:', selectedEquipment);

        // Validate required fields
        if (!data.equipment_id || !data.start_date || !data.end_date || !data.purpose) {
            alert('Please fill in all required fields');
            return;
        }

        post('/equipment/request', {
            onSuccess: () => {
                onClose();
                reset();
                setSelectedEquipment(null);
            },
            onError: (errors) => {
                console.error('Form submission errors:', errors);
            }
        });
    };

    const calculateTotalCost = () => {
        if (!selectedEquipment || !data.start_date || !data.end_date) return 0;

        try {
            const startDate = new Date(data.start_date);
            const endDate = new Date(data.end_date);

            // Validate dates
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return 0;
            if (endDate < startDate) return 0;

            const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;

            // Ensure we have valid numbers
            const dailyRate = parseFloat(selectedEquipment.daily_rental_rate) || 0;
            const operatorRate = parseFloat(selectedEquipment.operator_rate) || 0;
            const transportRate = parseFloat(selectedEquipment.transport_rate) || 0;

            let total = dailyRate * days;

            if (data.operator_needed && operatorRate > 0) {
                total += operatorRate * days;
            }

            if (data.transport_needed && transportRate > 0) {
                total += transportRate;
            }

            return isNaN(total) ? 0 : total;
        } catch (error) {
            console.error('Error calculating total cost:', error);
            return 0;
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-medium text-gray-900">🚜 Borrow Equipment</h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {loading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto"></div>
                            <p className="mt-2 text-gray-600">Loading available equipment...</p>
                        </div>
                    ) : (
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Equipment Selection */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-3">Select Equipment *</label>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto">
                                    {availableEquipment.map((equipment) => (
                                        <div
                                            key={equipment.id}
                                            onClick={() => handleEquipmentSelect(equipment)}
                                            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                                                selectedEquipment?.id === equipment.id
                                                    ? 'border-green-500 bg-green-50'
                                                    : 'border-gray-300 hover:border-green-300'
                                            }`}
                                        >
                                            <div className="flex justify-between items-start">
                                                <div>
                                                    <h4 className="font-medium text-gray-900">{equipment.name}</h4>
                                                    <p className="text-sm text-gray-600">{equipment.type}</p>
                                                    <p className="text-sm text-green-600 font-medium">K{equipment.daily_rental_rate}/day</p>
                                                </div>
                                                {selectedEquipment?.id === equipment.id && (
                                                    <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                    </svg>
                                                )}
                                            </div>
                                            {equipment.description && (
                                                <p className="text-xs text-gray-500 mt-2">{equipment.description}</p>
                                            )}
                                            <div className="mt-2 flex flex-wrap gap-2">
                                                {equipment.operator_required && (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        Operator Available
                                                    </span>
                                                )}
                                                {equipment.transport_available && (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                        Transport Available
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                {availableEquipment.length === 0 && (
                                    <div className="text-center py-8 text-gray-500">
                                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4a1 1 0 00-1-1H9a1 1 0 00-1 1v1" />
                                        </svg>
                                        <p className="mt-2">No equipment available for borrowing at the moment.</p>
                                    </div>
                                )}
                                {errors.equipment_id && <p className="text-red-500 text-xs mt-1">{errors.equipment_id}</p>}
                            </div>

                            {selectedEquipment && (
                                <>
                                    {/* Date Selection */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Start Date *</label>
                                            <input
                                                type="date"
                                                value={data.start_date}
                                                onChange={(e) => setData('start_date', e.target.value)}
                                                min={new Date().toISOString().split('T')[0]}
                                                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                                required
                                            />
                                            {errors.start_date && <p className="text-red-500 text-xs mt-1">{errors.start_date}</p>}
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">End Date *</label>
                                            <input
                                                type="date"
                                                value={data.end_date}
                                                onChange={(e) => setData('end_date', e.target.value)}
                                                min={data.start_date || new Date().toISOString().split('T')[0]}
                                                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                                required
                                            />
                                            {errors.end_date && <p className="text-red-500 text-xs mt-1">{errors.end_date}</p>}
                                        </div>
                                    </div>

                                    {/* Purpose */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Purpose *</label>
                                        <textarea
                                            value={data.purpose}
                                            onChange={(e) => setData('purpose', e.target.value)}
                                            rows={3}
                                            placeholder="Describe what you'll use the equipment for..."
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            required
                                        />
                                        {errors.purpose && <p className="text-red-500 text-xs mt-1">{errors.purpose}</p>}
                                    </div>

                                    {/* Additional Services */}
                                    <div className="space-y-3">
                                        {selectedEquipment.operator_required && (
                                            <label className="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    checked={data.operator_needed}
                                                    onChange={(e) => setData('operator_needed', e.target.checked)}
                                                    className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
                                                />
                                                <span className="ml-2 text-sm text-gray-700">
                                                    Include operator (+K{selectedEquipment.operator_rate}/day)
                                                </span>
                                            </label>
                                        )}
                                        {selectedEquipment.transport_available && (
                                            <label className="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    checked={data.transport_needed}
                                                    onChange={(e) => setData('transport_needed', e.target.checked)}
                                                    className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
                                                />
                                                <span className="ml-2 text-sm text-gray-700">
                                                    Include transport (+K{selectedEquipment.transport_rate})
                                                </span>
                                            </label>
                                        )}
                                    </div>

                                    {/* Cost Summary */}
                                    {data.start_date && data.end_date && (
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                            <h4 className="font-medium text-green-900 mb-2">Cost Summary</h4>
                                            <div className="space-y-1 text-sm">
                                                {(() => {
                                                    const startDate = new Date(data.start_date);
                                                    const endDate = new Date(data.end_date);
                                                    const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
                                                    const dailyRate = parseFloat(selectedEquipment.daily_rental_rate) || 0;
                                                    const operatorRate = parseFloat(selectedEquipment.operator_rate) || 0;
                                                    const transportRate = parseFloat(selectedEquipment.transport_rate) || 0;

                                                    return (
                                                        <>
                                                            <div className="flex justify-between">
                                                                <span>Equipment rental ({days} days):</span>
                                                                <span>K{(dailyRate * days).toFixed(2)}</span>
                                                            </div>
                                                            {data.operator_needed && operatorRate > 0 && (
                                                                <div className="flex justify-between">
                                                                    <span>Operator ({days} days):</span>
                                                                    <span>K{(operatorRate * days).toFixed(2)}</span>
                                                                </div>
                                                            )}
                                                            {data.transport_needed && transportRate > 0 && (
                                                                <div className="flex justify-between">
                                                                    <span>Transport:</span>
                                                                    <span>K{transportRate.toFixed(2)}</span>
                                                                </div>
                                                            )}
                                                            <div className="border-t border-green-300 pt-1 flex justify-between font-medium">
                                                                <span>Total:</span>
                                                                <span>K{calculateTotalCost().toFixed(2)}</span>
                                                            </div>
                                                        </>
                                                    );
                                                })()}
                                            </div>
                                        </div>
                                    )}

                                    {/* Notes */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Additional Notes</label>
                                        <textarea
                                            value={data.notes}
                                            onChange={(e) => setData('notes', e.target.value)}
                                            rows={2}
                                            placeholder="Any special requirements or notes..."
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                        />
                                    </div>
                                </>
                            )}

                            {/* Form Actions */}
                            <div className="flex justify-end space-x-3 pt-4 border-t">
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={processing || !selectedEquipment}
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                                >
                                    {processing ? 'Submitting...' : 'Submit Request'}
                                </button>
                            </div>
                        </form>
                    )}
                </div>
            </div>
        </div>
    );
}
