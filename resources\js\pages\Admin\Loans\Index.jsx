import React from 'react';
import AdminLayout from '@/Layouts/AdminLayout';
import { Head } from '@inertiajs/react';

export default function LoanIndex({ loans }) {
    return (
        <AdminLayout>
            <Head title="Manage Loans" />
            <div className="container mx-auto p-4">
                <h1 className="text-2xl font-bold mb-4">Manage Loans</h1>
                <div className="overflow-x-auto">
                    <table className="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th className="py-2 px-4 border-b">ID</th>
                                <th className="py-2 px-4 border-b">Farmer</th>
                                <th className="py-2 px-4 border-b">Amount</th>
                                <th className="py-2 px-4 border-b">Interest Rate</th>
                                <th className="py-2 px-4 border-b">Term (Months)</th>
                                <th className="py-2 px-4 border-b">Purpose</th>
                                <th className="py-2 px-4 border-b">Status</th>
                                <th className="py-2 px-4 border-b">Application Date</th>
                                <th className="py-2 px-4 border-b">Approval Date</th>
                                <th className="py-2 px-4 border-b">Disbursement Date</th>
                                <th className="py-2 px-4 border-b">Approved By</th>
                                <th className="py-2 px-4 border-b">Rejection Reason</th>
                            </tr>
                        </thead>
                        <tbody>
                            {loans.map((loan) => (
                                <tr key={loan.id}>
                                    <td className="py-2 px-4 border-b text-center">{loan.id}</td>
                                    <td className="py-2 px-4 border-b">{loan.farmer ? loan.farmer.name : 'N/A'}</td>
                                    <td className="py-2 px-4 border-b text-right">{parseFloat(loan.amount).toFixed(2)}</td>
                                    <td className="py-2 px-4 border-b text-right">{parseFloat(loan.interest_rate).toFixed(2)}%</td>
                                    <td className="py-2 px-4 border-b text-center">{loan.term_months}</td>
                                    <td className="py-2 px-4 border-b">{loan.purpose}</td>
                                    <td className="py-2 px-4 border-b">{loan.status}</td>
                                    <td className="py-2 px-4 border-b">{loan.application_date}</td>
                                    <td className="py-2 px-4 border-b">{loan.approval_date || 'N/A'}</td>
                                    <td className="py-2 px-4 border-b">{loan.disbursement_date || 'N/A'}</td>
                                    <td className="py-2 px-4 border-b">{loan.approved_by ? loan.approved_by.name : 'N/A'}</td>
                                    <td className="py-2 px-4 border-b">{loan.rejection_reason || 'N/A'}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </AdminLayout>
    );
}