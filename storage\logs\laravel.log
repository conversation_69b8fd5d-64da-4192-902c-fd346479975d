[2025-07-12 17:20:03] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 17:20:02"} 
[2025-07-12 17:20:31] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 17:20:31"} 
[2025-07-12 17:26:27] local.INFO: Membership registration started {"request_data":{"full_name":"<PERSON><PERSON><PERSON>","national_id":"0k6M3AK8","phone_number":"**********","email":"cosmashen<PERSON><EMAIL>","land_size":"6","village_location":"<PERSON><PERSON><PERSON>","membership_fee_paid":true}} 
[2025-07-12 17:26:27] local.INFO: Password generated {"password":"Co<PERSON>s<PERSON>5!"} 
[2025-07-12 17:26:27] local.INFO: Auto-approval check {"auto_approve":true} 
[2025-07-12 17:26:27] local.INFO: Creating approved member  
[2025-07-12 17:26:27] local.INFO: Creating user account {"email":"<EMAIL>"} 
[2025-07-12 17:26:28] local.INFO: User created {"user_id":16,"role_id":2} 
[2025-07-12 17:26:28] local.INFO: Sending welcome email {"user_email":"<EMAIL>"} 
[2025-07-12 17:26:28] local.INFO: Attempting to send welcome email {"to":"<EMAIL>","name":"Cosmas Henrico","password_length":10} 
[2025-07-12 17:26:35] local.INFO: Welcome email sent successfully to: <EMAIL>  
[2025-07-12 17:26:35] local.INFO: Email send result {"email_sent":true} 
[2025-07-12 17:28:18] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"jFGRHs2l9kRLtvPvg2m1qsAr5Re1jfaOwPaRt7vl","timestamp":"2025-07-12 17:28:18"} 
[2025-07-12 17:32:37] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"jFGRHs2l9kRLtvPvg2m1qsAr5Re1jfaOwPaRt7vl","timestamp":"2025-07-12 17:32:37"} 
[2025-07-12 17:33:23] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"jFGRHs2l9kRLtvPvg2m1qsAr5Re1jfaOwPaRt7vl","timestamp":"2025-07-12 17:33:23"} 
[2025-07-12 17:34:46] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"jFGRHs2l9kRLtvPvg2m1qsAr5Re1jfaOwPaRt7vl","timestamp":"2025-07-12 17:34:46"} 
[2025-07-12 17:35:17] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"jFGRHs2l9kRLtvPvg2m1qsAr5Re1jfaOwPaRt7vl","timestamp":"2025-07-12 17:35:17"} 
[2025-07-12 17:38:29] local.INFO: Dashboard access {"user_id":16,"user_email":"<EMAIL>","user_role":"member","ip_address":"127.0.0.1"} 
[2025-07-12 17:38:30] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"jFGRHs2l9kRLtvPvg2m1qsAr5Re1jfaOwPaRt7vl","timestamp":"2025-07-12 17:38:30"} 
[2025-07-12 19:17:07] local.INFO: Available equipment for members {"count":3,"equipment":{"Illuminate\\Support\\Collection":["Combine Harvester","John Deere Tractor 5055E","Seed Planter"]}} 
[2025-07-12 19:17:10] local.INFO: Available equipment for members {"count":3,"equipment":{"Illuminate\\Support\\Collection":["Combine Harvester","John Deere Tractor 5055E","Seed Planter"]}} 
[2025-07-12 20:20:48] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 20:20:48"} 
[2025-07-12 20:41:18] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\my-backend\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformations.php:9)
[stacktrace]
#0 {main}
"} 
[2025-07-12 20:41:54] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 20:41:54"} 
[2025-07-12 20:42:16] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 20:42:16"} 
[2025-07-12 20:43:20] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 20:43:20"} 
[2025-07-12 20:44:23] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 20:44:23"} 
[2025-07-12 20:55:40] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 20:55:40"} 
[2025-07-12 21:13:46] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 21:13:46"} 
[2025-07-12 21:13:52] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 21:13:52"} 
[2025-07-12 21:21:42] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 21:21:42"} 
[2025-07-12 21:24:52] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 21:24:51"} 
[2025-07-12 21:31:25] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 21:31:25"} 
[2025-07-12 21:31:42] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"hoV1xKsoXYmA8IxHUM8QS3PNCC8GLY17ouOXyb7Y","timestamp":"2025-07-12 21:31:42"} 
[2025-07-12 21:34:40] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"F2hnZ2Z5VQrpz6zlhPXYwzbjRgvk1qerUrmqYBc0","timestamp":"2025-07-12 21:34:40"} 
[2025-07-12 21:37:56] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"F2hnZ2Z5VQrpz6zlhPXYwzbjRgvk1qerUrmqYBc0","timestamp":"2025-07-12 21:37:56"} 
[2025-07-12 21:38:24] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"F2hnZ2Z5VQrpz6zlhPXYwzbjRgvk1qerUrmqYBc0","timestamp":"2025-07-12 21:38:24"} 
[2025-07-12 21:44:12] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"F2hnZ2Z5VQrpz6zlhPXYwzbjRgvk1qerUrmqYBc0","timestamp":"2025-07-12 21:44:10"} 
[2025-07-12 21:44:16] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"F2hnZ2Z5VQrpz6zlhPXYwzbjRgvk1qerUrmqYBc0","timestamp":"2025-07-12 21:44:16"} 
[2025-07-12 21:45:35] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"F2hnZ2Z5VQrpz6zlhPXYwzbjRgvk1qerUrmqYBc0","timestamp":"2025-07-12 21:45:35"} 
[2025-07-12 21:45:36] local.INFO: Member dashboard accessed {"user_id":16,"user_email":"<EMAIL>","session_id":"F2hnZ2Z5VQrpz6zlhPXYwzbjRgvk1qerUrmqYBc0","timestamp":"2025-07-12 21:45:36"} 
[2025-07-12 21:48:10] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 21:48:10"} 
[2025-07-12 21:52:29] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 21:52:29"} 
[2025-07-12 21:52:38] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 21:52:38"} 
[2025-07-12 21:53:26] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 21:53:26"} 
[2025-07-12 21:55:11] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 21:55:11"} 
[2025-07-12 21:55:14] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 21:55:14"} 
[2025-07-12 22:01:30] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 22:01:29"} 
[2025-07-12 22:04:20] local.ERROR: Maximum execution time of 60 seconds exceeded {"userId":11,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\my-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:430)
[stacktrace]
#0 {main}
"} 
[2025-07-12 22:06:16] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 22:06:16"} 
[2025-07-12 22:27:53] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 22:27:51"} 
[2025-07-12 22:27:58] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 22:27:58"} 
[2025-07-12 22:29:50] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 22:29:47"} 
[2025-07-12 22:33:38] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 22:33:38"} 
[2025-07-12 22:35:16] local.INFO: Member dashboard accessed {"user_id":11,"user_email":"<EMAIL>","session_id":"JHmRrCZb7NWTK5djbWsVYLf93AEyoLPx8BheUfT5","timestamp":"2025-07-12 22:35:16"} 
[2025-07-12 23:21:13] local.INFO: Member dashboard accessed {"user_id":2,"user_email":"<EMAIL>","session_id":"lExt08GPvDj5oGpYOgYv4liqjFBUmYK7bzkb6PAB","timestamp":"2025-07-12 23:21:12"} 
[2025-07-12 23:29:44] local.INFO: Membership registration started {"request_data":{"full_name":"Itachi uchiha","national_id":"0K6M3AK8","phone_number":"+************","email":"<EMAIL>","land_size":"6","village_location":"Bereu","membership_fee_paid":true}} 
[2025-07-12 23:29:45] local.INFO: Password generated {"password":"Itachi329^"} 
[2025-07-12 23:29:45] local.INFO: Auto-approval check {"auto_approve":true} 
[2025-07-12 23:29:45] local.INFO: Creating approved member  
[2025-07-12 23:29:45] local.INFO: Creating user account {"email":"<EMAIL>"} 
[2025-07-12 23:29:46] local.INFO: User created {"user_id":17,"role_id":2} 
[2025-07-12 23:29:46] local.INFO: Sending welcome email {"user_email":"<EMAIL>"} 
[2025-07-12 23:29:46] local.INFO: Attempting to send welcome email {"to":"<EMAIL>","name":"Itachi uchiha","password_length":10} 
[2025-07-12 23:30:07] local.INFO: Welcome email sent successfully to: <EMAIL>  
[2025-07-12 23:30:07] local.INFO: Email send result {"email_sent":true} 
[2025-07-12 23:30:08] local.INFO: Member dashboard accessed {"user_id":2,"user_email":"<EMAIL>","session_id":"lExt08GPvDj5oGpYOgYv4liqjFBUmYK7bzkb6PAB","timestamp":"2025-07-12 23:30:08"} 
[2025-07-12 23:31:26] local.INFO: Member dashboard accessed {"user_id":2,"user_email":"<EMAIL>","session_id":"lExt08GPvDj5oGpYOgYv4liqjFBUmYK7bzkb6PAB","timestamp":"2025-07-12 23:31:26"} 
[2025-07-12 23:32:06] local.INFO: Member dashboard accessed {"user_id":17,"user_email":"<EMAIL>","session_id":"DhBQfpKLVBykFdKseVsxbe2kuuVdRjV5SIcR8FGI","timestamp":"2025-07-12 23:32:06"} 
[2025-07-12 23:32:24] local.INFO: Member dashboard accessed {"user_id":17,"user_email":"<EMAIL>","session_id":"DhBQfpKLVBykFdKseVsxbe2kuuVdRjV5SIcR8FGI","timestamp":"2025-07-12 23:32:24"} 
[2025-07-12 23:34:19] local.INFO: Membership registration started {"request_data":{"full_name":"Cosmas Henrico","national_id":"ID0721","phone_number":"+************","email":"<EMAIL>","land_size":"4","village_location":"Bereu","membership_fee_paid":true}} 
[2025-07-12 23:34:19] local.INFO: Password generated {"password":"Cosmas630&"} 
[2025-07-12 23:34:19] local.INFO: Auto-approval check {"auto_approve":true} 
[2025-07-12 23:34:19] local.INFO: Creating approved member  
[2025-07-12 23:34:19] local.INFO: Creating user account {"email":"<EMAIL>"} 
[2025-07-12 23:34:19] local.INFO: User created {"user_id":18,"role_id":2} 
[2025-07-12 23:34:20] local.INFO: Sending welcome email {"user_email":"<EMAIL>"} 
[2025-07-12 23:34:20] local.INFO: Attempting to send welcome email {"to":"<EMAIL>","name":"Cosmas Henrico","password_length":10} 
[2025-07-12 23:34:25] local.INFO: Welcome email sent successfully to: <EMAIL>  
[2025-07-12 23:34:25] local.INFO: Email send result {"email_sent":true} 
[2025-07-12 23:35:54] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"Q0cdfzVj5tCptx9B5qaFxnNpk6eSKGGlBRwz3Pku","timestamp":"2025-07-12 23:35:54"} 
[2025-07-12 23:38:19] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"Q0cdfzVj5tCptx9B5qaFxnNpk6eSKGGlBRwz3Pku","timestamp":"2025-07-12 23:38:19"} 
[2025-07-12 23:38:27] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"Q0cdfzVj5tCptx9B5qaFxnNpk6eSKGGlBRwz3Pku","timestamp":"2025-07-12 23:38:27"} 
[2025-07-12 23:38:29] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"Q0cdfzVj5tCptx9B5qaFxnNpk6eSKGGlBRwz3Pku","timestamp":"2025-07-12 23:38:29"} 
[2025-07-13 09:31:13] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\my-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:816)
[stacktrace]
#0 {main}
"} 
[2025-07-13 09:32:08] local.ERROR: Login error: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `users` where `email` = <EMAIL> limit 1)  
[2025-07-13 09:32:52] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"JjLCvb1zfBRTzr8K0aMNefNlm8A4UM2XcIQKtDqt","timestamp":"2025-07-13 09:32:52"} 
[2025-07-13 09:41:07] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 09:41:07"} 
[2025-07-13 09:42:40] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 09:42:40"} 
[2025-07-13 09:42:45] local.INFO: Available equipment for members {"count":3,"equipment":{"Illuminate\\Support\\Collection":["Combine Harvester","John Deere Tractor 5055E","Seed Planter"]}} 
[2025-07-13 09:53:20] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 09:53:20"} 
[2025-07-13 09:53:29] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 09:53:29"} 
[2025-07-13 09:53:31] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 09:53:31"} 
[2025-07-13 09:55:49] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 09:55:49"} 
[2025-07-13 10:01:52] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 10:01:51"} 
[2025-07-13 10:02:00] local.ERROR: Maximum execution time of 60 seconds exceeded {"userId":18,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\my-backend\\vendor\\tightenco\\ziggy\\src\\BladeRouteGenerator.php:38)
[stacktrace]
#0 {main}
"} 
[2025-07-13 10:02:53] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 10:02:53"} 
[2025-07-13 10:37:02] local.ERROR: Maximum execution time of 60 seconds exceeded {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\my-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:18)
[stacktrace]
#0 {main}
"} 
[2025-07-13 10:55:26] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 10:55:23"} 
[2025-07-13 10:59:26] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 10:59:26"} 
[2025-07-13 11:00:07] local.INFO: Member dashboard accessed {"user_id":18,"user_email":"<EMAIL>","session_id":"CXXl2yBavrOdocLO7Ap6W8LN9vEcDmrvchi5uu79","timestamp":"2025-07-13 11:00:07"} 
