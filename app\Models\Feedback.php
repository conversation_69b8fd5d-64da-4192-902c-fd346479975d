<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Feedback extends Model
{
    use HasFactory;

    protected $table = 'feedback';

    protected $fillable = [
        'title',
        'message',
        'type',
        'category',
        'priority',
        'status',
        'submitted_by',
        'assigned_to',
        'admin_response',
        'responded_at',
        'is_anonymous',
        'attachments',
    ];

    protected $casts = [
        'is_anonymous' => 'boolean',
        'attachments' => 'array',
        'responded_at' => 'datetime',
    ];

    /**
     * Get the user who submitted the feedback.
     */
    public function submitter()
    {
        return $this->belongsTo(User::class, 'submitted_by');
    }

    /**
     * Get the admin assigned to handle the feedback.
     */
    public function assignedAdmin()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the submitter's name (considering anonymity).
     */
    public function getSubmitterNameAttribute()
    {
        if ($this->is_anonymous) {
            return 'Anonymous';
        }

        return $this->submitter->farmer->name ?? $this->submitter->name;
    }

    /**
     * Check if feedback is pending.
     */
    public function getIsPendingAttribute()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if feedback is resolved.
     */
    public function getIsResolvedAttribute()
    {
        return in_array($this->status, ['resolved', 'closed']);
    }

    /**
     * Get priority color for UI.
     */
    public function getPriorityColorAttribute()
    {
        switch ($this->priority) {
            case 'urgent':
                return 'bg-red-100 text-red-800';
            case 'high':
                return 'bg-orange-100 text-orange-800';
            case 'medium':
                return 'bg-yellow-100 text-yellow-800';
            case 'low':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'under_review':
                return 'bg-blue-100 text-blue-800';
            case 'resolved':
                return 'bg-green-100 text-green-800';
            case 'closed':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Get type color for UI.
     */
    public function getTypeColorAttribute()
    {
        switch ($this->type) {
            case 'complaint':
                return 'bg-red-100 text-red-800';
            case 'suggestion':
                return 'bg-blue-100 text-blue-800';
            case 'compliment':
                return 'bg-green-100 text-green-800';
            case 'question':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }
}
