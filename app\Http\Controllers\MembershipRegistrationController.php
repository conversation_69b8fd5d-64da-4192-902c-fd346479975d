<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Farmer;
use App\Models\MemberApplication;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class MembershipRegistrationController extends Controller
{
    /**
     * Show the membership registration form
     */
    public function showRegistrationForm()
    {
        return Inertia::render('MembershipRegistration');
    }

    /**
     * Handle membership registration
     */
    public function register(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'full_name' => 'required|string|max:255',
            'national_id' => 'required|string|unique:users,identification_number|unique:farmers,identification_number',
            'phone_number' => 'required|string|max:20',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
            'land_size' => 'required|numeric|min:0.5',
            'village_location' => 'required|string|max:255',
            'membership_fee_paid' => 'required|accepted',
        ], [
            'national_id.unique' => 'This National ID is already registered.',
            'email.unique' => 'This email address is already registered.',
            'land_size.min' => 'Minimum land size required is 0.5 hectares.',
            'membership_fee_paid.accepted' => 'You must confirm that the membership fee has been paid.',
        ]);

        try {
            // Check if application meets auto-approval criteria
            $autoApprove = $this->shouldAutoApprove($validated);

            if ($autoApprove) {
                // Auto-approve and create user account
                return $this->createApprovedMember($validated);
            } else {
                // Save as pending application for manual review
                return $this->savePendingApplication($validated);
            }
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Registration failed: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Determine if application should be auto-approved
     */
    private function shouldAutoApprove($data)
    {
        // Auto-approve criteria:
        // 1. Land size >= 0.5 hectares ✓ (already validated)
        // 2. Membership fee paid ✓ (already validated)
        // 3. All required fields filled ✓ (already validated)
        // 4. Valid email and phone ✓ (already validated)
        
        // Additional criteria can be added here
        // For now, auto-approve if all basic requirements are met
        return true;
    }

    /**
     * Create approved member account
     */
    private function createApprovedMember($data)
    {
        return DB::transaction(function () use ($data) {
            // Create user account
            $user = User::create([
                'name' => $data['full_name'],
                'email' => $data['email'],
                'phone' => $data['phone_number'],
                'password' => Hash::make($data['password']),
                'identification_number' => $data['national_id'],
                'role_id' => 3, // Member role
                'email_verified_at' => now(),
            ]);

            // Create farmer profile
            $farmer = Farmer::create([
                'user_id' => $user->id,
                'identification_number' => $data['national_id'],
                'land_size' => $data['land_size'],
                'address' => $data['village_location'],
                'membership_date' => now(),
                'status' => 'active',
            ]);

            // Log the activity
            ActivityLog::create([
                'user_id' => $user->id,
                'action' => 'auto_approved_registration',
                'subject_type' => Farmer::class,
                'subject_id' => $farmer->id,
                'description' => "Auto-approved membership registration for {$data['full_name']}",
            ]);

            // Send welcome email with credentials
            $this->sendWelcomeEmail($user, $data['password']);

            return redirect()->route('login')
                ->with('success', 'Congratulations! Your membership has been approved. Please check your email for login credentials.');
        });
    }

    /**
     * Save pending application for manual review
     */
    private function savePendingApplication($data)
    {
        $application = MemberApplication::create([
            'name' => $data['full_name'],
            'email' => $data['email'],
            'phone' => $data['phone_number'],
            'identification_number' => $data['national_id'],
            'land_size' => $data['land_size'],
            'address' => $data['village_location'],
            'status' => 'pending',
            'admin_notes' => 'Membership fee confirmed as paid.',
        ]);

        // Store the password temporarily (encrypted) for later use
        $application->update([
            'admin_notes' => $application->admin_notes . ' | Temp Password: ' . encrypt($data['password'])
        ]);

        return redirect()->route('login')
            ->with('info', 'Thank you for your application! Your membership is under review. You will receive an email with your login credentials once approved.');
    }

    /**
     * Send welcome email with credentials
     */
    private function sendWelcomeEmail($user, $plainPassword)
    {
        try {
            Mail::send('emails.welcome-member', [
                'user' => $user,
                'password' => $plainPassword,
                'loginUrl' => route('login'),
            ], function ($message) use ($user) {
                $message->to($user->email, $user->name)
                    ->subject('Welcome to Siyamphanje Agricultural Cooperative!');
            });
        } catch (\Exception $e) {
            // Log email failure but don't fail the registration
            \Log::error('Failed to send welcome email: ' . $e->getMessage());
        }
    }

    /**
     * Show success page
     */
    public function success()
    {
        return Inertia::render('MembershipSuccess');
    }

    /**
     * Check if email or national ID already exists
     */
    public function checkAvailability(Request $request)
    {
        $field = $request->get('field');
        $value = $request->get('value');

        $exists = false;

        if ($field === 'email') {
            $exists = User::where('email', $value)->exists() || 
                     MemberApplication::where('email', $value)->exists();
        } elseif ($field === 'national_id') {
            $exists = User::where('identification_number', $value)->exists() || 
                     Farmer::where('identification_number', $value)->exists() ||
                     MemberApplication::where('identification_number', $value)->exists();
        }

        return response()->json(['available' => !$exists]);
    }
}
