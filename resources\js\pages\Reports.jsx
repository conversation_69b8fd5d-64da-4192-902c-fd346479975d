import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import { router } from '@inertiajs/react';

const Reports = () => {
  const [selectedReport, setSelectedReport] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [generatedReport, setGeneratedReport] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const reportTypes = [
    { id: 'financial', name: 'Financial Summary', description: 'Overview of all financial transactions' },
    { id: 'members', name: 'Members Report', description: 'Detailed list of all members with their status' },
    { id: 'loans', name: 'Loans Portfolio', description: 'Current loan status and repayment progress' },
    { id: 'savings', name: 'Savings Report', description: 'Member savings contributions and balances' },
    { id: 'contributions', name: 'Contributions Report', description: 'Member contributions and arrears' },
    { id: 'meetings', name: 'Meetings Attendance', description: 'Attendance records for all meetings' },
  ];

  const handleGenerateReport = () => {
    if (!selectedReport) return;
    
    setIsGenerating(true);
    setGeneratedReport(null); // Clear previous report

    router.post(route('admin.reports.generate'), {
      reportType: selectedReport,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    }, {
      onSuccess: (page) => {
        setGeneratedReport(page.props.reportData);
        setIsGenerating(false);
      },
      onError: (errors) => {
        console.error("Error generating report:", errors);
        setIsGenerating(false);
      },
    });
  };

  const handleDownload = (format) => {
    if (!generatedReport) return; // Only allow download if a report has been generated

    const url = route('admin.reports.download', {
      reportType: selectedReport,
      format: format,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    });
    window.open(url, '_blank'); // Open in new tab to trigger download
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800">Cooperative Reports</h1>
          {generatedReport && (
            <div className="flex gap-3">
              <button 
                onClick={() => handleDownload('PDF')}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <i className="fas fa-file-pdf"></i> PDF
              </button>
              <button 
                onClick={() => handleDownload('Excel')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <i className="fas fa-file-excel"></i> Excel
              </button>
              <button 
                onClick={() => handleDownload('CSV')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <i className="fas fa-file-csv"></i> CSV
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Report Selection Panel */}
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Select Report Type</h2>
            
            <div className="space-y-4 mb-6">
              {reportTypes.map((report) => (
                <div 
                  key={report.id}
                  onClick={() => setSelectedReport(report.id)}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedReport === report.id 
                      ? 'border-green-500 bg-green-50' 
                      : 'border-gray-200 hover:border-green-300'
                  }`}
                >
                  <h3 className="font-medium text-gray-800">{report.name}</h3>
                  <p className="text-sm text-gray-600">{report.description}</p>
                </div>
              ))}
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <div className="grid grid-cols-2 gap-3">
                <input
                  type="date"
                  className="border border-gray-300 rounded-md p-2 w-full"
                  value={dateRange.startDate}
                  onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                />
                <input
                  type="date"
                  className="border border-gray-300 rounded-md p-2 w-full"
                  value={dateRange.endDate}
                  onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
                />
              </div>
            </div>

            <button
              onClick={handleGenerateReport}
              disabled={!selectedReport || isGenerating}
              className={`w-full py-3 px-4 rounded-lg text-white font-medium ${
                !selectedReport || isGenerating
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-700'
              } transition-colors flex items-center justify-center gap-2`}
            >
              {isGenerating ? (
                <>
                  <i className="fas fa-spinner fa-spin"></i> Generating...
                </>
              ) : (
                <>
                  <i className="fas fa-chart-bar"></i> Generate Report
                </>
              )}
            </button>
          </div>

          {/* Report Display Area */}
          <div className="lg:col-span-2">
            {generatedReport ? (
              <div className="bg-white p-6 rounded-xl shadow-md">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800">{generatedReport.title}</h2>
                    <p className="text-gray-600">
                      Generated on: {generatedReport.dateGenerated} | 
                      Period: {generatedReport.period || 'All time'}
                    </p>
                  </div>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                    Ready
                  </span>
                </div>

                {selectedReport === 'financial' && generatedReport.data && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Savings</h3>
                        <p className="text-3xl font-bold text-green-600">
                          UGX {generatedReport.data.totalSavings.toLocaleString()}
                        </p>
                      </div>
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Loans</h3>
                        <p className="text-3xl font-bold text-purple-600">
                          UGX {generatedReport.data.totalLoans.toLocaleString()}
                        </p>
                      </div>
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Contributions</h3>
                        <p className="text-3xl font-bold text-blue-600">
                          UGX {generatedReport.data.totalContributions.toLocaleString()}
                        </p>
                      </div>
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Expenses</h3>
                        <p className="text-3xl font-bold text-red-600">
                          UGX {generatedReport.data.totalExpenses.toLocaleString()}
                        </p>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <h3 className="font-semibold text-gray-800 mb-2">Net Cooperative Balance</h3>
                      <p className="text-4xl font-bold text-green-700">
                        UGX {generatedReport.data.netBalance.toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {selectedReport === 'members' && generatedReport.totalMembers && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Members</h3>
                        <p className="text-3xl font-bold text-indigo-600">
                          {generatedReport.totalMembers}
                        </p>
                      </div>
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Active Members</h3>
                        <p className="text-3xl font-bold text-green-600">
                          {generatedReport.activeMembers}
                        </p>
                      </div>
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Inactive Members</h3>
                        <p className="text-3xl font-bold text-red-600">
                          {generatedReport.inactiveMembers}
                        </p>
                      </div>
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">New Members</h3>
                        <p className="text-3xl font-bold text-blue-600">
                          {generatedReport.newMembersThisPeriod}
                        </p>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <h3 className="font-semibold text-gray-800 mb-3">Membership Growth</h3>
                      <div className="h-64 bg-white rounded p-4 flex items-center justify-center border border-gray-200">
                        <p className="text-gray-500">Membership growth chart would appear here</p>
                      </div>
                    </div>
                  </div>
                )}

                {selectedReport === 'loans' && generatedReport.data && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Loans</h3>
                        <p className="text-3xl font-bold text-purple-600">
                          UGX {generatedReport.data.totalLoans.toLocaleString()}
                        </p>
                      </div>
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Outstanding Loans</h3>
                        <p className="text-3xl font-bold text-red-600">
                          UGX {generatedReport.data.outstandingLoans.toLocaleString()}
                        </p>
                      </div>
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Paid Loans</h3>
                        <p className="text-3xl font-bold text-green-600">
                          UGX {generatedReport.data.paidLoans.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {selectedReport === 'savings' && generatedReport.data && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Savings</h3>
                        <p className="text-3xl font-bold text-green-600">
                          UGX {generatedReport.data.totalSavings.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {selectedReport === 'contributions' && generatedReport.data && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Contributions</h3>
                        <p className="text-3xl font-bold text-blue-600">
                          UGX {generatedReport.data.totalContributions.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {selectedReport === 'meetings' && generatedReport.data && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-2">Total Meetings Attended</h3>
                        <p className="text-3xl font-bold text-indigo-600">
                          {generatedReport.data.totalMeetings}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {!['financial', 'members', 'loans', 'savings', 'contributions', 'meetings'].includes(selectedReport) && (
                  <div className="text-center py-12">
                    <i className="fas fa-chart-pie text-5xl text-gray-300 mb-4"></i>
                    <h3 className="text-xl font-medium text-gray-600">Sample Report Preview</h3>
                    <p className="text-gray-500 mt-2">
                      The actual {reportTypes.find(r => r.id === selectedReport)?.name} content would appear here
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-white p-12 rounded-xl shadow-md text-center">
                <i className="fas fa-chart-bar text-5xl text-gray-300 mb-4"></i>
                <h3 className="text-xl font-medium text-gray-600">No Report Generated</h3>
                <p className="text-gray-500 mt-2">
                  Select a report type and click "Generate Report" to view data
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;