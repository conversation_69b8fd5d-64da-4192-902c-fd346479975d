<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\Meeting;
use App\Models\MeetingAttendance;
use App\Models\User;
use Carbon\Carbon;

class EventController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the event calendar.
     */
    public function index(Request $request)
    {
        $view = $request->get('view', 'month'); // month, week, list
        $date = $request->get('date', Carbon::now()->format('Y-m-d'));
        $type = $request->get('type');

        $query = Event::with(['creator', 'attendees.user.farmer'])
            ->where('is_public', true);

        // Filter by type
        if ($type && $type !== 'all') {
            $query->where('type', $type);
        }

        // Filter by date range based on view
        $startDate = Carbon::parse($date);
        switch ($view) {
            case 'week':
                $endDate = $startDate->copy()->endOfWeek();
                $startDate = $startDate->startOfWeek();
                break;
            case 'month':
                $endDate = $startDate->copy()->endOfMonth();
                $startDate = $startDate->startOfMonth();
                break;
            default: // list view - show next 3 months
                $endDate = $startDate->copy()->addMonths(3);
                break;
        }

        $query->whereBetween('event_date', [$startDate, $endDate]);

        $events = $query->orderBy('event_date')
            ->orderBy('start_time')
            ->get()
            ->map(function ($event) {
                $userAttendance = null;
                if (auth()->check()) {
                    $userAttendance = $event->attendees()
                        ->where('user_id', auth()->id())
                        ->first();
                }

                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'description' => $event->description,
                    'type' => $event->type,
                    'event_date' => $event->event_date,
                    'start_time' => $event->start_time,
                    'end_time' => $event->end_time,
                    'location' => $event->location,
                    'agenda' => $event->agenda,
                    'max_attendees' => $event->max_attendees,
                    'status' => $event->status,
                    'created_by' => $event->creator->name ?? 'Unknown',
                    'is_upcoming' => $event->is_upcoming,
                    'is_past' => $event->is_past,
                    'formatted_date_time' => $event->formatted_date_time,
                    'attendance_stats' => $event->attendance_stats,
                    'has_available_spots' => $event->hasAvailableSpots(),
                    'user_attendance' => $userAttendance ? [
                        'response_status' => $userAttendance->response_status,
                        'attended' => $userAttendance->attended,
                        'comments' => $userAttendance->comments,
                    ] : null,
                ];
            });

        // Get meetings and convert them to event format
        $meetingsQuery = Meeting::with(['attendances.farmer.user', 'calledBy']);

        // Apply similar date filtering for meetings
        $meetingsQuery->whereBetween('meeting_date', [$startDate, $endDate]);

        // Apply type filtering for meetings
        if ($type && $type !== 'all') {
            // Only include meetings if the type filter is 'meeting' or 'all'
            if ($type === 'meeting') {
                // Include all meeting types
            } else {
                // If filtering for other types, exclude meetings by setting impossible condition
                $meetingsQuery->where('id', -1);
            }
        }

        $meetings = $meetingsQuery->orderBy('meeting_date')
            ->orderBy('meeting_time')
            ->get()
            ->map(function ($meeting) {
                $userAttendance = null;
                if (auth()->check() && auth()->user()->farmer) {
                    $userAttendance = $meeting->attendances()
                        ->where('farmer_id', auth()->user()->farmer->id)
                        ->first();
                }

                return [
                    'id' => 'meeting_' . $meeting->id, // Prefix to distinguish from events
                    'title' => $meeting->title,
                    'description' => $meeting->description,
                    'type' => 'meeting', // Set type as meeting
                    'meeting_type' => $meeting->meeting_type, // Keep original meeting type
                    'event_date' => $meeting->meeting_date,
                    'start_time' => $meeting->meeting_time,
                    'end_time' => null, // Meetings don't have end time
                    'location' => $meeting->location,
                    'agenda' => $meeting->agenda,
                    'max_attendees' => $meeting->max_attendees,
                    'status' => $meeting->status === 'scheduled' ? 'upcoming' : $meeting->status,
                    'created_by' => $meeting->calledBy->name ?? 'Unknown',
                    'is_upcoming' => $meeting->meeting_date >= Carbon::today(),
                    'is_past' => $meeting->meeting_date < Carbon::today(),
                    'formatted_date_time' => Carbon::parse($meeting->meeting_time)->format('M j, Y \a\t g:i A'),
                    'attendance_stats' => [
                        'total_attendees' => $meeting->attendances->count(),
                        'attending' => $meeting->attendances->where('response_status', 'attending')->count(),
                        'not_attending' => $meeting->attendances->where('response_status', 'not_attending')->count(),
                        'maybe' => $meeting->attendances->where('response_status', 'maybe')->count(),
                        'pending' => $meeting->attendances->where('response_status', 'pending')->count(),
                    ],
                    'has_available_spots' => $meeting->max_attendees ?
                        $meeting->attendances->where('response_status', 'attending')->count() < $meeting->max_attendees :
                        true,
                    'user_attendance' => $userAttendance ? [
                        'response_status' => $userAttendance->response_status,
                        'attended' => $userAttendance->attended,
                        'comments' => $userAttendance->comments ?? null,
                    ] : null,
                    'is_meeting' => true, // Flag to identify meetings
                ];
            });

        // Combine events and meetings, then sort by date
        $allEvents = $events->concat($meetings)->sortBy(function ($item) {
            return $item['event_date'] . ' ' . ($item['start_time'] ?? '00:00:00');
        })->values();

        $stats = [
            'total_events' => Event::where('is_public', true)->count() + Meeting::count(),
            'upcoming_events' => Event::where('is_public', true)
                ->where('event_date', '>=', Carbon::today())
                ->count() + Meeting::where('meeting_date', '>=', Carbon::today())->count(),
            'this_month' => Event::where('is_public', true)
                ->whereMonth('event_date', Carbon::now()->month)
                ->whereYear('event_date', Carbon::now()->year)
                ->count() + Meeting::whereMonth('meeting_date', Carbon::now()->month)
                ->whereYear('meeting_date', Carbon::now()->year)
                ->count(),
            'total_meetings' => Meeting::count(),
            'upcoming_meetings' => Meeting::where('meeting_date', '>=', Carbon::today())->count(),
        ];

        return Inertia::render('Events/Index', [
            'events' => $allEvents,
            'stats' => $stats,
            'filters' => [
                'view' => $view,
                'date' => $date,
                'type' => $type,
            ],
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Store a new event (Admin only).
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $user = \App\Models\User::with('role')->find($user->id);

        if (!$user || !$user->role || $user->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'type' => 'required|in:meeting,training,field_day,social,market_day,other',
            'event_date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'location' => 'required|string|max:255',
            'agenda' => 'nullable|string|max:2000',
            'max_attendees' => 'nullable|integer|min:1',
            'is_public' => 'boolean',
        ]);

        $event = Event::create([
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'event_date' => $request->event_date,
            'start_time' => Carbon::createFromFormat('H:i', $request->start_time),
            'end_time' => $request->end_time ? Carbon::createFromFormat('H:i', $request->end_time) : null,
            'location' => $request->location,
            'agenda' => $request->agenda,
            'max_attendees' => $request->max_attendees,
            'is_public' => $request->boolean('is_public', true),
            'created_by' => auth()->id(),
        ]);

        // Auto-invite all active members if it's a public event
        if ($event->is_public) {
            $activeUsers = User::whereHas('farmer', function ($query) {
                $query->where('status', 'active');
            })->get();

            foreach ($activeUsers as $user) {
                EventAttendee::create([
                    'event_id' => $event->id,
                    'user_id' => $user->id,
                    'response_status' => 'pending',
                ]);
            }
        }

        return redirect()->back()->with('success', 'Event created successfully!');
    }

    /**
     * Update event attendance response.
     */
    public function updateAttendance(Request $request, $eventId)
    {
        $request->validate([
            'response_status' => 'required|in:attending,not_attending,maybe',
            'comments' => 'nullable|string|max:500',
        ]);

        // Check if this is a meeting (prefixed with 'meeting_')
        if (str_starts_with($eventId, 'meeting_')) {
            $meetingId = str_replace('meeting_', '', $eventId);
            $meeting = Meeting::findOrFail($meetingId);

            // Get the user's farmer profile
            $farmer = auth()->user()->farmer;
            if (!$farmer) {
                return redirect()->back()->with('error', 'No farmer profile found. Please contact admin.');
            }

            // Check if meeting has available spots
            if ($request->response_status === 'attending' && $meeting->max_attendees) {
                $currentAttending = $meeting->attendances()->where('response_status', 'attending')->count();
                if ($currentAttending >= $meeting->max_attendees) {
                    return redirect()->back()->with('error', 'This meeting is full. No more spots available.');
                }
            }

            MeetingAttendance::updateOrCreate(
                [
                    'meeting_id' => $meetingId,
                    'farmer_id' => $farmer->id,
                ],
                [
                    'response_status' => $request->response_status,
                    'comments' => $request->comments,
                ]
            );

            return redirect()->back()->with('success', 'Your meeting response has been recorded!');
        } else {
            // Handle regular events
            $event = Event::findOrFail($eventId);

            // Check if event has available spots
            if ($request->response_status === 'attending' && !$event->hasAvailableSpots()) {
                return redirect()->back()->with('error', 'This event is full. No more spots available.');
            }

            EventAttendee::updateOrCreate(
                [
                    'event_id' => $eventId,
                    'user_id' => auth()->id(),
                ],
                [
                    'response_status' => $request->response_status,
                    'comments' => $request->comments,
                    'responded_at' => now(),
                ]
            );

            return redirect()->back()->with('success', 'Your event response has been recorded!');
        }
    }

    /**
     * Show event details.
     */
    public function show($id)
    {
        $event = Event::with(['creator', 'attendees.user.farmer'])
            ->findOrFail($id);

        if (!$event->is_public && auth()->user()->role->name !== 'admin') {
            abort(403, 'This event is not public.');
        }

        $userAttendance = null;
        if (auth()->check()) {
            $userAttendance = $event->attendees()
                ->where('user_id', auth()->id())
                ->first();
        }

        $eventData = [
            'id' => $event->id,
            'title' => $event->title,
            'description' => $event->description,
            'type' => $event->type,
            'event_date' => $event->event_date,
            'start_time' => $event->start_time,
            'end_time' => $event->end_time,
            'location' => $event->location,
            'agenda' => $event->agenda,
            'max_attendees' => $event->max_attendees,
            'status' => $event->status,
            'created_by' => $event->creator->name ?? 'Unknown',
            'is_upcoming' => $event->is_upcoming,
            'is_past' => $event->is_past,
            'formatted_date_time' => $event->formatted_date_time,
            'attendance_stats' => $event->attendance_stats,
            'has_available_spots' => $event->hasAvailableSpots(),
            'user_attendance' => $userAttendance ? [
                'response_status' => $userAttendance->response_status,
                'attended' => $userAttendance->attended,
                'comments' => $userAttendance->comments,
            ] : null,
            'attendees' => $event->attendees->map(function ($attendee) {
                return [
                    'id' => $attendee->id,
                    'farmer_name' => $attendee->farmer_name,
                    'response_status' => $attendee->response_status,
                    'attended' => $attendee->attended,
                    'comments' => $attendee->comments,
                ];
            }),
        ];

        return Inertia::render('Events/Show', [
            'event' => $eventData,
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Admin index for managing events
     */
    public function adminIndex(Request $request)
    {
        $user = auth()->user();
        $user = \App\Models\User::with('role')->find($user->id);

        if (!$user || !$user->role || $user->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $status = $request->get('status', 'all');
        $type = $request->get('type', 'all');
        $search = $request->get('search');

        $query = Event::with(['creator', 'attendees.user.farmer'])
            ->orderBy('event_date', 'desc');

        if ($status && $status !== 'all') {
            if ($status === 'upcoming') {
                $query->where('event_date', '>=', Carbon::today());
            } elseif ($status === 'past') {
                $query->where('event_date', '<', Carbon::today());
            }
        }

        if ($type && $type !== 'all') {
            $query->where('type', $type);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        $events = $query->get()->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'description' => $event->description,
                'type' => $event->type,
                'event_date' => $event->event_date,
                'start_time' => $event->start_time,
                'end_time' => $event->end_time,
                'location' => $event->location,
                'agenda' => $event->agenda,
                'max_attendees' => $event->max_attendees,
                'is_public' => $event->is_public,
                'created_by' => $event->creator->name ?? 'Unknown',
                'attendee_count' => $event->attendees->count(),
                'attending_count' => $event->attendees->where('response_status', 'attending')->count(),
                'is_upcoming' => $event->event_date >= Carbon::today(),
                'status' => $event->event_date >= Carbon::today() ? 'upcoming' : 'past',
            ];
        });

        $stats = [
            'total_events' => Event::count(),
            'upcoming_events' => Event::where('event_date', '>=', Carbon::today())->count(),
            'past_events' => Event::where('event_date', '<', Carbon::today())->count(),
            'total_attendees' => \App\Models\EventAttendee::where('response_status', 'attending')->count(),
        ];

        return Inertia::render('Admin/Events', [
            'events' => $events,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'type' => $type,
                'search' => $search,
            ],
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Admin show event details
     */
    public function adminShow($id)
    {
        $user = auth()->user();
        $user = \App\Models\User::with('role')->find($user->id);

        if (!$user || !$user->role || $user->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $event = Event::with(['creator', 'attendees.user.farmer'])
            ->findOrFail($id);

        $eventData = [
            'id' => $event->id,
            'title' => $event->title,
            'description' => $event->description,
            'type' => $event->type,
            'event_date' => $event->event_date,
            'start_time' => $event->start_time,
            'end_time' => $event->end_time,
            'location' => $event->location,
            'agenda' => $event->agenda,
            'max_attendees' => $event->max_attendees,
            'is_public' => $event->is_public,
            'created_by' => $event->creator->name ?? 'Unknown',
            'attendees' => $event->attendees->map(function ($attendee) {
                return [
                    'id' => $attendee->id,
                    'farmer_name' => $attendee->user->farmer->user->name ?? $attendee->user->name ?? 'Unknown',
                    'response_status' => $attendee->response_status,
                    'attended' => $attendee->attended,
                    'comments' => $attendee->comments,
                    'responded_at' => $attendee->responded_at,
                ];
            }),
        ];

        return Inertia::render('Admin/EventDetails', [
            'event' => $eventData,
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Update an event (Admin only)
     */
    public function update(Request $request, $id)
    {
        $user = auth()->user();
        $user = \App\Models\User::with('role')->find($user->id);

        if (!$user || !$user->role || $user->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $event = Event::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'type' => 'required|in:meeting,training,field_day,social,market_day,other',
            'event_date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'location' => 'required|string|max:255',
            'agenda' => 'nullable|string|max:2000',
            'max_attendees' => 'nullable|integer|min:1',
            'is_public' => 'boolean',
        ]);

        $event->update([
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'event_date' => $request->event_date,
            'start_time' => Carbon::createFromFormat('H:i', $request->start_time),
            'end_time' => $request->end_time ? Carbon::createFromFormat('H:i', $request->end_time) : null,
            'location' => $request->location,
            'agenda' => $request->agenda,
            'max_attendees' => $request->max_attendees,
            'is_public' => $request->boolean('is_public', true),
        ]);

        return redirect()->back()->with('success', 'Event updated successfully!');
    }

    /**
     * Delete an event (Admin only)
     */
    public function destroy($id)
    {
        $user = auth()->user();
        $user = \App\Models\User::with('role')->find($user->id);

        if (!$user || !$user->role || $user->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $event = Event::findOrFail($id);

        // Delete associated attendees first
        $event->attendees()->delete();

        // Delete the event
        $event->delete();

        return redirect()->route('admin.events.index')->with('success', 'Event deleted successfully!');
    }
}
