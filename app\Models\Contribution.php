<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contribution extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id',
        'farmer_id',
        'amount',
        'type',
        'description',
        'payment_method',
        'reference_number',
        'contribution_date',
        'status',
        'notes',
        'recorded_by'
    ];

    protected $attributes = [
        'farmer_id' => null,
    ];

    protected $casts = [
        'contribution_date' => 'date',
        'amount' => 'decimal:2',
    ];

    public function member()
    {
        return $this->belongsTo(User::class, 'member_id');
    }

    public function farmer()
    {
        return $this->belongsTo(Farmer::class);
    }

    public function recordedBy()
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    // Scopes
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('contribution_date', now()->month)
                    ->whereYear('contribution_date', now()->year);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('contribution_date', now()->year);
    }
}
