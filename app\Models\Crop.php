<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Crop extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'variety',
        'growth_duration_days',
        'description'
    ];

    public function farmers()
    {
        return $this->belongsToMany(Farmer::class, 'farmer_crops')
            ->withPivot('area_planted', 'planting_date', 'expected_harvest_date')
            ->withTimestamps();
    }

    public function farmerCrops()
    {
        return $this->hasMany(FarmerCrop::class);
    }

    public function harvests()
    {
        return $this->hasMany(Harvest::class);
    }

    public function sugarcaneRecords()
    {
        return $this->hasMany(SugarcaneRecord::class);
    }
}
