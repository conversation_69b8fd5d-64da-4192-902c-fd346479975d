import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

export default function MemberNotifications({ auth, notifications }) {
    const [selectedNotification, setSelectedNotification] = useState(null);
    const [showDetailsModal, setShowDetailsModal] = useState(false);

    const { post } = useForm();

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-ZM', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getNotificationTypeColor = (type) => {
        switch (type) {
            case 'general': return 'bg-blue-100 text-blue-800';
            case 'meeting': return 'bg-purple-100 text-purple-800';
            case 'loan': return 'bg-green-100 text-green-800';
            case 'equipment': return 'bg-orange-100 text-orange-800';
            case 'announcement': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getNotificationIcon = (type) => {
        switch (type) {
            case 'meeting':
                return (
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                );
            case 'loan':
                return (
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                    </svg>
                );
            case 'equipment':
                return (
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                );
            case 'announcement':
                return (
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                );
            default:
                return (
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                );
        }
    };

    const markAsRead = (notificationId) => {
        post(`/notifications/${notificationId}/read`);
    };

    const markAllAsRead = () => {
        post('/notifications/mark-all-read');
    };

    const deleteNotification = (notificationId) => {
        post(`/notifications/${notificationId}`, {
            _method: 'DELETE'
        });
    };

    const showNotificationDetails = (notification) => {
        setSelectedNotification(notification);
        setShowDetailsModal(true);
        if (!notification.is_read) {
            markAsRead(notification.id);
        }
    };

    const unreadCount = notifications?.filter(n => !n.is_read).length || 0;

    return (
        <MemberLayout user={auth.user} header="Notifications">
            <Head title="Notifications - Siyamphanje Cooperative" />
            
            <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-green-50 p-6">
                <div className="max-w-4xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
                                <p className="mt-2 text-gray-600">
                                    Stay updated with cooperative announcements and alerts
                                    {unreadCount > 0 && (
                                        <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            {unreadCount} unread
                                        </span>
                                    )}
                                </p>
                            </div>
                            <div className="flex space-x-3">
                                <Link
                                    href="/dashboard/member"
                                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    ← Back to Dashboard
                                </Link>
                                {unreadCount > 0 && (
                                    <button
                                        onClick={markAllAsRead}
                                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                                    >
                                        Mark All Read
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>



                    {/* Notifications List */}
                    {notifications && notifications.length > 0 ? (
                        <div className="space-y-4">
                            {notifications.map((notification) => (
                                <div
                                    key={notification.id}
                                    className={`bg-white rounded-xl shadow-lg overflow-hidden border-l-4 cursor-pointer transition-all duration-300 hover:shadow-xl ${
                                        notification.is_read 
                                            ? 'border-gray-300 opacity-75' 
                                            : 'border-blue-500'
                                    }`}
                                    onClick={() => showNotificationDetails(notification)}
                                >
                                    <div className="p-6">
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start space-x-4 flex-1">
                                                <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                                                    notification.is_read ? 'bg-gray-100 text-gray-600' : getNotificationTypeColor(notification.type)
                                                }`}>
                                                    {getNotificationIcon(notification.type)}
                                                </div>
                                                
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center justify-between mb-2">
                                                        <h3 className={`text-lg font-semibold truncate ${
                                                            notification.is_read ? 'text-gray-700' : 'text-gray-900'
                                                        }`}>
                                                            {notification.title}
                                                        </h3>
                                                        <div className="flex items-center space-x-2">
                                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getNotificationTypeColor(notification.type)}`}>
                                                                {notification.type}
                                                            </span>
                                                            {!notification.is_read && (
                                                                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                                                            )}
                                                        </div>
                                                    </div>
                                                    
                                                    <p className={`text-sm mb-3 line-clamp-2 ${
                                                        notification.is_read ? 'text-gray-600' : 'text-gray-700'
                                                    }`}>
                                                        {notification.message}
                                                    </p>
                                                    
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-xs text-gray-500">
                                                            {formatDate(notification.created_at)}
                                                        </span>
                                                        
                                                        <div className="flex space-x-2">
                                                            {!notification.is_read && (
                                                                <button
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        markAsRead(notification.id);
                                                                    }}
                                                                    className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                                                                >
                                                                    Mark as Read
                                                                </button>
                                                            )}
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    deleteNotification(notification.id);
                                                                }}
                                                                className="text-xs text-red-600 hover:text-red-800 font-medium"
                                                            >
                                                                Delete
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="bg-white rounded-xl shadow-lg p-12 text-center">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No Notifications</h3>
                            <p className="text-gray-600">You're all caught up! New notifications will appear here.</p>
                        </div>
                    )}
                </div>
            </div>

            {/* Notification Details Modal */}
            {showDetailsModal && selectedNotification && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <div className="flex justify-between items-start mb-4">
                                <div className="flex items-center space-x-3">
                                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getNotificationTypeColor(selectedNotification.type)}`}>
                                        {getNotificationIcon(selectedNotification.type)}
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold text-gray-900">
                                            {selectedNotification.title}
                                        </h3>
                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getNotificationTypeColor(selectedNotification.type)}`}>
                                            {selectedNotification.type}
                                        </span>
                                    </div>
                                </div>
                                <button
                                    onClick={() => setShowDetailsModal(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </button>
                            </div>

                            <div className="space-y-4">
                                <div>
                                    <h4 className="text-sm font-medium text-gray-700 mb-2">Message</h4>
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <p className="text-sm text-gray-900 whitespace-pre-wrap">{selectedNotification.message}</p>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h4 className="text-sm font-medium text-gray-700 mb-1">Received</h4>
                                        <p className="text-sm text-gray-900">{formatDate(selectedNotification.created_at)}</p>
                                    </div>

                                    <div>
                                        <h4 className="text-sm font-medium text-gray-700 mb-1">Status</h4>
                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                            selectedNotification.is_read
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-yellow-100 text-yellow-800'
                                        }`}>
                                            {selectedNotification.is_read ? 'Read' : 'Unread'}
                                        </span>
                                    </div>
                                </div>

                                {selectedNotification.data && (
                                    <div>
                                        <h4 className="text-sm font-medium text-gray-700 mb-2">Additional Information</h4>
                                        <div className="bg-blue-50 rounded-lg p-4">
                                            {selectedNotification.data.meeting_id && (
                                                <div className="space-y-2">
                                                    <p className="text-sm text-blue-900">
                                                        <strong>Meeting Date:</strong> {selectedNotification.data.meeting_date}
                                                    </p>
                                                    <p className="text-sm text-blue-900">
                                                        <strong>Meeting Time:</strong> {selectedNotification.data.meeting_time}
                                                    </p>
                                                    <p className="text-sm text-blue-900">
                                                        <strong>Location:</strong> {selectedNotification.data.location}
                                                    </p>
                                                    <Link
                                                        href="/meetings"
                                                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 mt-2"
                                                    >
                                                        View Meeting Details
                                                    </Link>
                                                </div>
                                            )}
                                            {selectedNotification.data.sent_by && (
                                                <p className="text-sm text-blue-900">
                                                    <strong>Sent by:</strong> {selectedNotification.data.sent_by}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                )}

                                <div className="border-t border-gray-200 pt-4">
                                    <div className="flex space-x-3">
                                        {!selectedNotification.is_read && (
                                            <button
                                                onClick={() => {
                                                    markAsRead(selectedNotification.id);
                                                    setShowDetailsModal(false);
                                                }}
                                                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                                            >
                                                Mark as Read
                                            </button>
                                        )}
                                        <button
                                            onClick={() => {
                                                deleteNotification(selectedNotification.id);
                                                setShowDetailsModal(false);
                                            }}
                                            className="px-4 py-2 border border-red-300 rounded-md text-red-700 hover:bg-red-50 transition-colors text-sm font-medium"
                                        >
                                            Delete
                                        </button>
                                        <button
                                            onClick={() => setShowDetailsModal(false)}
                                            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors text-sm font-medium"
                                        >
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </MemberLayout>
    );
}
