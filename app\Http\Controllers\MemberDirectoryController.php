<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\User;
use App\Models\Farmer;

class MemberDirectoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the member directory.
     */
    public function index(Request $request)
    {
        $search = $request->get('search');
        $status = $request->get('status');
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        $query = Farmer::with('user')
            ->where('status', '!=', 'inactive');

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhere('farmers.address', 'like', "%{$search}%")
                ->orWhere('farmers.farm_size', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($status && $status !== 'all') {
            $query->where('status', $status);
        }

        // Sorting
        if ($sortBy === 'name') {
            $query->join('users', 'farmers.user_id', '=', 'users.id')
                  ->orderBy('users.name', $sortOrder)
                  ->select('farmers.*');
        } else {
            $query->orderBy($sortBy, $sortOrder);
        }

        $members = $query->paginate(20);

        $stats = [
            'total_members' => Farmer::where('status', '!=', 'inactive')->count(),
            'active_members' => Farmer::where('status', 'active')->count(),
            'pending_members' => Farmer::where('status', 'pending')->count(),
            'suspended_members' => Farmer::where('status', 'suspended')->count(),
        ];

        return Inertia::render('MemberDirectory/Index', [
            'members' => $members,
            'stats' => $stats,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
            ],
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Display a specific member's details.
     */
    public function show($id)
    {
        $farmer = Farmer::with(['user', 'savingsAccounts', 'loans', 'contributions'])
            ->findOrFail($id);

        // Only show basic contact info to regular members
        // Full details only for admins
        $isAdmin = auth()->user()->role->name === 'admin';

        $memberData = [
            'id' => $farmer->id,
            'name' => $farmer->name,
            'phone' => $farmer->phone,
            'email' => $farmer->email,
            'location' => $farmer->location,
            'farm_size' => $farmer->farm_size,
            'crops' => $farmer->crops,
            'status' => $farmer->status,
            'membership_date' => $farmer->membership_date,
            'profile_picture' => $farmer->profile_picture,
        ];

        // Add financial info for admins only
        if ($isAdmin) {
            $memberData['financial_summary'] = [
                'total_savings' => $farmer->savingsAccounts()->sum('balance'),
                'total_contributions' => $farmer->contributions()->sum('amount'),
                'active_loans' => $farmer->loans()->where('status', 'approved')->count(),
                'total_loan_amount' => $farmer->loans()->where('status', 'approved')->sum('amount'),
            ];
        }

        return Inertia::render('MemberDirectory/Show', [
            'member' => $memberData,
            'isAdmin' => $isAdmin,
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Export member directory (Admin only).
     */
    public function export(Request $request)
    {
        if (auth()->user()->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $members = Farmer::with('user')
            ->where('status', '!=', 'inactive')
            ->orderBy('name')
            ->get();

        $csvData = [];
        $csvData[] = ['Name', 'Phone', 'Email', 'Location', 'Farm Size', 'Crops', 'Status', 'Membership Date'];

        foreach ($members as $member) {
            $csvData[] = [
                $member->name,
                $member->phone,
                $member->email,
                $member->location,
                $member->farm_size,
                $member->crops,
                $member->status,
                $member->membership_date ? $member->membership_date->format('Y-m-d') : '',
            ];
        }

        $filename = 'member_directory_' . date('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
