<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\EquipmentRequest;
use App\Models\Equipment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class EquipmentRequestController extends Controller
{
    /**
     * Display a listing of available equipment for members
     */
    public function index()
    {
        $user = auth()->user();

        // Get available equipment for rent
        $availableEquipment = Equipment::where('status', 'available')
            ->get();

        // Get member's equipment requests
        $memberRequests = EquipmentRequest::where('user_id', $user->id)
            ->with(['equipment', 'approvedBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Equipment/BorrowEquipment', [
            'auth' => ['user' => $user],
            'availableEquipment' => $availableEquipment->map(function ($equipment) {
                return [
                    'id' => $equipment->id,
                    'name' => $equipment->name,
                    'type' => $equipment->type,
                    'description' => $equipment->description,
                    'daily_rental_rate' => $equipment->daily_rental_rate,
                    'operator_rate' => $equipment->operator_rate,
                    'transport_rate' => $equipment->transport_rate,
                    'operator_required' => $equipment->operator_required,
                    'transport_available' => $equipment->transport_available,
                    'status' => $equipment->status,
                ];
            }),
            'memberRequests' => $memberRequests->map(function ($request) {
                return [
                    'id' => $request->id,
                    'equipment' => [
                        'name' => $request->equipment->name ?? 'Unknown Equipment'
                    ],
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date,
                    'purpose' => $request->purpose,
                    'status' => $request->status,
                    'payment_status' => $request->payment_status,
                    'estimated_cost' => $request->estimated_cost,
                ];
            }),
        ]);
    }

    /**
     * Store a newly created equipment request
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'equipment_id' => 'required|exists:equipment,id',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'purpose' => 'required|string|max:500',
            'operator_needed' => 'boolean',
            'transport_needed' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        $user = auth()->user();

        // Find the equipment
        $equipment = Equipment::findOrFail($validated['equipment_id']);

        // Calculate rental cost
        $startDate = Carbon::parse($validated['start_date']);
        $endDate = Carbon::parse($validated['end_date']);
        $days = $startDate->diffInDays($endDate) + 1; // Include both start and end dates

        $totalCost = $days * $equipment->daily_rental_rate;

        // Add operator cost if needed
        if ($validated['operator_needed'] && $equipment->operator_rate) {
            $totalCost += $days * $equipment->operator_rate;
        }

        // Add transport cost if needed
        if ($validated['transport_needed'] && $equipment->transport_rate) {
            $totalCost += $equipment->transport_rate;
        }

        $equipmentRequest = EquipmentRequest::create([
            'equipment_id' => $validated['equipment_id'],
            'user_id' => $user->id,
            'start_date' => $validated['start_date'],
            'end_date' => $validated['end_date'],
            'purpose' => $validated['purpose'],
            'operator_needed' => $validated['operator_needed'] ?? false,
            'transport_needed' => $validated['transport_needed'] ?? false,
            'notes' => $validated['notes'],
            'estimated_cost' => $totalCost,
            'status' => 'pending',
        ]);

        Log::info('Equipment request created', [
            'request_id' => $equipmentRequest->id,
            'equipment_id' => $validated['equipment_id'],
            'user_id' => $user->id,
            'estimated_cost' => $totalCost,
        ]);

        return redirect()->back()->with('success', 'Equipment request submitted successfully! You will be notified once it\'s reviewed.');
    }

    /**
     * Admin view for managing equipment requests
     */
    public function adminIndex()
    {
        $requests = EquipmentRequest::with(['equipment', 'user', 'approvedBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Admin/EquipmentRequests', [
            'requests' => $requests->map(function ($request) {
                return [
                    'id' => $request->id,
                    'equipment' => [
                        'id' => $request->equipment->id ?? null,
                        'name' => $request->equipment->name ?? 'Unknown Equipment',
                        'type' => $request->equipment->type ?? 'Unknown',
                    ],
                    'user' => [
                        'id' => $request->user->id,
                        'name' => $request->user->name,
                        'email' => $request->user->email,
                    ],
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date,
                    'purpose' => $request->purpose,
                    'operator_needed' => $request->operator_needed,
                    'transport_needed' => $request->transport_needed,
                    'notes' => $request->notes,
                    'estimated_cost' => $request->estimated_cost,
                    'status' => $request->status,
                    'payment_status' => $request->payment_status,
                    'admin_notes' => $request->admin_notes,
                    'approved_by' => $request->approvedBy ? [
                        'id' => $request->approvedBy->id,
                        'name' => $request->approvedBy->name,
                    ] : null,
                    'created_at' => $request->created_at,
                    'updated_at' => $request->updated_at,
                ];
            }),
        ]);
    }

    /**
     * Approve an equipment request
     */
    public function approve(Request $request, $id)
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $equipmentRequest = EquipmentRequest::findOrFail($id);

        $equipmentRequest->update([
            'status' => 'approved',
            'approved_by' => auth()->id(),
            'admin_notes' => $request->admin_notes,
        ]);

        return redirect()->back()->with('success', 'Equipment request approved successfully!');
    }

    /**
     * Reject an equipment request
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $equipmentRequest = EquipmentRequest::findOrFail($id);

        $equipmentRequest->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'rejection_reason' => $request->rejection_reason,
        ]);

        return redirect()->back()->with('success', 'Equipment request rejected.');
    }

    /**
     * Mark payment as received
     */
    public function markPaid($id)
    {
        $equipmentRequest = EquipmentRequest::findOrFail($id);

        $equipmentRequest->update([
            'payment_status' => 'paid',
            'payment_date' => now(),
        ]);

        return redirect()->back()->with('success', 'Payment marked as received!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $equipmentRequest = EquipmentRequest::with(['equipment', 'user', 'approvedBy'])
            ->findOrFail($id);

        return Inertia::render('Equipment/RequestDetails', [
            'request' => [
                'id' => $equipmentRequest->id,
                'equipment' => [
                    'id' => $equipmentRequest->equipment->id ?? null,
                    'name' => $equipmentRequest->equipment->name ?? 'Unknown Equipment',
                    'type' => $equipmentRequest->equipment->type ?? 'Unknown',
                ],
                'user' => [
                    'id' => $equipmentRequest->user->id,
                    'name' => $equipmentRequest->user->name,
                    'email' => $equipmentRequest->user->email,
                ],
                'start_date' => $equipmentRequest->start_date,
                'end_date' => $equipmentRequest->end_date,
                'purpose' => $equipmentRequest->purpose,
                'operator_needed' => $equipmentRequest->operator_needed,
                'transport_needed' => $equipmentRequest->transport_needed,
                'notes' => $equipmentRequest->notes,
                'estimated_cost' => $equipmentRequest->estimated_cost,
                'status' => $equipmentRequest->status,
                'payment_status' => $equipmentRequest->payment_status,
                'admin_notes' => $equipmentRequest->admin_notes,
                'approved_by' => $equipmentRequest->approvedBy ? [
                    'id' => $equipmentRequest->approvedBy->id,
                    'name' => $equipmentRequest->approvedBy->name,
                ] : null,
                'created_at' => $equipmentRequest->created_at,
                'updated_at' => $equipmentRequest->updated_at,
            ],
        ]);
    }
}
