<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contributions', function (Blueprint $table) {
            // Add new columns if they don't exist
            if (!Schema::hasColumn('contributions', 'member_id')) {
                $table->foreignId('member_id')->nullable()->after('id')->constrained('users')->onDelete('cascade');
            }

            if (!Schema::hasColumn('contributions', 'type')) {
                $table->enum('type', ['monthly', 'special', 'emergency', 'development'])->default('monthly')->after('amount');
            }

            if (!Schema::hasColumn('contributions', 'description')) {
                $table->text('description')->nullable()->after('type');
            }

            if (!Schema::hasColumn('contributions', 'reference_number')) {
                $table->string('reference_number')->nullable()->after('description');
            }

            if (!Schema::hasColumn('contributions', 'status')) {
                $table->enum('status', ['pending', 'confirmed', 'rejected'])->default('pending')->after('reference_number');
            }
        });

        // Handle the receipt_number to reference_number conversion separately
        if (Schema::hasColumn('contributions', 'receipt_number') && Schema::hasColumn('contributions', 'reference_number')) {
            // Copy data from receipt_number to reference_number if needed
            DB::statement('UPDATE contributions SET reference_number = receipt_number WHERE receipt_number IS NOT NULL');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contributions', function (Blueprint $table) {
            // Drop columns if they exist
            $columnsToDrop = [];

            if (Schema::hasColumn('contributions', 'member_id')) {
                $columnsToDrop[] = 'member_id';
            }
            if (Schema::hasColumn('contributions', 'type')) {
                $columnsToDrop[] = 'type';
            }
            if (Schema::hasColumn('contributions', 'description')) {
                $columnsToDrop[] = 'description';
            }
            if (Schema::hasColumn('contributions', 'reference_number')) {
                $columnsToDrop[] = 'reference_number';
            }
            if (Schema::hasColumn('contributions', 'status')) {
                $columnsToDrop[] = 'status';
            }

            if (!empty($columnsToDrop)) {
                $table->dropColumn($columnsToDrop);
            }
        });
    }
};
