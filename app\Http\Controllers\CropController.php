<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FarmerCrop;
use App\Models\Crop;
use Inertia\Inertia;

class CropController extends Controller
{
    public function create()
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile associated with this user.']);
        }

        // Get existing crops for this farmer
        $existingCrops = FarmerCrop::where('farmer_id', $farmer->id)
            ->with('crop')
            ->get()
            ->map(function ($farmerCrop) {
                return [
                    'id' => $farmerCrop->id,
                    'crop_name' => $farmerCrop->crop_name ?? $farmerCrop->crop->name ?? 'Unknown',
                    'area_planted' => $farmerCrop->area_planted,
                    'planting_date' => $farmerCrop->planting_date,
                    'expected_harvest_date' => $farmerCrop->expected_harvest_date,
                    'estimated_value' => $farmerCrop->estimated_value,
                ];
            });

        return Inertia::render('Crops/Register', [
            'auth' => ['user' => $user],
            'existingCrops' => $existingCrops
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'crop_name' => 'required|string|max:255',
            'crop_type' => 'required|string|max:255',
            'variety' => 'nullable|string|max:255',
            'area_planted' => 'required|numeric|min:0.1',
            'planting_date' => 'required|date',
            'expected_harvest_date' => 'required|date|after:planting_date',
            'planting_method' => 'required|string',
            'irrigation_method' => 'required|string',
            'fertilizer_used' => 'nullable|string|max:255',
            'pesticide_used' => 'nullable|string|max:255',
            'expected_yield' => 'nullable|numeric|min:0',
            'estimated_value' => 'nullable|numeric|min:0',
            'location_description' => 'required|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        $user = $request->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->back()->withErrors(['error' => 'No farmer profile associated with this user.']);
        }

        FarmerCrop::create([
            'farmer_id' => $farmer->id,
            'crop_name' => $request->crop_name,
            'crop_type' => $request->crop_type,
            'variety' => $request->variety,
            'area_planted' => $request->area_planted,
            'planting_date' => $request->planting_date,
            'expected_harvest_date' => $request->expected_harvest_date,
            'planting_method' => $request->planting_method,
            'irrigation_method' => $request->irrigation_method,
            'fertilizer_used' => $request->fertilizer_used,
            'pesticide_used' => $request->pesticide_used,
            'expected_yield' => $request->expected_yield,
            'estimated_value' => $request->estimated_value,
            'location_description' => $request->location_description,
            'notes' => $request->notes,
        ]);

        return redirect()->back()->with('success', 'Crop "' . $request->crop_name . '" registered successfully!');
    }
}