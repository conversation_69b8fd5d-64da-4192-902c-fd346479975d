<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\TransportDelivery;
use App\Models\TransportRate;
use App\Models\Farmer;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class TransportController extends Controller
{
    /**
     * Display transport management dashboard
     */
    public function index(Request $request)
    {
        $dateRange = $request->get('date_range', 'this_month');
        $status = $request->get('status', 'all');
        $vehicle = $request->get('vehicle', 'all');

        // Calculate date range
        switch ($dateRange) {
            case 'today':
                $startDate = Carbon::today();
                $endDate = Carbon::today();
                break;
            case 'this_week':
                $startDate = Carbon::now()->startOfWeek();
                $endDate = Carbon::now()->endOfWeek();
                break;
            case 'this_month':
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;
            case 'last_month':
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                break;
            default:
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
        }

        // Build query
        $query = TransportDelivery::with(['farmer.user', 'vehicle', 'transportRate'])
            ->byDateRange($startDate, $endDate);

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        if ($vehicle !== 'all') {
            $query->where('vehicle_id', $vehicle);
        }

        $deliveries = $query->orderBy('delivery_date', 'desc')->get();

        // Calculate statistics
        $stats = [
            'total_deliveries' => $deliveries->count(),
            'total_weight' => $deliveries->sum('sugarcane_weight_tons'),
            'total_transport_cost' => $deliveries->sum('total_transport_cost'),
            'total_distance' => $deliveries->sum('distance_km'),
            'pending_deliveries' => $deliveries->where('status', 'pending')->count(),
            'completed_deliveries' => $deliveries->where('status', 'completed')->count(),
        ];

        // Get vehicles for filter
        $vehicles = Vehicle::active()->get(['id', 'registration_number', 'vehicle_type']);

        return Inertia::render('Admin/Transport/Index', [
            'deliveries' => $deliveries,
            'stats' => $stats,
            'vehicles' => $vehicles,
            'filters' => [
                'date_range' => $dateRange,
                'status' => $status,
                'vehicle' => $vehicle,
            ],
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Show form to create new delivery
     */
    public function create()
    {
        $farmers = Farmer::with('user')->get()->map(function ($farmer) {
            return [
                'id' => $farmer->id,
                'name' => $farmer->user->name ?? 'Unknown',
                'phone' => $farmer->user->phone ?? 'N/A',
                'location' => $farmer->location ?? 'Not specified',
            ];
        });

        $vehicles = Vehicle::active()->get();
        $transportRates = TransportRate::active()->get();

        return Inertia::render('Admin/Transport/Create', [
            'farmers' => $farmers,
            'vehicles' => $vehicles,
            'transportRates' => $transportRates,
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Store new delivery
     */
    public function store(Request $request)
    {
        $request->validate([
            'farmer_id' => 'required|exists:farmers,id',
            'vehicle_id' => 'required|exists:vehicles,id',
            'transport_rate_id' => 'required|exists:transport_rates,id',
            'delivery_date' => 'required|date',
            'sugarcane_weight_tons' => 'required|numeric|min:0.1',
            'distance_km' => 'required|numeric|min:0.1',
            'pickup_location' => 'required|string|max:255',
            'departure_time' => 'nullable|date_format:H:i',
            'arrival_time' => 'nullable|date_format:H:i',
            'weighbridge_ticket_number' => 'nullable|string|max:100',
            'delivery_notes' => 'nullable|string|max:1000',
        ]);

        // Get transport rate
        $transportRate = TransportRate::findOrFail($request->transport_rate_id);

        // Calculate costs
        $transportCost = $request->sugarcane_weight_tons * $transportRate->total_rate_per_ton;

        // Create delivery
        $delivery = TransportDelivery::create([
            'delivery_number' => TransportDelivery::generateDeliveryNumber(),
            'farmer_id' => $request->farmer_id,
            'vehicle_id' => $request->vehicle_id,
            'transport_rate_id' => $request->transport_rate_id,
            'delivery_date' => $request->delivery_date,
            'departure_time' => $request->departure_time,
            'arrival_time' => $request->arrival_time,
            'sugarcane_weight_tons' => $request->sugarcane_weight_tons,
            'distance_km' => $request->distance_km,
            'transport_rate_per_ton' => $transportRate->rate_per_ton,
            'fuel_surcharge' => $transportRate->fuel_surcharge,
            'total_transport_cost' => $transportCost,
            'pickup_location' => $request->pickup_location,
            'weighbridge_ticket_number' => $request->weighbridge_ticket_number,
            'delivery_notes' => $request->delivery_notes,
            'status' => 'pending',
        ]);

        // Update vehicle statistics
        $vehicle = Vehicle::find($request->vehicle_id);
        $vehicle->increment('total_trips');
        $vehicle->increment('total_distance_km', $request->distance_km);

        return redirect()->route('admin.transport.index')
            ->with('success', "Delivery {$delivery->delivery_number} created successfully!");
    }

    /**
     * Show delivery details
     */
    public function show($id)
    {
        $delivery = TransportDelivery::with(['farmer.user', 'vehicle', 'transportRate'])
            ->findOrFail($id);

        return Inertia::render('Admin/Transport/Show', [
            'delivery' => $delivery,
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Update delivery status
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,completed,cancelled',
            'gross_sales_amount' => 'nullable|numeric|min:0',
            'fuel_used_liters' => 'nullable|numeric|min:0',
            'fuel_cost' => 'nullable|numeric|min:0',
        ]);

        $delivery = TransportDelivery::findOrFail($id);

        $updateData = [
            'status' => $request->status,
        ];

        if ($request->gross_sales_amount) {
            $updateData['gross_sales_amount'] = $request->gross_sales_amount;
            $updateData['net_amount_after_transport'] = $request->gross_sales_amount - $delivery->total_transport_cost;
        }

        if ($request->fuel_used_liters) {
            $updateData['fuel_used_liters'] = $request->fuel_used_liters;
        }

        if ($request->fuel_cost) {
            $updateData['fuel_cost'] = $request->fuel_cost;
        }

        $delivery->update($updateData);

        return redirect()->back()->with('success', 'Delivery status updated successfully!');
    }

    /**
     * Vehicle management
     */
    public function vehicles()
    {
        $vehicles = Vehicle::orderBy('registration_number')->get();

        $stats = [
            'total_vehicles' => $vehicles->count(),
            'active_vehicles' => $vehicles->where('status', 'active')->count(),
            'maintenance_vehicles' => $vehicles->where('status', 'maintenance')->count(),
            'vehicles_needing_maintenance' => $vehicles->filter(function ($vehicle) {
                return $vehicle->needsMaintenance();
            })->count(),
        ];

        return Inertia::render('Admin/Transport/Vehicles', [
            'vehicles' => $vehicles,
            'stats' => $stats,
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Store new vehicle
     */
    public function storeVehicle(Request $request)
    {
        $request->validate([
            'registration_number' => 'required|string|unique:vehicles,registration_number',
            'vehicle_type' => 'required|string',
            'make' => 'required|string',
            'model' => 'required|string',
            'year' => 'required|integer|min:1990|max:' . (date('Y') + 1),
            'capacity_tons' => 'required|numeric|min:0.1',
            'fuel_consumption_per_km' => 'nullable|numeric|min:0',
            'driver_name' => 'nullable|string|max:255',
            'driver_phone' => 'nullable|string|max:20',
            'insurance_expiry' => 'nullable|date|after:today',
            'license_expiry' => 'nullable|date|after:today',
            'notes' => 'nullable|string|max:1000',
        ]);

        Vehicle::create($request->all());

        return redirect()->back()->with('success', 'Vehicle added successfully!');
    }

    /**
     * Show vehicle details
     */
    public function showVehicle($id)
    {
        $vehicle = Vehicle::findOrFail($id);

        // Get vehicle statistics
        $deliveries = TransportDelivery::where('vehicle_id', $id)->get();
        $stats = [
            'total_deliveries' => $deliveries->count(),
            'total_distance' => $deliveries->sum('distance_km'),
            'total_weight_transported' => $deliveries->sum('sugarcane_weight_tons'),
            'total_fuel_used' => $deliveries->sum('fuel_used_liters'),
            'average_fuel_consumption' => $deliveries->avg('fuel_used_liters'),
            'last_delivery' => $deliveries->sortByDesc('delivery_date')->first()?->delivery_date,
        ];

        return response()->json([
            'vehicle' => $vehicle,
            'stats' => $stats,
            'recent_deliveries' => $deliveries->sortByDesc('delivery_date')->take(10)->values()
        ]);
    }

    /**
     * Update vehicle
     */
    public function updateVehicle(Request $request, $id)
    {
        $vehicle = Vehicle::findOrFail($id);

        $validated = $request->validate([
            'registration_number' => 'required|string|max:255|unique:vehicles,registration_number,' . $id,
            'vehicle_type' => 'required|in:truck,trailer,pickup,lorry',
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1990|max:' . (date('Y') + 1),
            'capacity_tons' => 'required|numeric|min:0.1|max:100',
            'fuel_consumption_per_km' => 'nullable|numeric|min:0',
            'driver_name' => 'nullable|string|max:255',
            'driver_phone' => 'nullable|string|max:20',
            'insurance_expiry' => 'nullable|date',
            'license_expiry' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        $vehicle->update($validated);

        return redirect()->back()->with('success', 'Vehicle updated successfully!');
    }

    /**
     * Update vehicle status
     */
    public function updateVehicleStatus(Request $request, $id)
    {
        $vehicle = Vehicle::findOrFail($id);

        $validated = $request->validate([
            'status' => 'required|in:active,maintenance,retired',
        ]);

        $vehicle->update(['status' => $validated['status']]);

        return redirect()->back()->with('success', 'Vehicle status updated successfully!');
    }

    /**
     * Transport rates management
     */
    public function rates()
    {
        $rates = TransportRate::orderBy('region_name')->get();

        return Inertia::render('Admin/Transport/Rates', [
            'rates' => $rates,
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Store new transport rate
     */
    public function storeRate(Request $request)
    {
        $request->validate([
            'region_name' => 'required|string|max:255',
            'distance_km' => 'required|numeric|min:0.1',
            'rate_per_ton' => 'required|numeric|min:0.01',
            'fuel_surcharge' => 'nullable|numeric|min:0',
            'description' => 'nullable|string|max:1000',
        ]);

        TransportRate::create($request->all());

        return redirect()->back()->with('success', 'Transport rate added successfully!');
    }

    /**
     * Show transport rate details
     */
    public function showRate($id)
    {
        $rate = TransportRate::findOrFail($id);

        // Get usage statistics
        $deliveries = TransportDelivery::where('transport_rate_id', $id)->get();
        $stats = [
            'total_deliveries' => $deliveries->count(),
            'total_weight_transported' => $deliveries->sum('sugarcane_weight_tons'),
            'total_revenue_generated' => $deliveries->sum('total_transport_cost'),
            'average_delivery_weight' => $deliveries->avg('sugarcane_weight_tons'),
            'last_used' => $deliveries->sortByDesc('delivery_date')->first()?->delivery_date,
        ];

        return response()->json([
            'rate' => $rate,
            'stats' => $stats,
            'recent_deliveries' => $deliveries->sortByDesc('delivery_date')->take(10)->values()
        ]);
    }

    /**
     * Update transport rate
     */
    public function updateRate(Request $request, $id)
    {
        $rate = TransportRate::findOrFail($id);

        $validated = $request->validate([
            'region_name' => 'required|string|max:255',
            'distance_km' => 'required|numeric|min:0.1|max:1000',
            'rate_per_ton' => 'required|numeric|min:0.01',
            'fuel_surcharge' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        $rate->update($validated);

        return redirect()->back()->with('success', 'Transport rate updated successfully!');
    }

    /**
     * Toggle transport rate active status
     */
    public function toggleRate($id)
    {
        $rate = TransportRate::findOrFail($id);

        $rate->update(['is_active' => !$rate->is_active]);

        $status = $rate->is_active ? 'activated' : 'deactivated';

        return redirect()->back()->with('success', "Transport rate {$status} successfully!");
    }
}
