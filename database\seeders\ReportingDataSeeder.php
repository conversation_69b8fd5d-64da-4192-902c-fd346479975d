<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Farmer;
use App\Models\Contribution;
use App\Models\Loan;
use App\Models\LoanRepayment;
use App\Models\SavingsAccount;
use App\Models\FarmEquipment;
use App\Models\EquipmentRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class ReportingDataSeeder extends Seeder
{
    public function run()
    {
        // Get existing farmers or create some if none exist
        $farmers = Farmer::with('user')->get();

        if ($farmers->count() < 3) {
            // Create a few sample farmers if none exist
            $memberRole = \App\Models\Role::where('name', 'member')->first();

            for ($i = 1; $i <= 5; $i++) {
                $user = User::create([
                    'name' => "Sample Member $i",
                    'email' => "member$<EMAIL>",
                    'password' => Hash::make('password123'),
                    'role_id' => $memberRole->id,
                ]);

                $farmer = Farmer::create([
                    'user_id' => $user->id,
                    'phone' => "+***********$i",
                    'address' => 'Lusaka, Zambia',
                    'land_size' => rand(1, 10),
                    'farm_location' => 'Chongwe District',
                    'crops_grown' => 'Maize, Soybeans',
                    'farming_experience' => rand(2, 20),
                    'status' => 'active',
                    'membership_date' => Carbon::now()->subDays(rand(30, 365)),
                ]);

                // Create savings account
                SavingsAccount::create([
                    'farmer_id' => $farmer->id,
                    'account_number' => 'SAV' . str_pad($farmer->id, 6, '0', STR_PAD_LEFT),
                    'balance' => rand(500, 5000),
                    'interest_rate' => 5.0,
                ]);
            }

            $farmers = Farmer::with('user')->get();
        }

        // Create contributions for the last 6 months
        foreach ($farmers as $farmer) {
            for ($i = 0; $i < 6; $i++) {
                $contributionDate = Carbon::now()->subMonths($i)->startOfMonth()->addDays(rand(1, 28));
                
                Contribution::create([
                    'farmer_id' => $farmer->id,
                    'amount' => [300, 500, 750, 1000][rand(0, 3)], // Various contribution amounts
                    'type' => ['monthly', 'monthly', 'monthly', 'special'][rand(0, 3)], // Mostly monthly
                    'description' => 'Monthly contribution for ' . $contributionDate->format('F Y'),
                    'payment_method' => ['cash', 'mobile_money', 'bank_transfer'][rand(0, 2)],
                    'contribution_date' => $contributionDate,
                    'status' => ['confirmed', 'confirmed', 'pending'][rand(0, 2)], // Mostly confirmed
                    'recorded_by' => 1, // Admin user
                ]);
            }
        }

        // Create loans
        $loanStatuses = ['pending', 'approved', 'disbursed', 'partial_repayment', 'completed'];
        $farmersArray = $farmers->toArray();
        foreach (array_slice($farmersArray, 0, min(6, count($farmersArray))) as $farmerData) { // 6 farmers have loans
            $farmer = Farmer::find($farmerData['id']);
            $loanAmount = [2000, 3000, 5000, 7500, 10000][rand(0, 4)];
            $status = $loanStatuses[rand(0, 4)];
            
            $loan = Loan::create([
                'farmer_id' => $farmer->id,
                'amount' => $loanAmount,
                'interest_rate' => 12.0,
                'term_months' => [6, 12, 18, 24][rand(0, 3)],
                'purpose' => ['Farm expansion', 'Equipment purchase', 'Seed capital', 'Livestock'][rand(0, 3)],
                'status' => $status,
                'application_date' => Carbon::now()->subDays(rand(30, 180)),
                'approved_date' => $status !== 'pending' ? Carbon::now()->subDays(rand(15, 150)) : null,
                'disbursed_date' => in_array($status, ['disbursed', 'partial_repayment', 'completed']) ? Carbon::now()->subDays(rand(10, 120)) : null,
            ]);

            // Create loan repayments for disbursed loans
            if (in_array($status, ['partial_repayment', 'completed'])) {
                $monthlyPayment = $loanAmount / $loan->term_months;
                $paymentsCount = $status === 'completed' ? $loan->term_months : rand(1, $loan->term_months - 1);
                
                for ($i = 0; $i < $paymentsCount; $i++) {
                    LoanRepayment::create([
                        'loan_id' => $loan->id,
                        'amount' => $monthlyPayment + rand(-50, 50), // Slight variation
                        'payment_date' => Carbon::now()->subDays(rand(5, 90)),
                        'payment_method' => ['cash', 'mobile_money', 'bank_transfer'][rand(0, 2)],
                    ]);
                }
            }
        }

        // Create farm equipment
        $equipment = [
            ['name' => 'Tractor - John Deere 5055E', 'type' => 'tractor', 'value' => 45000, 'rental_cost' => 200],
            ['name' => 'Plough - 3-Disc', 'type' => 'plough', 'value' => 1500, 'rental_cost' => 50],
            ['name' => 'Harrow - 20-Disc', 'type' => 'harrow', 'value' => 2500, 'rental_cost' => 75],
            ['name' => 'Seed Drill - 12-Row', 'type' => 'planter', 'value' => 8000, 'rental_cost' => 120],
            ['name' => 'Harvester - Combine', 'type' => 'harvester', 'value' => 85000, 'rental_cost' => 300],
            ['name' => 'Irrigation Pump', 'type' => 'pump', 'value' => 3500, 'rental_cost' => 80],
        ];

        $equipmentIds = [];
        foreach ($equipment as $item) {
            $equipmentRecord = FarmEquipment::create([
                'name' => $item['name'],
                'type' => $item['type'],
                'description' => 'High-quality farm equipment for cooperative members',
                'value' => $item['value'],
                'rental_cost_per_day' => $item['rental_cost'],
                'purchase_date' => Carbon::now()->subDays(rand(100, 500)),
                'is_available' => rand(0, 1),
                'available_for_rent' => true,
            ]);
            $equipmentIds[] = $equipmentRecord->id;
        }

        // Create equipment requests (if equipment exists)
        if (count($equipmentIds) > 0) {
            foreach ($farmers->take(5) as $farmer) {
                $equipmentId = $equipmentIds[rand(0, count($equipmentIds) - 1)];
                $equipment = FarmEquipment::find($equipmentId);
                $days = rand(1, 7);

                EquipmentRequest::create([
                    'farmer_id' => $farmer->id,
                    'equipment_id' => $equipmentId,
                    'request_date' => Carbon::now()->subDays(rand(1, 30)),
                    'start_date' => Carbon::now()->addDays(rand(1, 10)),
                    'end_date' => Carbon::now()->addDays(rand(11, 20)),
                    'purpose' => ['Land preparation', 'Planting', 'Harvesting', 'Irrigation'][rand(0, 3)],
                    'status' => ['pending', 'approved', 'completed', 'rejected'][rand(0, 3)],
                    'rental_cost_per_day' => $equipment->rental_cost_per_day ?? 50,
                    'total_rental_cost' => ($equipment->rental_cost_per_day ?? 50) * $days,
                    'payment_status' => ['pending', 'paid', 'overdue'][rand(0, 2)],
                ]);
            }
        }

        $this->command->info('Sample reporting data created successfully!');
    }
}
