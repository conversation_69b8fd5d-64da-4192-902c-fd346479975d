const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"ignition.healthCheck":{"uri":"_ignition\/health-check","methods":["GET","HEAD"]},"ignition.executeSolution":{"uri":"_ignition\/execute-solution","methods":["POST"]},"ignition.updateConfig":{"uri":"_ignition\/update-config","methods":["POST"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"members.index":{"uri":"members","methods":["GET","HEAD"]},"savings.index":{"uri":"savings","methods":["GET","HEAD"]},"contributions.index":{"uri":"contributions","methods":["GET","HEAD"]},"loans.index":{"uri":"loans","methods":["GET","HEAD"]},"reports.index":{"uri":"reports","methods":["GET","HEAD"]},"settings.index":{"uri":"settings","methods":["GET","HEAD"]},"members.store":{"uri":"members","methods":["POST"]},"reports.generate":{"uri":"generate-report","methods":["POST"]},"announcements.send":{"uri":"send-announcement","methods":["POST"]},"loans.process":{"uri":"process-loans","methods":["POST"]},"login":{"uri":"login","methods":["GET","HEAD"]},"login.attempt":{"uri":"login","methods":["POST"]},"dashboard.admin":{"uri":"dashboard\/admin","methods":["GET","HEAD"]},"dashboard.staff":{"uri":"dashboard\/staff","methods":["GET","HEAD"]},"dashboard.member":{"uri":"dashboard\/member","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
