// resources/js/Layouts/AdminLayout.jsx
import React from 'react';
import AuthenticatedLayout from './AuthenticatedLayout';

export default function AdminLayout({ user, children, header }) {
  return (
    <AuthenticatedLayout 
      user={user} 
      role="staff"
      header={header}
    >
      {children}
    </AuthenticatedLayout>
  );
}

// Similarly create StaffLayout.jsx and MemberLayout.jsx with role="staff"/"member"