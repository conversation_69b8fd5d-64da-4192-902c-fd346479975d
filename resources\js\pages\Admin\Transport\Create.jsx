import React, { useState, useEffect } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function CreateDelivery({ auth, farmers, vehicles, transportRates }) {
    const [selectedRate, setSelectedRate] = useState(null);
    const [estimatedCost, setEstimatedCost] = useState(0);

    const { data, setData, post, processing, errors } = useForm({
        farmer_id: '',
        vehicle_id: '',
        transport_rate_id: '',
        delivery_date: new Date().toISOString().split('T')[0],
        sugarcane_weight_tons: '',
        distance_km: '',
        pickup_location: '',
        departure_time: '',
        arrival_time: '',
        weighbridge_ticket_number: '',
        delivery_notes: '',
    });

    // Calculate estimated cost when weight or rate changes
    useEffect(() => {
        if (data.sugarcane_weight_tons && selectedRate) {
            const weight = parseFloat(data.sugarcane_weight_tons);
            const totalRate = parseFloat(selectedRate.rate_per_ton) + parseFloat(selectedRate.fuel_surcharge);
            setEstimatedCost(weight * totalRate);
        } else {
            setEstimatedCost(0);
        }
    }, [data.sugarcane_weight_tons, selectedRate]);

    // Update selected rate when transport rate changes
    useEffect(() => {
        if (data.transport_rate_id) {
            const rate = transportRates.find(r => r.id == data.transport_rate_id);
            setSelectedRate(rate);
            if (rate) {
                setData('distance_km', rate.distance_km);
            }
        } else {
            setSelectedRate(null);
        }
    }, [data.transport_rate_id]);

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/admin/transport');
    };

    return (
        <AdminLayout user={auth.user}>
            <Head title="New Delivery" />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div className="p-6 border-b border-gray-200">
                            <div className="flex items-center">
                                <Link
                                    href="/admin/transport"
                                    className="text-green-600 hover:text-green-800 mr-4"
                                >
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M15 19l-7-7 7-7" clipRule="evenodd" />
                                    </svg>
                                </Link>
                                <div>
                                    <h2 className="text-2xl font-bold text-gray-900">🚛 New Delivery Record</h2>
                                    <p className="text-gray-600">Record a new sugarcane delivery to Illovo</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Form */}
                    <div className="bg-white rounded-lg shadow overflow-hidden">
                        <form onSubmit={handleSubmit} className="p-6 space-y-6">
                            {/* Basic Information */}
                            <div>
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Farmer *</label>
                                        <select
                                            value={data.farmer_id}
                                            onChange={(e) => setData('farmer_id', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            required
                                        >
                                            <option value="">Select Farmer</option>
                                            {farmers.map((farmer) => (
                                                <option key={farmer.id} value={farmer.id}>
                                                    {farmer.name} - {farmer.location}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.farmer_id && <p className="mt-1 text-sm text-red-600">{errors.farmer_id}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Vehicle *</label>
                                        <select
                                            value={data.vehicle_id}
                                            onChange={(e) => setData('vehicle_id', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            required
                                        >
                                            <option value="">Select Vehicle</option>
                                            {vehicles.map((vehicle) => (
                                                <option key={vehicle.id} value={vehicle.id}>
                                                    {vehicle.registration_number} - {vehicle.vehicle_type} ({vehicle.capacity_tons}t)
                                                </option>
                                            ))}
                                        </select>
                                        {errors.vehicle_id && <p className="mt-1 text-sm text-red-600">{errors.vehicle_id}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Transport Rate *</label>
                                        <select
                                            value={data.transport_rate_id}
                                            onChange={(e) => setData('transport_rate_id', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            required
                                        >
                                            <option value="">Select Transport Rate</option>
                                            {transportRates.map((rate) => (
                                                <option key={rate.id} value={rate.id}>
                                                    {rate.region_name} - MWK {(parseFloat(rate.rate_per_ton) + parseFloat(rate.fuel_surcharge)).toLocaleString()}/ton
                                                </option>
                                            ))}
                                        </select>
                                        {errors.transport_rate_id && <p className="mt-1 text-sm text-red-600">{errors.transport_rate_id}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Delivery Date *</label>
                                        <input
                                            type="date"
                                            value={data.delivery_date}
                                            onChange={(e) => setData('delivery_date', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            required
                                        />
                                        {errors.delivery_date && <p className="mt-1 text-sm text-red-600">{errors.delivery_date}</p>}
                                    </div>
                                </div>
                            </div>

                            {/* Delivery Details */}
                            <div>
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Delivery Details</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Sugarcane Weight (tons) *</label>
                                        <input
                                            type="number"
                                            step="0.01"
                                            value={data.sugarcane_weight_tons}
                                            onChange={(e) => setData('sugarcane_weight_tons', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            placeholder="e.g., 12.50"
                                            required
                                        />
                                        {errors.sugarcane_weight_tons && <p className="mt-1 text-sm text-red-600">{errors.sugarcane_weight_tons}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Distance (km) *</label>
                                        <input
                                            type="number"
                                            step="0.1"
                                            value={data.distance_km}
                                            onChange={(e) => setData('distance_km', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            placeholder="e.g., 15.0"
                                            required
                                        />
                                        {errors.distance_km && <p className="mt-1 text-sm text-red-600">{errors.distance_km}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Pickup Location *</label>
                                        <input
                                            type="text"
                                            value={data.pickup_location}
                                            onChange={(e) => setData('pickup_location', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            placeholder="e.g., Farmer's Field, Lilongwe"
                                            required
                                        />
                                        {errors.pickup_location && <p className="mt-1 text-sm text-red-600">{errors.pickup_location}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Weighbridge Ticket #</label>
                                        <input
                                            type="text"
                                            value={data.weighbridge_ticket_number}
                                            onChange={(e) => setData('weighbridge_ticket_number', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                            placeholder="e.g., WB-2025-001"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Departure Time</label>
                                        <input
                                            type="time"
                                            value={data.departure_time}
                                            onChange={(e) => setData('departure_time', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Arrival Time</label>
                                        <input
                                            type="time"
                                            value={data.arrival_time}
                                            onChange={(e) => setData('arrival_time', e.target.value)}
                                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Cost Estimation */}
                            {selectedRate && data.sugarcane_weight_tons && (
                                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <h4 className="text-lg font-medium text-green-800 mb-3">💰 Cost Estimation</h4>
                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <p className="text-green-600">Weight</p>
                                            <p className="font-medium text-green-800">{data.sugarcane_weight_tons} tons</p>
                                        </div>
                                        <div>
                                            <p className="text-green-600">Base Rate</p>
                                            <p className="font-medium text-green-800">MWK {parseFloat(selectedRate.rate_per_ton).toLocaleString()}/ton</p>
                                        </div>
                                        <div>
                                            <p className="text-green-600">Fuel Surcharge</p>
                                            <p className="font-medium text-green-800">MWK {parseFloat(selectedRate.fuel_surcharge).toLocaleString()}</p>
                                        </div>
                                        <div>
                                            <p className="text-green-600">Total Cost</p>
                                            <p className="font-bold text-green-800 text-lg">MWK {estimatedCost.toLocaleString()}</p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Notes */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Delivery Notes</label>
                                <textarea
                                    value={data.delivery_notes}
                                    onChange={(e) => setData('delivery_notes', e.target.value)}
                                    rows={3}
                                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                                    placeholder="Any additional notes about this delivery..."
                                />
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end space-x-3 pt-6 border-t">
                                <Link
                                    href="/admin/transport"
                                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                                >
                                    Cancel
                                </Link>
                                <button
                                    type="submit"
                                    disabled={processing}
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 transition-colors"
                                >
                                    {processing ? 'Creating...' : 'Create Delivery Record'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
