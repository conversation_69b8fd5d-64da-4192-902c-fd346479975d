import React, { useState } from 'react';
import MemberLayout from '@/Layouts/MemberLayout';
import { Head, usePage } from '@inertiajs/react';
import { Inertia } from '@inertiajs/inertia';

export default function LoanApplication({ auth }) {
    const [formData, setFormData] = useState({
        amount: '',
        purpose: '',
        term_months: '',
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        Inertia.post(route('loans.store'), formData, {
            onSuccess: () => {
                alert('Loan application submitted successfully!');
                setFormData({ amount: '', purpose: '', term_months: '' });
            },
            onError: (errors) => {
                console.error('Error submitting loan application:', errors);
                alert('Error submitting loan application. Please check console for details.');
            },
        });
    };

    return (
        <MemberLayout user={auth.user} header="Loan Application">
            <Head title="Apply for Loan" />
            <div className="container mx-auto p-4">
                <h1 className="text-2xl font-bold mb-4">Apply for a New Loan</h1>
                <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md">
                    <div className="mb-4">
                        <label htmlFor="amount" className="block text-gray-700 text-sm font-bold mb-2">Loan Amount</label>
                        <input
                            type="number"
                            name="amount"
                            id="amount"
                            value={formData.amount}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="purpose" className="block text-gray-700 text-sm font-bold mb-2">Purpose</label>
                        <textarea
                            name="purpose"
                            id="purpose"
                            value={formData.purpose}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            rows="4"
                            required
                        ></textarea>
                    </div>
                    <div className="mb-6">
                        <label htmlFor="term_months" className="block text-gray-700 text-sm font-bold mb-2">Term (Months)</label>
                        <input
                            type="number"
                            name="term_months"
                            id="term_months"
                            value={formData.term_months}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            required
                        />
                    </div>
                    <div className="flex items-center justify-between">
                        <button
                            type="submit"
                            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </MemberLayout>
    );
}