<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Check if admin role exists
$adminRole = DB::table('roles')->where('name', 'admin')->first();
if (!$adminRole) {
    echo "Creating admin role...\n";
    $adminRoleId = DB::table('roles')->insertGetId([
        'name' => 'admin',
        'description' => 'Administrator role',
        'created_at' => now(),
        'updated_at' => now(),
    ]);
} else {
    $adminRoleId = $adminRole->id;
    echo "Admin role exists with ID: $adminRoleId\n";
}

// Check if admin user exists
$adminUser = DB::table('users')->where('email', '<EMAIL>')->first();
if (!$adminUser) {
    echo "Creating admin user...\n";
    DB::table('users')->insert([
        'name' => 'Admin User',
        'email' => '<EMAIL>',
        'password' => bcrypt('admin123'),
        'role_id' => $adminRoleId,
        'is_active' => true,
        'created_at' => now(),
        'updated_at' => now(),
    ]);
    echo "Admin user created!\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
} else {
    echo "Admin user already exists!\n";
    echo "Email: <EMAIL>\n";
}

// Show all users and their roles
echo "\nAll users and their roles:\n";
$users = DB::table('users')
    ->join('roles', 'users.role_id', '=', 'roles.id')
    ->select('users.id', 'users.name', 'users.email', 'roles.name as role_name')
    ->get();

foreach ($users as $user) {
    echo "ID: {$user->id}, Name: {$user->name}, Email: {$user->email}, Role: {$user->role_name}\n";
}
