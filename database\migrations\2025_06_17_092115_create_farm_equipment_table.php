<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('farm_equipment', function (Blueprint $table) {
            $table->id();
            $table->foreignId('owner_id')->constrained('farmers');
            $table->string('name');
            $table->string('type');
            $table->string('model')->nullable();
            $table->year('purchase_year')->nullable();
            $table->decimal('value', 10, 2)->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_available')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('farm_equipment');
    }
};
