import React from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function ShowMember({ auth, member }) {
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-ZM', {
            style: 'currency',
            currency: 'ZMW',
            minimumFractionDigits: 0
        }).format(amount || 0);
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-ZM', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            active: { bg: 'bg-green-100', text: 'text-green-800', label: 'Active' },
            inactive: { bg: 'bg-red-100', text: 'text-red-800', label: 'Inactive' },
            suspended: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Suspended' },
            pending: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Pending' },
        };

        const config = statusConfig[status] || statusConfig.pending;
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
                {config.label}
            </span>
        );
    };

    return (
        <AdminLayout auth={auth}>
            <Head title={`Member: ${member.name}`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <Link
                                    href="/admin/members"
                                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
                                >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                    </svg>
                                    Back to Members
                                </Link>
                                <h1 className="text-3xl font-bold text-gray-900">{member.name}</h1>
                                <p className="mt-2 text-gray-600">Member details and information</p>
                            </div>
                            <div className="flex items-center space-x-3">
                                {getStatusBadge(member.status)}
                                <Link
                                    href={`/admin/members/${member.id}/edit`}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                                >
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    Edit Member
                                </Link>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Main Information */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Personal Information */}
                            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-medium text-gray-900">Personal Information</h3>
                                </div>
                                <div className="px-6 py-4">
                                    <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Full Name</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{member.name}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Email</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{member.email}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Phone</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{member.phone || 'N/A'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Address</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{member.address || 'N/A'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Membership Date</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{formatDate(member.membership_date)}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Status</dt>
                                            <dd className="mt-1">{getStatusBadge(member.status)}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>

                            {/* Farm Information */}
                            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-medium text-gray-900">Farm Information</h3>
                                </div>
                                <div className="px-6 py-4">
                                    <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Farm Size</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{member.land_size ? `${member.land_size} acres` : 'N/A'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Farm Location</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{member.farm_location || 'N/A'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Crops Grown</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{member.crops_grown || 'N/A'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Farming Experience</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{member.farming_experience ? `${member.farming_experience} years` : 'N/A'}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        {/* Financial Summary */}
                        <div className="space-y-6">
                            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-medium text-gray-900">Financial Summary</h3>
                                </div>
                                <div className="px-6 py-4 space-y-4">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-gray-500">Total Savings</span>
                                        <span className="text-sm font-bold text-green-600">
                                            {formatCurrency(member.financial_summary?.total_savings)}
                                        </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-gray-500">Total Contributions</span>
                                        <span className="text-sm font-bold text-blue-600">
                                            {formatCurrency(member.financial_summary?.total_contributions)}
                                        </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-gray-500">Active Loans</span>
                                        <span className="text-sm font-bold text-orange-600">
                                            {member.financial_summary?.active_loans || 0}
                                        </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-gray-500">Total Loan Amount</span>
                                        <span className="text-sm font-bold text-red-600">
                                            {formatCurrency(member.financial_summary?.total_loans)}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Quick Actions */}
                            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
                                </div>
                                <div className="px-6 py-4 space-y-3">
                                    <Link
                                        href={`/admin/members/${member.id}/edit`}
                                        className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                                    >
                                        Edit Member
                                    </Link>
                                    <Link
                                        href="/admin/contributions/create"
                                        className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        Record Contribution
                                    </Link>
                                    <Link
                                        href="/admin/loans/create"
                                        className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        Create Loan
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
