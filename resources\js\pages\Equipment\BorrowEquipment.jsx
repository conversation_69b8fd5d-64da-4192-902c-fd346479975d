import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';
import { DashboardBreadcrumb, QuickDashboardReturn } from '@/components/ReturnToDashboard';

export default function BorrowEquipment({ auth, availableEquipment, memberRequests, inventoryEquipment }) {
    const [selectedEquipment, setSelectedEquipment] = useState(null);
    const [showRequestForm, setShowRequestForm] = useState(false);
    const [activeTab, setActiveTab] = useState('available');

    const { data, setData, post, processing, errors, reset } = useForm({
        equipment_id: '',
        start_date: '',
        end_date: '',
        purpose: '',
        location: '',
        operator_needed: false,
        transport_needed: false,
        additional_requirements: '',
        emergency_contact: '',
    });

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-ZM', {
            style: 'currency',
            currency: 'ZMW',
            minimumFractionDigits: 0
        }).format(amount || 0);
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-ZM', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const calculateRentalCost = () => {
        if (!selectedEquipment || !data.start_date || !data.end_date) return 0;
        
        const startDate = new Date(data.start_date);
        const endDate = new Date(data.end_date);
        const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
        
        return days * (selectedEquipment.rental_cost_per_day || selectedEquipment.daily_rate || 100);
    };

    const handleEquipmentSelect = (equipment) => {
        setSelectedEquipment(equipment);
        setData('equipment_id', equipment.id);
        setShowRequestForm(true);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/equipment/request', {
            onSuccess: () => {
                reset();
                setSelectedEquipment(null);
                setShowRequestForm(false);
            }
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'approved': return 'bg-green-100 text-green-800';
            case 'rejected': return 'bg-red-100 text-red-800';
            case 'completed': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getPaymentStatusColor = (status) => {
        switch (status) {
            case 'pending': return 'bg-orange-100 text-orange-800';
            case 'paid': return 'bg-green-100 text-green-800';
            case 'overdue': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <MemberLayout auth={auth}>
            <Head title="Borrow Equipment" />
            
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-7xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        {/* Breadcrumb */}
                        <DashboardBreadcrumb
                            user={auth.user}
                            currentPage="Equipment Borrowing"
                            className="mb-4"
                        />

                        <div className="flex justify-between items-center">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Equipment Borrowing</h1>
                                <p className="mt-2 text-gray-600">Request farm equipment and track your borrowing history</p>
                            </div>

                            <div className="flex items-center space-x-3">
                                <QuickDashboardReturn user={auth.user} />
                                <Link
                                href="/inventory"
                                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            >
                                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                </svg>
                                View All Inventory
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* Tabs */}
                    <div className="mb-8">
                        <div className="border-b border-gray-200">
                            <nav className="-mb-px flex space-x-8">
                                <button
                                    onClick={() => setActiveTab('available')}
                                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                        activeTab === 'available'
                                            ? 'border-green-500 text-green-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    Available Equipment ({availableEquipment?.length || 0})
                                </button>
                                <button
                                    onClick={() => setActiveTab('requests')}
                                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                        activeTab === 'requests'
                                            ? 'border-green-500 text-green-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    My Requests ({memberRequests?.length || 0})
                                </button>
                                <button
                                    onClick={() => setActiveTab('inventory')}
                                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                        activeTab === 'inventory'
                                            ? 'border-green-500 text-green-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    General Inventory ({inventoryEquipment?.length || 0})
                                </button>
                            </nav>
                        </div>
                    </div>

                    {/* Available Equipment Tab */}
                    {activeTab === 'available' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {availableEquipment && availableEquipment.length > 0 ? (
                                availableEquipment.map((equipment) => (
                                    <div key={equipment.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                                        <div className="p-6">
                                            <div className="flex items-start justify-between mb-4">
                                                <div>
                                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                                        {equipment.name}
                                                    </h3>
                                                    <p className="text-sm text-gray-600 mb-2">{equipment.type}</p>
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Available
                                                    </span>
                                                </div>
                                            </div>

                                            <div className="space-y-2 mb-4">
                                                <div className="flex justify-between text-sm">
                                                    <span className="text-gray-600">Daily Rate:</span>
                                                    <span className="font-medium">{formatCurrency(equipment.daily_rate || equipment.rental_cost_per_day)}</span>
                                                </div>
                                                {equipment.operator_rate && (
                                                    <div className="flex justify-between text-sm">
                                                        <span className="text-gray-600">Operator Rate:</span>
                                                        <span className="font-medium">{formatCurrency(equipment.operator_rate)}</span>
                                                    </div>
                                                )}
                                                {equipment.transport_rate && (
                                                    <div className="flex justify-between text-sm">
                                                        <span className="text-gray-600">Transport Rate:</span>
                                                        <span className="font-medium">{formatCurrency(equipment.transport_rate)}</span>
                                                    </div>
                                                )}
                                            </div>

                                            {equipment.description && (
                                                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                                                    {equipment.description}
                                                </p>
                                            )}

                                            <button
                                                onClick={() => handleEquipmentSelect(equipment)}
                                                className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
                                            >
                                                Request Equipment
                                            </button>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="col-span-full text-center py-12">
                                    <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                    </svg>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Equipment Available</h3>
                                    <p className="text-gray-600">There is currently no equipment available for borrowing.</p>
                                </div>
                            )}
                        </div>
                    )}

                    {/* My Requests Tab */}
                    {activeTab === 'requests' && (
                        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                            {memberRequests && memberRequests.length > 0 ? (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Equipment
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Dates
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Cost
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Status
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Payment
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {memberRequests.map((request) => (
                                                <tr key={request.id} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {request.equipment?.name || 'Unknown Equipment'}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {request.purpose}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {formatDate(request.start_date)} - {formatDate(request.end_date)}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            Requested: {formatDate(request.request_date)}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {formatCurrency(request.total_rental_cost)}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {formatCurrency(request.rental_cost_per_day)}/day
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                                                            {request.status}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(request.payment_status)}`}>
                                                            {request.payment_status}
                                                        </span>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                                        <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                                    </svg>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Equipment Requests</h3>
                                    <p className="text-gray-600">You haven't made any equipment requests yet.</p>
                                </div>
                            )}
                        </div>
                    )}

                    {/* General Inventory Tab */}
                    {activeTab === 'inventory' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {inventoryEquipment && inventoryEquipment.length > 0 ? (
                                inventoryEquipment.map((item) => (
                                    <div key={item.id} className="bg-white rounded-xl shadow-lg overflow-hidden">
                                        <div className="p-6">
                                            <div className="flex items-start justify-between mb-4">
                                                <div>
                                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                                        {item.name}
                                                    </h3>
                                                    <p className="text-sm text-gray-600 mb-2">{item.category}</p>
                                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                        item.status === 'available' ? 'bg-green-100 text-green-800' :
                                                        item.status === 'low_stock' ? 'bg-yellow-100 text-yellow-800' :
                                                        item.status === 'out_of_stock' ? 'bg-red-100 text-red-800' :
                                                        'bg-gray-100 text-gray-800'
                                                    }`}>
                                                        {item.status.replace('_', ' ')}
                                                    </span>
                                                </div>
                                            </div>

                                            <div className="space-y-2 mb-4">
                                                <div className="flex justify-between text-sm">
                                                    <span className="text-gray-600">Quantity:</span>
                                                    <span className="font-medium">{item.quantity} {item.unit}</span>
                                                </div>
                                                <div className="flex justify-between text-sm">
                                                    <span className="text-gray-600">Location:</span>
                                                    <span className="font-medium">{item.location || 'Not specified'}</span>
                                                </div>
                                                {item.unit_cost && (
                                                    <div className="flex justify-between text-sm">
                                                        <span className="text-gray-600">Unit Cost:</span>
                                                        <span className="font-medium">{formatCurrency(item.unit_cost)}</span>
                                                    </div>
                                                )}
                                            </div>

                                            {item.description && (
                                                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                                                    {item.description}
                                                </p>
                                            )}

                                            <div className="text-center text-sm text-gray-500">
                                                View only - Contact admin for borrowing
                                            </div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="col-span-full text-center py-12">
                                    <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                    </svg>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Inventory Items</h3>
                                    <p className="text-gray-600">No equipment items found in the general inventory.</p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>

            {/* Equipment Request Modal */}
            {showRequestForm && selectedEquipment && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Request Equipment: {selectedEquipment.name}
                            </h3>

                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Start Date <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="date"
                                            value={data.start_date}
                                            onChange={(e) => setData('start_date', e.target.value)}
                                            min={new Date().toISOString().split('T')[0]}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        />
                                        {errors.start_date && <p className="mt-1 text-sm text-red-600">{errors.start_date}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            End Date <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="date"
                                            value={data.end_date}
                                            onChange={(e) => setData('end_date', e.target.value)}
                                            min={data.start_date || new Date().toISOString().split('T')[0]}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        />
                                        {errors.end_date && <p className="mt-1 text-sm text-red-600">{errors.end_date}</p>}
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Purpose <span className="text-red-500">*</span>
                                    </label>
                                    <textarea
                                        value={data.purpose}
                                        onChange={(e) => setData('purpose', e.target.value)}
                                        rows={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        placeholder="Describe what you'll use the equipment for..."
                                        required
                                    />
                                    {errors.purpose && <p className="mt-1 text-sm text-red-600">{errors.purpose}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Location <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        value={data.location}
                                        onChange={(e) => setData('location', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        placeholder="Where will you use the equipment?"
                                        required
                                    />
                                    {errors.location && <p className="mt-1 text-sm text-red-600">{errors.location}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Emergency Contact <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="tel"
                                        value={data.emergency_contact}
                                        onChange={(e) => setData('emergency_contact', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        placeholder="Emergency contact number"
                                        required
                                    />
                                    {errors.emergency_contact && <p className="mt-1 text-sm text-red-600">{errors.emergency_contact}</p>}
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={data.operator_needed}
                                            onChange={(e) => setData('operator_needed', e.target.checked)}
                                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                        />
                                        <label className="ml-2 block text-sm text-gray-900">
                                            Operator needed
                                        </label>
                                    </div>

                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={data.transport_needed}
                                            onChange={(e) => setData('transport_needed', e.target.checked)}
                                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                        />
                                        <label className="ml-2 block text-sm text-gray-900">
                                            Transport needed
                                        </label>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Additional Requirements
                                    </label>
                                    <textarea
                                        value={data.additional_requirements}
                                        onChange={(e) => setData('additional_requirements', e.target.value)}
                                        rows={2}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                        placeholder="Any special requirements or notes..."
                                    />
                                    {errors.additional_requirements && <p className="mt-1 text-sm text-red-600">{errors.additional_requirements}</p>}
                                </div>

                                {/* Cost Calculation */}
                                {data.start_date && data.end_date && (
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <h4 className="text-sm font-medium text-gray-900 mb-2">Estimated Cost</h4>
                                        <div className="text-lg font-bold text-green-600">
                                            {formatCurrency(calculateRentalCost())}
                                        </div>
                                        <div className="text-sm text-gray-600">
                                            {Math.ceil((new Date(data.end_date) - new Date(data.start_date)) / (1000 * 60 * 60 * 24)) + 1} days × {formatCurrency(selectedEquipment.rental_cost_per_day || selectedEquipment.daily_rate || 100)}/day
                                        </div>
                                    </div>
                                )}

                                <div className="flex space-x-3 pt-4">
                                    <button
                                        type="submit"
                                        disabled={processing}
                                        className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                                    >
                                        {processing ? 'Submitting...' : 'Submit Request'}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowRequestForm(false);
                                            setSelectedEquipment(null);
                                            reset();
                                        }}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}
        </MemberLayout>
    );
}