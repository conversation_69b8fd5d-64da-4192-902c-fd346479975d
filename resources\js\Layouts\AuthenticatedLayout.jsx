import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';

export default function AuthenticatedLayout({ user, role, header, children }) {
  // Define paths directly
  const paths = {
    admin: { dashboard: '/dashboard/admin' },
    staff: { dashboard: '/dashboard/staff' },
    member: { dashboard: '/dashboard/member' },
    logout: '/logout'
  };
  const adminPaths = {
    members: '/members',
    savings: '/savings',
    contributions: '/contributions',
    loans: '/loans',
    reports: '/reports',
    settings: '/settings'
  }
  const staffPaths = {
    members: '/members',
    savings: '/savings',
    contributions: '/contributions',
    loans: '/loans',
    reports: '/reports',
    settings: '/settings'
  }
  const memberPaths = {
    savings: '/savings',
    contributions: '/contributions',
    loans: '/loans',
    reports: '/reports',
    settings: '/settings'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>{header || 'Siyamphanje Cooperative'}</title>
      </Head>
      
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href={paths[role].dashboard} className="font-bold">
                Siyamphanje Cooperative
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm">{user?.name || 'User'} ({role})</span>
              <Link 
                href={paths.logout} 
                method="post"
                className="bg-red-600 text-white px-3 py-2 rounded-md hover:bg-red-700 transition-all flex items-center gap-2 text-sm font-medium">
            <i className="fas fa-sign-out-alt"></i>
              
                Logout
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <main className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {header && (
            <h2 className="text-2xl font-semibold mb-6">
              {header}
            </h2>
          )}
          {children}
        </div>
      </main>
    </div>
  );
} 