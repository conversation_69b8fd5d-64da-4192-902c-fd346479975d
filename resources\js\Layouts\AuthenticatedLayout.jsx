import React from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import ThemeToggle from '@/components/ThemeToggle';

export default function AuthenticatedLayout({ user, role, header, children }) {
  // Define paths directly
  const paths = {
    admin: { dashboard: '/dashboard/admin' },
    staff: { dashboard: '/dashboard/staff' },
    member: { dashboard: '/dashboard/member' },
    logout: '/logout'
  };
  const adminPaths = {
    members: '/members',
    savings: '/savings',
    contributions: '/contributions',
    loans: '/loans',
    reports: '/reports',
    settings: '/settings'
  }
  const staffPaths = {
    members: '/members',
    savings: '/savings',
    contributions: '/contributions',
    loans: '/loans',
    reports: '/reports',
    settings: '/settings'
  }
  const memberPaths = {
    savings: '/savings',
    contributions: '/contributions',
    loans: '/loans',
    reports: '/reports',
    settings: '/settings'
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      <Head>
        <title>{header || 'Siyamphanje Cooperative'}</title>
      </Head>

      <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href={paths[role].dashboard} className="font-bold text-gray-900 dark:text-white transition-colors duration-200">
                Siyamphanje Cooperative
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeToggle size="sm" />
              <span className="text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">{user?.name || 'User'} ({role})</span>
              <Link 
                href={paths.logout} 
                method="post"
                className="bg-red-600 text-white px-3 py-2 rounded-md hover:bg-red-700 transition-all flex items-center gap-2 text-sm font-medium">
            <i className="fas fa-sign-out-alt"></i>
              
                Logout
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <main className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {header && (
            <h2 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-white transition-colors duration-200">
              {header}
            </h2>
          )}
          {children}
        </div>
      </main>
    </div>
  );
} 