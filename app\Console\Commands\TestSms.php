<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SmsService;

class TestSms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sms:test {phone} {message?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test SMS to verify Twilio integration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $phone = $this->argument('phone');
        $message = $this->argument('message') ?? 'Test SMS from Siyamphanje Cooperative. MailerSend integration is working!';

        $this->info("Sending test SMS to: {$phone}");
        $this->info("Message: {$message}");
        $this->info("Provider: " . config('sms.default_provider'));

        $smsService = new SmsService();
        $result = $smsService->sendSms($phone, $message, 'test');

        if ($result['success']) {
            $this->info("✅ SMS sent successfully!");
            $this->info("SMS ID: {$result['sms_id']}");
        } else {
            $this->error("❌ SMS failed to send!");
            $this->error("Error: {$result['error']}");
        }

        return $result['success'] ? 0 : 1;
    }
}
