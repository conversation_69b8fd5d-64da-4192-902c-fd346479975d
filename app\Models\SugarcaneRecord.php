<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SugarcaneRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'farmer_id',
        'farmer_crop_id',
        'expected_yield',
        'actual_yield',
        'sugar_content',
        'record_date',
        'notes'
    ];

    public function farmer()
    {
        return $this->belongsTo(Farmer::class);
    }

    public function farmerCrop()
    {
        return $this->belongsTo(FarmerCrop::class);
    }
}
