<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the table exists and has the old structure
        if (Schema::hasTable('equipment_requests')) {
            Schema::table('equipment_requests', function (Blueprint $table) {
                // Check if old column exists and rename it
                if (Schema::hasColumn('equipment_requests', 'requester_id') && !Schema::hasColumn('equipment_requests', 'user_id')) {
                    $table->renameColumn('requester_id', 'user_id');
                }

                // Add missing columns if they don't exist
                if (!Schema::hasColumn('equipment_requests', 'operator_needed')) {
                    $table->boolean('operator_needed')->default(false);
                }
                if (!Schema::hasColumn('equipment_requests', 'transport_needed')) {
                    $table->boolean('transport_needed')->default(false);
                }
                if (!Schema::hasColumn('equipment_requests', 'notes')) {
                    $table->text('notes')->nullable();
                }
                if (!Schema::hasColumn('equipment_requests', 'estimated_cost')) {
                    $table->decimal('estimated_cost', 10, 2)->default(0);
                }

                // Remove old columns if they exist
                if (Schema::hasColumn('equipment_requests', 'request_date')) {
                    $table->dropColumn('request_date');
                }
                if (Schema::hasColumn('equipment_requests', 'location')) {
                    $table->dropColumn('location');
                }
                if (Schema::hasColumn('equipment_requests', 'additional_requirements')) {
                    $table->dropColumn('additional_requirements');
                }
                if (Schema::hasColumn('equipment_requests', 'emergency_contact')) {
                    $table->dropColumn('emergency_contact');
                }
                if (Schema::hasColumn('equipment_requests', 'rental_cost_per_day')) {
                    $table->dropColumn('rental_cost_per_day');
                }
                if (Schema::hasColumn('equipment_requests', 'total_rental_cost')) {
                    $table->dropColumn('total_rental_cost');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the changes if needed
        if (Schema::hasTable('equipment_requests')) {
            Schema::table('equipment_requests', function (Blueprint $table) {
                if (Schema::hasColumn('equipment_requests', 'user_id')) {
                    $table->renameColumn('user_id', 'requester_id');
                }
            });
        }
    }
};
