<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class MeetingNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $memberName;
    public $meetingTitle;
    public $meetingDate;
    public $meetingTime;
    public $location;
    public $agenda;
    public $meetingUrl;

    /**
     * Create a new message instance.
     */
    public function __construct($memberName, $meetingTitle, $meetingDate, $meetingTime, $location, $agenda = null, $meetingUrl = null)
    {
        $this->memberName = $memberName;
        $this->meetingTitle = $meetingTitle;
        $this->meetingDate = $meetingDate;
        $this->meetingTime = $meetingTime;
        $this->location = $location;
        $this->agenda = $agenda;
        $this->meetingUrl = $meetingUrl ?? url('/meetings');
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Meeting Notification: ' . $this->meetingTitle,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.meeting-notification',
            text: 'emails.meeting-notification-text',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
