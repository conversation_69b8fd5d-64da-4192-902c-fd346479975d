<!DOCTYPE html>
<html>
<head>
    <title>{{ $report['title'] ?? 'Report' }}</title>
    <style>
        body { font-family: sans-serif; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #333; }
        .summary { margin-top: 20px; }
        .summary p { margin: 5px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ $report['title'] ?? 'Cooperative Report' }}</h1>
        <p>Generated on: {{ $report['dateGenerated'] ?? 'N/A' }}</p>
        <p>Period: {{ $report['period'] ?? 'All time' }}</p>
    </div>

    @if (($report['reportType'] ?? '') === 'financial' && isset($report['data']))
        <h2>Financial Summary</h2>
        <table>
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Value (UGX)</th>
                </tr>
            </thead>
            <tbody>
                <tr><td>Total Savings</td><td>{{ number_format($report['data']['totalSavings'] ?? 0) }}</td></tr>
                <tr><td>Total Loans</td><td>{{ number_format($report['data']['totalLoans'] ?? 0) }}</td></tr>
                <tr><td>Total Contributions</td><td>{{ number_format($report['data']['totalContributions'] ?? 0) }}</td></tr>
                <tr><td>Total Expenses</td><td>{{ number_format($report['data']['totalExpenses'] ?? 0) }}</td></tr>
                <tr><td>Net Balance</td><td>{{ number_format($report['data']['netBalance'] ?? 0) }}</td></tr>
            </tbody>
        </table>
    @elseif (($report['reportType'] ?? '') === 'members' && isset($report['totalMembers']))
        <h2>Members Report</h2>
        <table>
            <thead>
                <tr>
                    <th>Category</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                <tr><td>Total Members</td><td>{{ $report['totalMembers'] }}</td></tr>
                <tr><td>Active Members</td><td>{{ $report['activeMembers'] }}</td></tr>
                <tr><td>Inactive Members</td><td>{{ $report['inactiveMembers'] }}</td></tr>
                <tr><td>New Members This Period</td><td>{{ $report['newMembersThisPeriod'] }}</td></tr>
            </tbody>
        </table>
    @else
        <p>No specific display template for this report type or data is missing.</p>
        <pre>{{ json_encode($report, JSON_PRETTY_PRINT) }}</pre>
    @endif

</body>
</html>
