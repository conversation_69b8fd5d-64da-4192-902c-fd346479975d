<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use App\Models\TransportDelivery;
use App\Models\TransportRate;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Barryvdh\DomPDF\Facade\Pdf;

class TransportController extends Controller
{
    /**
     * Display member's transport deliveries
     */
    public function index()
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->route('member.dashboard')->with('error', 'Farmer profile not found.');
        }

        $deliveries = TransportDelivery::where('farmer_id', $farmer->id)
            ->with(['vehicle', 'transportRate'])
            ->orderBy('delivery_date', 'desc')
            ->paginate(10);

        $stats = [
            'totalDeliveries' => TransportDelivery::where('farmer_id', $farmer->id)->count(),
            'totalTonsDelivered' => TransportDelivery::where('farmer_id', $farmer->id)->sum('sugarcane_weight_tons') ?? 0,
            'totalTransportCosts' => TransportDelivery::where('farmer_id', $farmer->id)->sum('total_transport_cost') ?? 0,
            'totalDistanceTraveled' => TransportDelivery::where('farmer_id', $farmer->id)->sum('distance_km') ?? 0,
            'thisYearDeliveries' => TransportDelivery::where('farmer_id', $farmer->id)
                ->whereYear('delivery_date', now()->year)->count(),
            'thisYearTransportCosts' => TransportDelivery::where('farmer_id', $farmer->id)
                ->whereYear('delivery_date', now()->year)->sum('total_transport_cost') ?? 0,
        ];

        return Inertia::render('Member/Transport/Index', [
            'deliveries' => $deliveries,
            'stats' => $stats,
            'auth' => [
                'user' => $user->load('farmer')
            ]
        ]);
    }

    /**
     * Display transport fees deducted
     */
    public function fees()
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->route('member.dashboard')->with('error', 'Farmer profile not found.');
        }

        $deliveries = TransportDelivery::where('farmer_id', $farmer->id)
            ->where('payment_deducted', true)
            ->with(['vehicle', 'transportRate'])
            ->orderBy('delivery_date', 'desc')
            ->paginate(10);

        $totalFeesDeducted = TransportDelivery::where('farmer_id', $farmer->id)
            ->where('payment_deducted', true)
            ->sum('total_transport_cost') ?? 0;

        $thisYearFees = TransportDelivery::where('farmer_id', $farmer->id)
            ->where('payment_deducted', true)
            ->whereYear('delivery_date', now()->year)
            ->sum('total_transport_cost') ?? 0;

        return Inertia::render('Member/Transport/Fees', [
            'deliveries' => $deliveries,
            'totalFeesDeducted' => $totalFeesDeducted,
            'thisYearFees' => $thisYearFees,
            'auth' => [
                'user' => $user->load('farmer')
            ]
        ]);
    }

    /**
     * Download transport report
     */
    public function downloadReport(Request $request)
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->route('member.dashboard')->with('error', 'Farmer profile not found.');
        }

        $year = $request->get('year', now()->year);
        
        $deliveries = TransportDelivery::where('farmer_id', $farmer->id)
            ->with(['vehicle', 'transportRate'])
            ->when($year, function ($query, $year) {
                return $query->whereYear('delivery_date', $year);
            })
            ->orderBy('delivery_date', 'desc')
            ->get();

        $stats = [
            'totalDeliveries' => $deliveries->count(),
            'totalTonsDelivered' => $deliveries->sum('sugarcane_weight_tons'),
            'totalTransportCosts' => $deliveries->sum('total_transport_cost'),
            'totalDistanceTraveled' => $deliveries->sum('distance_km'),
            'averageCostPerTon' => $deliveries->sum('sugarcane_weight_tons') > 0 
                ? $deliveries->sum('total_transport_cost') / $deliveries->sum('sugarcane_weight_tons') 
                : 0,
        ];

        $pdf = Pdf::loadView('reports.member-transport', [
            'deliveries' => $deliveries,
            'stats' => $stats,
            'farmer' => $farmer,
            'user' => $user,
            'year' => $year,
            'generatedAt' => now()->format('Y-m-d H:i:s')
        ]);

        return $pdf->download("transport-report-{$farmer->id}-{$year}.pdf");
    }

    /**
     * Request transport (future feature)
     */
    public function requestTransport()
    {
        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->route('member.dashboard')->with('error', 'Farmer profile not found.');
        }

        // Get available transport rates for reference
        $transportRates = TransportRate::where('is_active', true)
            ->orderBy('region_name')
            ->get();

        return Inertia::render('Member/Transport/Request', [
            'transportRates' => $transportRates,
            'auth' => [
                'user' => $user->load('farmer')
            ]
        ]);
    }

    /**
     * Store transport request (future feature)
     */
    public function storeRequest(Request $request)
    {
        $request->validate([
            'pickup_location' => 'required|string|max:255',
            'estimated_tons' => 'required|numeric|min:0.1',
            'preferred_date' => 'required|date|after:today',
            'notes' => 'nullable|string|max:1000',
        ]);

        $user = auth()->user();
        $farmer = $user->farmer;

        if (!$farmer) {
            return redirect()->route('member.dashboard')->with('error', 'Farmer profile not found.');
        }

        // For now, we'll just store this as a simple request
        // In the future, this could be expanded to a full transport request system
        
        return redirect()->route('member.transport.index')
            ->with('success', 'Transport request submitted successfully. An admin will contact you soon.');
    }
}
