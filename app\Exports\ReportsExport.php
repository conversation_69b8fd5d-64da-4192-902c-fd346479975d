<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Collection;

class ReportsExport implements FromCollection, WithHeadings
{
    protected $reportData;

    public function __construct(array $reportData)
    {
        $this->reportData = $reportData;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        // This is a simplified example. You'll need to format your $reportData
        // into a collection of arrays, where each inner array is a row in your spreadsheet.
        // The structure depends on the specific report type.

        $data = [];
        $reportType = $this->reportData['reportType'] ?? 'unknown';

        switch ($reportType) {
            case 'financial':
                $data[] = ['Metric', 'Value'];
                foreach ($this->reportData['data'] as $key => $value) {
                    $data[] = [ucwords(str_replace('_', ' ', $key)), $value];
                }
                break;
            case 'members':
                // Assuming you have a list of members in $this->reportData['membersList']
                // You would fetch detailed member data here if not already in $reportData
                $data[] = ['Total Members', $this->reportData['totalMembers'] ?? 0];
                $data[] = ['Active Members', $this->reportData['activeMembers'] ?? 0];
                $data[] = ['Inactive Members', $this->reportData['inactiveMembers'] ?? 0];
                $data[] = ['New Members This Period', $this->reportData['newMembersThisPeriod'] ?? 0];
                break;
            // Add more cases for other report types (loans, savings, contributions, meetings)
            default:
                $data[] = ['Report Type', $reportType];
                $data[] = ['Date Generated', $this->reportData['dateGenerated'] ?? 'N/A'];
                $data[] = ['Period', $this->reportData['period'] ?? 'All time'];
                // Add generic data display if needed
                if (isset($this->reportData['data'])) {
                    foreach ($this->reportData['data'] as $key => $value) {
                        $data[] = [ucwords(str_replace('_', ' ', $key)), $value];
                    }
                }
                break;
        }

        return collect($data);
    }

    public function headings(): array
    {
        // This is a simplified example. Headings should be dynamic based on report type.
        $reportType = $this->reportData['reportType'] ?? 'unknown';

        switch ($reportType) {
            case 'financial':
                return ['Metric', 'Value'];
            case 'members':
                return ['Category', 'Count'];
            // Add more cases for other report types
            default:
                return ['Key', 'Value'];
        }
    }
}
