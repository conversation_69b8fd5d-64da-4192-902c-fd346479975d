import React from 'react';
import { Head } from '@inertiajs/react'
const Loans = () => {
  const loans = [
    { id: 1, date: '2023-05-20', amount: 1000000, status: 'Active', dueDate: '2023-11-20', paid: 400000 },
    { id: 2, date: '2022-12-15', amount: 500000, status: 'Paid', dueDate: '2023-06-15', paid: 500000 },
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-purple-800">Loan Management</h1>
          <button className="bg-purple-700 hover:bg-purple-800 text-white px-4 py-2 rounded-lg transition-colors">
            Apply for Loan
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Active Loans</h3>
            <p className="text-3xl font-bold text-purple-700">1</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Total Borrowed</h3>
            <p className="text-3xl font-bold text-purple-700">K 1,000,000</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Amount Paid</h3>
            <p className="text-3xl font-bold text-green-600">K 400,000</p>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="grid grid-cols-6 bg-purple-100 p-4 font-semibold text-purple-800">
            <div>Date</div>
            <div>Amount</div>
            <div>Status</div>
            <div>Due Date</div>
            <div>Paid</div>
            <div>Actions</div>
          </div>
          
          {loans.map((loan) => (
            <div key={loan.id} className="grid grid-cols-6 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors">
              <div>{loan.date}</div>
              <div className="font-medium">K {loan.amount.toLocaleString()}</div>
              <div>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  loan.status === 'Active' 
                    ? 'bg-yellow-100 text-yellow-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {loan.status}
                </span>
              </div>
              <div>{loan.dueDate}</div>
              <div className="font-semibold">K {loan.paid.toLocaleString()}</div>
              <div>
                <button className="text-purple-600 hover:text-purple-800 hover:underline mr-3">
                  View
                </button>
                {loan.status === 'Active' && (
                  <button className="text-green-600 hover:text-green-800 hover:underline">
                    Pay
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Loans;