<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransportDelivery extends Model
{
    use HasFactory;

    protected $fillable = [
        'delivery_number',
        'farmer_id',
        'vehicle_id',
        'transport_rate_id',
        'delivery_date',
        'departure_time',
        'arrival_time',
        'sugarcane_weight_tons',
        'distance_km',
        'transport_rate_per_ton',
        'fuel_surcharge',
        'total_transport_cost',
        'gross_sales_amount',
        'net_amount_after_transport',
        'status',
        'destination',
        'pickup_location',
        'delivery_notes',
        'payment_deducted',
        'payment_deducted_at',
        'weighbridge_ticket_number',
        'fuel_used_liters',
        'fuel_cost',
    ];

    protected $casts = [
        'delivery_date' => 'date',
        'departure_time' => 'datetime:H:i',
        'arrival_time' => 'datetime:H:i',
        'sugarcane_weight_tons' => 'decimal:2',
        'distance_km' => 'decimal:2',
        'transport_rate_per_ton' => 'decimal:2',
        'fuel_surcharge' => 'decimal:2',
        'total_transport_cost' => 'decimal:2',
        'gross_sales_amount' => 'decimal:2',
        'net_amount_after_transport' => 'decimal:2',
        'payment_deducted' => 'boolean',
        'payment_deducted_at' => 'datetime',
        'fuel_used_liters' => 'decimal:2',
        'fuel_cost' => 'decimal:2',
    ];

    /**
     * Get the farmer for this delivery
     */
    public function farmer()
    {
        return $this->belongsTo(Farmer::class);
    }

    /**
     * Get the vehicle used for this delivery
     */
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get the transport rate used
     */
    public function transportRate()
    {
        return $this->belongsTo(TransportRate::class);
    }

    /**
     * Calculate total transport cost
     */
    public function calculateTransportCost()
    {
        $baseCost = $this->sugarcane_weight_tons * $this->transport_rate_per_ton;
        $totalCost = $baseCost + $this->fuel_surcharge;

        $this->update(['total_transport_cost' => $totalCost]);

        return $totalCost;
    }

    /**
     * Calculate net amount after transport deduction
     */
    public function calculateNetAmount()
    {
        if ($this->gross_sales_amount) {
            $netAmount = $this->gross_sales_amount - $this->total_transport_cost;
            $this->update(['net_amount_after_transport' => $netAmount]);
            return $netAmount;
        }

        return null;
    }

    /**
     * Mark payment as deducted
     */
    public function markPaymentDeducted()
    {
        $this->update([
            'payment_deducted' => true,
            'payment_deducted_at' => now(),
        ]);
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'completed' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-red-100 text-red-800',
        ];

        return $badges[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Get formatted weight
     */
    public function getFormattedWeightAttribute()
    {
        return number_format($this->sugarcane_weight_tons, 2) . ' tons';
    }

    /**
     * Get formatted transport cost
     */
    public function getFormattedTransportCostAttribute()
    {
        return 'MWK ' . number_format($this->total_transport_cost, 2);
    }

    /**
     * Scope for completed deliveries
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending deliveries
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for deliveries by date range
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('delivery_date', [$startDate, $endDate]);
    }

    /**
     * Generate delivery number
     */
    public static function generateDeliveryNumber()
    {
        $year = date('Y');
        $lastDelivery = self::where('delivery_number', 'like', "DEL-{$year}-%")
            ->orderBy('delivery_number', 'desc')
            ->first();

        if ($lastDelivery) {
            $lastNumber = (int) substr($lastDelivery->delivery_number, -3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return "DEL-{$year}-" . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }
}
