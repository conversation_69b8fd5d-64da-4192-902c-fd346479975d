import React, { useState } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

const AdminLoans = ({ auth, loans }) => {
  const [selectedLoan, setSelectedLoan] = useState(null);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [showRepaymentModal, setShowRepaymentModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');

  const { data: approvalData, setData: setApprovalData, post: postApproval, processing: processingApproval, errors: approvalErrors, reset: resetApproval } = useForm({
    interest_rate: '5.0',
    admin_notes: '',
  });

  const { data: rejectionData, setData: setRejectionData, post: postRejection, processing: processingRejection, errors: rejectionErrors, reset: resetRejection } = useForm({
    rejection_reason: '',
  });

  const { data: repaymentData, setData: setRepaymentData, post: postRepayment, processing: processingRepayment, errors: repaymentErrors, reset: resetRepayment } = useForm({
    amount: '',
    payment_method: 'cash',
    receipt_number: '',
    notes: '',
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'MW',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-ZM', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'disbursed':
        return 'bg-blue-100 text-blue-800';
      case 'partial_repayment':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredLoans = loans?.filter(loan => {
    if (filterStatus === 'all') return true;
    return loan.status === filterStatus;
  }) || [];

  const handleApprove = (loan) => {
    setSelectedLoan(loan);
    setShowApprovalModal(true);
  };

  const handleReject = (loan) => {
    setSelectedLoan(loan);
    setShowRejectionModal(true);
  };

  const handleRepayment = (loan) => {
    setSelectedLoan(loan);
    setRepaymentData('amount', loan.remaining_balance.toString());
    setShowRepaymentModal(true);
  };

  const submitApproval = (e) => {
    e.preventDefault();
    postApproval(`/admin/loans/${selectedLoan.id}/approve`, {
      onSuccess: () => {
        setShowApprovalModal(false);
        setSelectedLoan(null);
        resetApproval();
      }
    });
  };

  const submitRejection = (e) => {
    e.preventDefault();
    postRejection(`/admin/loans/${selectedLoan.id}/reject`, {
      onSuccess: () => {
        setShowRejectionModal(false);
        setSelectedLoan(null);
        resetRejection();
      }
    });
  };

  const submitRepayment = (e) => {
    e.preventDefault();
    postRepayment(`/admin/loans/${selectedLoan.id}/repayment`, {
      onSuccess: () => {
        setShowRepaymentModal(false);
        setSelectedLoan(null);
        resetRepayment();
      }
    });
  };

  const disburseLoan = (loanId) => {
    if (confirm('Confirm loan disbursement? This action cannot be undone.')) {
      postApproval(`/admin/loans/${loanId}/disburse`, {
        onSuccess: () => {
          // Refresh page or update state
        }
      });
    }
  };

  const getStatusStats = () => {
    const total = loans?.length || 0;
    const pending = loans?.filter(l => l.status === 'pending').length || 0;
    const approved = loans?.filter(l => l.status === 'approved').length || 0;
    const disbursed = loans?.filter(l => l.status === 'disbursed').length || 0;
    const completed = loans?.filter(l => l.status === 'completed').length || 0;
    const rejected = loans?.filter(l => l.status === 'rejected').length || 0;

    return { total, pending, approved, disbursed, completed, rejected };
  };

  const stats = getStatusStats();

  return (
    <AdminLayout auth={auth}>
      <Head title="Loan Management - Admin" />

      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Loan Management</h1>
                <p className="mt-2 text-gray-600">Manage member loan applications and repayments</p>
              </div>
              <Link
                href="/dashboard/admin"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-6 gap-6 mb-8">
            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Total Loans</h3>
                  <p className="text-3xl font-bold text-blue-600">{stats.total}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-yellow-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Pending</h3>
                  <p className="text-3xl font-bold text-yellow-600">{stats.pending}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Approved</h3>
                  <p className="text-3xl font-bold text-green-600">{stats.approved}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-blue-600">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Disbursed</h3>
                  <p className="text-3xl font-bold text-blue-700">{stats.disbursed}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-700">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-gray-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Completed</h3>
                  <p className="text-3xl font-bold text-gray-600">{stats.completed}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center text-gray-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-red-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-600">Rejected</h3>
                  <p className="text-3xl font-bold text-red-600">{stats.rejected}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center text-red-600">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-medium text-gray-900">Filter by Status:</h3>
              <div className="flex space-x-2">
                {['all', 'pending', 'approved', 'disbursed', 'completed', 'rejected'].map((status) => (
                  <button
                    key={status}
                    onClick={() => setFilterStatus(status)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      filterStatus === status
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Loans Table */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                Loan Applications ({filteredLoans.length})
              </h3>
            </div>

            {filteredLoans.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Member & Purpose
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount & Terms
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status & Dates
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Repayment Progress
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredLoans.map((loan) => (
                      <tr key={loan.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {loan.farmer_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {loan.purpose}
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              Applied: {formatDate(loan.application_date)}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            <div className="font-medium text-lg text-green-600">
                              {formatCurrency(loan.amount)}
                            </div>
                            <div className="text-xs text-gray-500">
                              {loan.term_months} months @ {loan.interest_rate}%
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="space-y-1">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(loan.status)}`}>
                              {loan.status.replace('_', ' ')}
                            </span>
                            {loan.approval_date && (
                              <div className="text-xs text-gray-500">
                                Approved: {formatDate(loan.approval_date)}
                              </div>
                            )}
                            {loan.disbursement_date && (
                              <div className="text-xs text-gray-500">
                                Disbursed: {formatDate(loan.disbursement_date)}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {['disbursed', 'partial_repayment', 'completed'].includes(loan.status) && (
                            <div className="space-y-1">
                              <div className="text-sm text-gray-900">
                                Paid: {formatCurrency(loan.total_repaid)}
                              </div>
                              <div className="text-sm text-gray-500">
                                Balance: {formatCurrency(loan.remaining_balance)}
                              </div>
                              <div className="text-xs text-gray-400">
                                {loan.repayment_count} payments
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-green-600 h-2 rounded-full"
                                  style={{ width: `${(loan.total_repaid / loan.amount) * 100}%` }}
                                ></div>
                              </div>
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                          {loan.status === 'pending' && (
                            <>
                              <button
                                onClick={() => handleApprove(loan)}
                                className="text-green-600 hover:text-green-900"
                              >
                                Approve
                              </button>
                              <button
                                onClick={() => handleReject(loan)}
                                className="text-red-600 hover:text-red-900"
                              >
                                Reject
                              </button>
                            </>
                          )}
                          {loan.status === 'approved' && (
                            <button
                              onClick={() => disburseLoan(loan.id)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Disburse
                            </button>
                          )}
                          {['disbursed', 'partial_repayment'].includes(loan.status) && (
                            <button
                              onClick={() => handleRepayment(loan)}
                              className="text-purple-600 hover:text-purple-900"
                            >
                              Record Payment
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Loans Found</h3>
                <p className="text-gray-600">No loan applications match the current filter.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Approval Modal */}
      {showApprovalModal && selectedLoan && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Approve Loan Application
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Approve loan for <strong>{selectedLoan.farmer_name}</strong> - {formatCurrency(selectedLoan.amount)}
              </p>

              <form onSubmit={submitApproval}>
                <div className="mb-4">
                  <label htmlFor="interest_rate" className="block text-sm font-medium text-gray-700 mb-2">
                    Interest Rate (% per annum) <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    id="interest_rate"
                    step="0.1"
                    min="0"
                    max="100"
                    value={approvalData.interest_rate}
                    onChange={(e) => setApprovalData('interest_rate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                    required
                  />
                  {approvalErrors.interest_rate && (
                    <p className="mt-1 text-sm text-red-600">{approvalErrors.interest_rate}</p>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="admin_notes" className="block text-sm font-medium text-gray-700 mb-2">
                    Admin Notes (Optional)
                  </label>
                  <textarea
                    id="admin_notes"
                    value={approvalData.admin_notes}
                    onChange={(e) => setApprovalData('admin_notes', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                    placeholder="Add any notes or conditions..."
                  />
                  {approvalErrors.admin_notes && (
                    <p className="mt-1 text-sm text-red-600">{approvalErrors.admin_notes}</p>
                  )}
                </div>

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={processingApproval}
                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                  >
                    {processingApproval ? 'Approving...' : 'Approve Loan'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowApprovalModal(false);
                      setSelectedLoan(null);
                      resetApproval();
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Rejection Modal */}
      {showRejectionModal && selectedLoan && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Reject Loan Application
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Reject loan for <strong>{selectedLoan.farmer_name}</strong> - {formatCurrency(selectedLoan.amount)}
              </p>

              <form onSubmit={submitRejection}>
                <div className="mb-4">
                  <label htmlFor="rejection_reason" className="block text-sm font-medium text-gray-700 mb-2">
                    Rejection Reason <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="rejection_reason"
                    value={rejectionData.rejection_reason}
                    onChange={(e) => setRejectionData('rejection_reason', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"
                    placeholder="Please provide a detailed reason for rejection..."
                    required
                  />
                  {rejectionErrors.rejection_reason && (
                    <p className="mt-1 text-sm text-red-600">{rejectionErrors.rejection_reason}</p>
                  )}
                </div>

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={processingRejection}
                    className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                  >
                    {processingRejection ? 'Rejecting...' : 'Reject Loan'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowRejectionModal(false);
                      setSelectedLoan(null);
                      resetRejection();
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Repayment Modal */}
      {showRepaymentModal && selectedLoan && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Record Loan Repayment
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Record payment for <strong>{selectedLoan.farmer_name}</strong>
                <br />
                Outstanding Balance: <strong>{formatCurrency(selectedLoan.remaining_balance)}</strong>
              </p>

              <form onSubmit={submitRepayment}>
                <div className="mb-4">
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Amount <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    id="amount"
                    step="0.01"
                    min="0.01"
                    max={selectedLoan.remaining_balance}
                    value={repaymentData.amount}
                    onChange={(e) => setRepaymentData('amount', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                    required
                  />
                  {repaymentErrors.amount && (
                    <p className="mt-1 text-sm text-red-600">{repaymentErrors.amount}</p>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="payment_method" className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Method <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="payment_method"
                    value={repaymentData.payment_method}
                    onChange={(e) => setRepaymentData('payment_method', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                    required
                  >
                    <option value="cash">Cash</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="mobile_money">Mobile Money</option>
                    <option value="check">Check</option>
                  </select>
                  {repaymentErrors.payment_method && (
                    <p className="mt-1 text-sm text-red-600">{repaymentErrors.payment_method}</p>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="receipt_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Receipt Number
                  </label>
                  <input
                    type="text"
                    id="receipt_number"
                    value={repaymentData.receipt_number}
                    onChange={(e) => setRepaymentData('receipt_number', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Optional receipt/reference number"
                  />
                  {repaymentErrors.receipt_number && (
                    <p className="mt-1 text-sm text-red-600">{repaymentErrors.receipt_number}</p>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                    Notes
                  </label>
                  <textarea
                    id="notes"
                    value={repaymentData.notes}
                    onChange={(e) => setRepaymentData('notes', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Optional notes about this payment..."
                  />
                  {repaymentErrors.notes && (
                    <p className="mt-1 text-sm text-red-600">{repaymentErrors.notes}</p>
                  )}
                </div>

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={processingRepayment}
                    className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50"
                  >
                    {processingRepayment ? 'Recording...' : 'Record Payment'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowRepaymentModal(false);
                      setSelectedLoan(null);
                      resetRepayment();
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default AdminLoans;