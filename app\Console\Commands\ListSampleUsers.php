<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Farmer;
use App\Models\Contribution;
use App\Models\Loan;
use App\Models\LoanRepayment;
use App\Models\SavingsAccount;
use App\Models\EquipmentRequest;
use App\Models\MeetingAttendance;
use App\Models\Feedback;
use App\Models\Document;

class ListSampleUsers extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'list:sample-users';

    /**
     * The console command description.
     */
    protected $description = 'List all sample/test users and their related data in the database';

    /**
     * Essential users that should never be deleted
     */
    protected $essentialEmails = [
        '<EMAIL>',
        '<EMAIL>', // Admin user from AdminSeeder
        '<EMAIL>',           // Member user from MemberSeeder
        '<EMAIL>',            // Staff user from StaffSeeder
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('👥 Database Users Overview');
        $this->info('==========================');

        // Get all users
        $allUsers = User::with('role', 'farmer')->get();
        
        if ($allUsers->isEmpty()) {
            $this->warn('No users found in the database.');
            return 0;
        }

        // Separate essential and sample users
        $essentialUsers = $allUsers->whereIn('email', $this->essentialEmails);
        $sampleUsers = $allUsers->whereNotIn('email', $this->essentialEmails);

        // Show essential users
        $this->info("\n✅ Essential Users (will be preserved):");
        if ($essentialUsers->isNotEmpty()) {
            $this->table(
                ['ID', 'Name', 'Email', 'Role', 'Has Farmer Profile', 'Created'],
                $essentialUsers->map(function ($user) {
                    return [
                        $user->id,
                        $user->name,
                        $user->email,
                        $user->role->name ?? 'No Role',
                        $user->farmer ? 'Yes' : 'No',
                        $user->created_at->format('Y-m-d H:i:s')
                    ];
                })->toArray()
            );
        } else {
            $this->line('  No essential users found.');
        }

        // Show sample users
        $this->info("\n🧪 Sample/Test Users (candidates for deletion):");
        if ($sampleUsers->isNotEmpty()) {
            $this->table(
                ['ID', 'Name', 'Email', 'Role', 'Has Farmer Profile', 'Created'],
                $sampleUsers->map(function ($user) {
                    return [
                        $user->id,
                        $user->name,
                        $user->email,
                        $user->role->name ?? 'No Role',
                        $user->farmer ? 'Yes' : 'No',
                        $user->created_at->format('Y-m-d H:i:s')
                    ];
                })->toArray()
            );

            // Show detailed breakdown for sample users
            $this->showSampleUsersBreakdown($sampleUsers);
        } else {
            $this->info('  ✅ No sample users found - database is clean!');
        }

        // Show summary
        $this->info("\n📊 Summary:");
        $this->line("  • Total users: {$allUsers->count()}");
        $this->line("  • Essential users: {$essentialUsers->count()}");
        $this->line("  • Sample users: {$sampleUsers->count()}");

        if ($sampleUsers->isNotEmpty()) {
            $this->info("\n💡 To delete sample users, run:");
            $this->line("  php artisan cleanup:sample-users");
        }

        return 0;
    }

    /**
     * Show detailed breakdown of sample users' data
     */
    protected function showSampleUsersBreakdown($sampleUsers)
    {
        $this->info("\n📋 Sample Users Data Breakdown:");

        $farmerIds = $sampleUsers->pluck('farmer.id')->filter();
        $userIds = $sampleUsers->pluck('id');

        $breakdown = [
            'Farmer Profiles' => Farmer::whereIn('user_id', $userIds)->count(),
            'Contributions' => Contribution::whereIn('farmer_id', $farmerIds)->count(),
            'Loans' => Loan::whereIn('farmer_id', $farmerIds)->count(),
            'Loan Repayments' => LoanRepayment::whereHas('loan', function($q) use ($farmerIds) {
                $q->whereIn('farmer_id', $farmerIds);
            })->count(),
            'Savings Accounts' => SavingsAccount::whereIn('farmer_id', $farmerIds)->count(),
            'Equipment Requests' => EquipmentRequest::whereIn('user_id', $userIds)->count(),
            'Meeting Attendances' => MeetingAttendance::whereIn('farmer_id', $farmerIds)->count(),
            'Feedback Submissions' => Feedback::whereIn('submitted_by', $userIds)->count(),
            'Documents' => Document::whereIn('uploaded_by', $userIds)->count(),
        ];

        $totalRecords = 0;
        foreach ($breakdown as $type => $count) {
            $this->line("  • {$type}: {$count}");
            $totalRecords += $count;
        }

        $this->line("  • Total related records: {$totalRecords}");

        if ($totalRecords > 0) {
            $this->warn("\n⚠️  Deleting sample users will also delete all their related data!");
        }
    }
}
