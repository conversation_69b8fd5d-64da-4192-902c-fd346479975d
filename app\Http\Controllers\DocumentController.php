<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Document;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocumentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the document library.
     */
    public function index(Request $request)
    {
        $search = $request->get('search');
        $category = $request->get('category');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Document::with('uploader');

        // Filter by access level based on user role
        $user = auth()->user();
        if ($user->role->name !== 'admin') {
            $query->where(function ($q) use ($user) {
                $q->where('access_level', 'public')
                  ->orWhere('access_level', 'member');
            });
        }

        // Only show active documents to non-admin users
        if ($user->role->name !== 'admin') {
            $query->where('is_active', true);
        }

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('file_name', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($category && $category !== 'all') {
            $query->where('category', $category);
        }

        // Sorting
        $query->orderBy($sortBy, $sortOrder);

        $documents = $query->get()->map(function ($document) {
            return [
                'id' => $document->id,
                'title' => $document->title,
                'description' => $document->description,
                'file_name' => $document->file_name,
                'file_type' => $document->file_type,
                'file_size' => $document->formatted_file_size,
                'file_icon' => $document->file_icon ?? '📎',
                'category' => $document->category,
                'category_display' => $document->category_display ?? ucfirst($document->category),
                'access_level' => $document->access_level,
                'download_count' => $document->download_count,
                'uploaded_by' => $document->uploader->name ?? 'Unknown',
                'created_at' => $document->created_at->format('M d, Y'),
                'file_extension' => $document->file_extension,
                'is_featured' => $document->is_featured ?? false,
                'download_url' => route('documents.download', $document->id),
            ];
        });

        $stats = [
            'total_documents' => Document::count(),
            'forms' => Document::where('category', 'forms')->count(),
            'policies' => Document::where('category', 'policies')->count(),
            'reports' => Document::where('category', 'reports')->count(),
            'training' => Document::where('category', 'training')->count(),
        ];

        // Get featured documents
        $featuredDocuments = Document::where('is_featured', true)
            ->where('is_active', true)
            ->limit(3)
            ->get()
            ->map(function ($document) {
                return [
                    'id' => $document->id,
                    'title' => $document->title,
                    'description' => $document->description,
                    'category_display' => $document->category_display ?? ucfirst($document->category),
                    'file_icon' => $document->file_icon ?? '📎',
                    'download_url' => route('documents.download', $document->id),
                ];
            });

        // Get categories with counts
        $categories = Document::where('is_active', true)
            ->selectRaw('category, COUNT(*) as count')
            ->groupBy('category')
            ->get()
            ->mapWithKeys(function ($item) {
                $document = new Document(['category' => $item->category]);
                return [$item->category => [
                    'name' => $document->category_display ?? ucfirst($item->category),
                    'count' => $item->count
                ]];
            });

        return Inertia::render('Documents/Index', [
            'documents' => $documents,
            'featuredDocuments' => $featuredDocuments,
            'categories' => $categories,
            'stats' => $stats,
            'filters' => [
                'search' => $search,
                'category' => $category,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
            ],
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Admin: Display all documents for management
     */
    public function adminIndex(Request $request)
    {
        $search = $request->get('search');
        $category = $request->get('category');
        $status = $request->get('status');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Document::with('uploader');

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('file_name', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($category && $category !== 'all') {
            $query->where('category', $category);
        }

        // Filter by status
        if ($status && $status !== 'all') {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($status === 'featured') {
                $query->where('is_featured', true);
            }
        }

        // Sorting
        $query->orderBy($sortBy, $sortOrder);

        $documents = $query->get()->map(function ($document) {
            return [
                'id' => $document->id,
                'title' => $document->title,
                'description' => $document->description,
                'file_name' => $document->file_name,
                'file_type' => $document->file_type,
                'file_size' => $document->formatted_file_size,
                'file_icon' => $document->file_icon ?? '📎',
                'category' => $document->category,
                'category_display' => $document->category_display ?? ucfirst($document->category),
                'access_level' => $document->access_level,
                'download_count' => $document->download_count,
                'uploaded_by' => $document->uploader->name ?? 'Unknown',
                'created_at' => $document->created_at->format('M d, Y'),
                'is_featured' => $document->is_featured ?? false,
                'is_active' => $document->is_active ?? true,
                'download_url' => route('documents.download', $document->id),
            ];
        });

        $stats = [
            'total' => Document::count(),
            'active' => Document::where('is_active', true)->count(),
            'featured' => Document::where('is_featured', true)->count(),
            'total_downloads' => Document::sum('download_count'),
        ];

        // Get categories for filter dropdown
        $categories = Document::selectRaw('category, COUNT(*) as count')
            ->groupBy('category')
            ->get()
            ->mapWithKeys(function ($item) {
                $document = new Document(['category' => $item->category]);
                return [$item->category => [
                    'name' => $document->category_display ?? ucfirst($item->category),
                    'count' => $item->count
                ]];
            });

        return Inertia::render('Admin/Documents', [
            'documents' => $documents,
            'categories' => $categories,
            'stats' => $stats,
            'filters' => [
                'search' => $search,
                'category' => $category,
                'status' => $status,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
            ],
            'auth' => ['user' => User::with('role')->find(auth()->id())]
        ]);
    }

    /**
     * Store a new document (Admin only).
     */
    public function store(Request $request)
    {
        if (auth()->user()->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|in:forms,policies,reports,training,general',
            'access_level' => 'required|in:public,member,admin',
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        $file = $request->file('file');
        $fileName = time() . '_' . Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $file->getClientOriginalExtension();
        $filePath = $file->storeAs('documents', $fileName, 'public');

        Document::create([
            'title' => $request->title,
            'description' => $request->description,
            'file_name' => $file->getClientOriginalName(),
            'file_path' => $filePath,
            'file_type' => $file->getClientMimeType(),
            'file_size' => $file->getSize(),
            'category' => $request->category,
            'access_level' => $request->access_level,
            'is_featured' => $request->boolean('is_featured', false),
            'is_active' => $request->boolean('is_active', true),
            'uploaded_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Document uploaded successfully!');
    }

    /**
     * Download a document.
     */
    public function download($id)
    {
        $document = Document::findOrFail($id);
        
        // Check access permissions
        if (!$document->canBeAccessedBy(auth()->user())) {
            abort(403, 'You do not have permission to access this document.');
        }

        // Increment download count
        $document->incrementDownloadCount();

        // Return file download
        if (Storage::disk('public')->exists($document->file_path)) {
            return response()->download(
                storage_path('app/public/' . $document->file_path),
                $document->file_name
            );
        }

        abort(404, 'File not found.');
    }

    /**
     * Delete a document (Admin only).
     */
    public function destroy($id)
    {
        if (auth()->user()->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $document = Document::findOrFail($id);
        
        // Delete file from storage
        if (Storage::disk('public')->exists($document->file_path)) {
            Storage::disk('public')->delete($document->file_path);
        }

        $document->delete();

        return redirect()->back()->with('success', 'Document deleted successfully!');
    }
}
