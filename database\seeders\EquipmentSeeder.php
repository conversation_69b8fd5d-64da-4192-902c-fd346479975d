<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FarmEquipment;
use App\Models\EquipmentRequest;
use App\Models\Farmer;
use App\Models\User;
use Carbon\Carbon;

class EquipmentSeeder extends Seeder
{
    public function run(): void
    {
        // Find or create a cooperative admin user for equipment ownership
        $adminUser = User::where('email', '<EMAIL>')->first();
        if (!$adminUser) {
            $adminUser = User::create([
                'name' => 'Cooperative Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('admin123'),
                'role_id' => 1, // Admin role
            ]);
        }

        // Create or find admin farmer profile
        $adminFarmer = Farmer::firstOrCreate(
            ['user_id' => $adminUser->id],
            [
                'farm_size' => '50 hectares',
                'location_coordinates' => '-15.4167, 28.2833',
                'membership_date' => Carbon::now()->subYears(5),
                'identification_number' => 'ADMIN001',
                'bank_account_number' => '**********',
                'bank_name' => 'Cooperative Bank',
                'gender' => 'male',
                'address' => 'Cooperative Headquarters, Lusaka',
                'land_size' => 100.0,
                'status' => 'active'
            ]
        );

        // Create farm equipment
        $equipment = [
            [
                'name' => 'John Deere Tractor 5075E',
                'type' => 'Tractor',
                'model' => '5075E',
                'purchase_year' => 2020,
                'value' => 450000.00,
                'rental_cost_per_day' => 800.00,
                'description' => '75HP utility tractor perfect for medium-scale farming operations',
                'is_available' => true,
                'available_for_rent' => true,
            ],
            [
                'name' => 'Disc Harrow',
                'type' => 'Tillage Equipment',
                'model' => 'DH-24',
                'purchase_year' => 2019,
                'value' => 85000.00,
                'rental_cost_per_day' => 200.00,
                'description' => '24-disc harrow for soil preparation and cultivation',
                'is_available' => true,
                'available_for_rent' => true,
            ],
            [
                'name' => 'Seed Planter',
                'type' => 'Planting Equipment',
                'model' => 'SP-8R',
                'purchase_year' => 2021,
                'value' => 120000.00,
                'rental_cost_per_day' => 300.00,
                'description' => '8-row precision seed planter for maize and soybeans',
                'is_available' => true,
                'available_for_rent' => true,
            ],
            [
                'name' => 'Combine Harvester',
                'type' => 'Harvesting Equipment',
                'model' => 'CH-2000',
                'purchase_year' => 2018,
                'value' => 850000.00,
                'rental_cost_per_day' => 1500.00,
                'description' => 'Multi-crop combine harvester for efficient grain harvesting',
                'is_available' => true,
                'available_for_rent' => true,
            ],
            [
                'name' => 'Water Pump',
                'type' => 'Irrigation Equipment',
                'model' => 'WP-500',
                'purchase_year' => 2022,
                'value' => 25000.00,
                'rental_cost_per_day' => 100.00,
                'description' => 'High-capacity water pump for irrigation systems',
                'is_available' => true,
                'available_for_rent' => true,
            ],
        ];

        foreach ($equipment as $item) {
            FarmEquipment::firstOrCreate(
                ['name' => $item['name'], 'owner_id' => $adminFarmer->id],
                array_merge($item, ['owner_id' => $adminFarmer->id])
            );
        }

        // Find the member user
        $memberUser = User::where('email', '<EMAIL>')->first();
        if ($memberUser && $memberUser->farmer) {
            $memberFarmer = $memberUser->farmer;

            // Create some sample equipment requests
            $tractor = FarmEquipment::where('name', 'John Deere Tractor 5075E')->first();
            $planter = FarmEquipment::where('name', 'Seed Planter')->first();

            if ($tractor) {
                EquipmentRequest::firstOrCreate(
                    [
                        'equipment_id' => $tractor->id,
                        'user_id' => $memberFarmer->user_id,
                        'start_date' => Carbon::now()->addDays(5)
                    ],
                    [
                        'end_date' => Carbon::now()->addDays(8),
                        'purpose' => 'Land preparation for maize planting season',
                        'status' => 'approved',
                        'approved_by' => $adminUser->id,
                        'estimated_cost' => $tractor->daily_rental_rate * 4, // 4 days
                        'payment_status' => 'pending',
                        'admin_notes' => 'Approved for experienced member. Please ensure proper maintenance.',
                    ]
                );
            }

            if ($planter) {
                EquipmentRequest::firstOrCreate(
                    [
                        'equipment_id' => $planter->id,
                        'user_id' => $memberFarmer->user_id,
                        'start_date' => Carbon::now()->subDays(2)
                    ],
                    [
                        'end_date' => Carbon::now()->addDays(1),
                        'purpose' => 'Planting maize on 5 hectares',
                        'status' => 'pending',
                        'estimated_cost' => $planter->daily_rental_rate * 3, // 3 days
                        'payment_status' => 'pending',
                    ]
                );
            }
        }

        $this->command->info('Equipment and sample requests created successfully!');
    }
}
