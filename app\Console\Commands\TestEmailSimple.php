<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\WelcomeNewMember;

class TestEmailSimple extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-simple {email} {name?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a simple test email using <PERSON><PERSON> built-in methods';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $name = $this->argument('name') ?? 'Test User';

        $this->info("Testing Laravel's built-in email system...");
        $this->info("Email: {$email}");
        $this->info("Name: {$name}");
        $this->info("Mail Driver: " . config('mail.default'));

        try {
            // Send the email using <PERSON><PERSON>'s Mail facade
            Mail::to($email)->send(new WelcomeNewMember(
                $name,
                $email,
                'TestPassword123',
                url('/login')
            ));

            $this->info("✅ Email sent successfully using <PERSON><PERSON>'s built-in Mail system!");
            $this->info("Check your log file at: storage/logs/laravel.log");
            
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Email failed to send!");
            $this->error("Error: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            
            return 1;
        }
    }
}
