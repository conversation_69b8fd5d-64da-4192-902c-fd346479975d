<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Farmer;
use App\Models\Contribution;
use App\Models\Loan;
use App\Models\LoanRepayment;
use App\Models\SavingsAccount;
use App\Models\FarmEquipment;
use App\Models\EquipmentRequest;
use App\Models\User;
use App\Models\Role;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class SampleDataSeeder extends Seeder
{
    public function run()
    {
        // Get existing farmers
        $farmers = Farmer::all();

        if ($farmers->count() === 0) {
            $this->command->info('No farmers found. Creating sample farmers...');
            $farmers = $this->createSampleFarmers();
        }

        $this->command->info('Creating sample data for ' . $farmers->count() . ' farmers...');

        // Create contributions for the last 6 months
        foreach ($farmers as $farmer) {
            for ($i = 0; $i < 6; $i++) {
                $contributionDate = Carbon::now()->subMonths($i)->startOfMonth()->addDays(rand(1, 28));
                
                Contribution::create([
                    'farmer_id' => $farmer->id,
                    'member_id' => $farmer->user_id,
                    'amount' => [300, 500, 750, 1000][rand(0, 3)],
                    'type' => ['monthly', 'monthly', 'monthly', 'special'][rand(0, 3)],
                    'description' => 'Monthly contribution for ' . $contributionDate->format('F Y'),
                    'payment_method' => ['cash', 'mobile_money', 'bank_transfer'][rand(0, 2)],
                    'reference_number' => 'REF' . rand(100000, 999999),
                    'contribution_date' => $contributionDate,
                    'status' => ['confirmed', 'confirmed', 'pending'][rand(0, 2)],
                    'recorded_by' => 1, // Admin user
                ]);
            }
        }

        // Create savings accounts for farmers who don't have them
        foreach ($farmers as $farmer) {
            if (!$farmer->savingsAccounts()->exists()) {
                SavingsAccount::create([
                    'farmer_id' => $farmer->id,
                    'account_number' => 'SAV' . str_pad($farmer->id, 6, '0', STR_PAD_LEFT),
                    'balance' => rand(500, 5000),
                    'interest_rate' => 5.0,
                    'opening_date' => Carbon::now()->subDays(rand(30, 365)),
                ]);
            }
        }

        // Create loans for some farmers
        $loanStatuses = ['pending', 'approved', 'disbursed', 'partial_repayment', 'completed'];
        foreach ($farmers->take(6) as $farmer) {
            $loanAmount = [2000, 3000, 5000, 7500, 10000][rand(0, 4)];
            $status = $loanStatuses[rand(0, 4)];
            
            $loan = Loan::create([
                'farmer_id' => $farmer->id,
                'amount' => $loanAmount,
                'interest_rate' => 12.0,
                'term_months' => [6, 12, 18, 24][rand(0, 3)],
                'purpose' => ['Farm expansion', 'Equipment purchase', 'Seed capital', 'Livestock'][rand(0, 3)],
                'status' => $status,
                'application_date' => Carbon::now()->subDays(rand(30, 180)),
                'approved_date' => $status !== 'pending' ? Carbon::now()->subDays(rand(15, 150)) : null,
                'disbursed_date' => in_array($status, ['disbursed', 'partial_repayment', 'completed']) ? Carbon::now()->subDays(rand(10, 120)) : null,
            ]);

            // Create loan repayments for disbursed loans
            if (in_array($status, ['partial_repayment', 'completed'])) {
                $monthlyPayment = $loanAmount / $loan->term_months;
                $paymentsCount = $status === 'completed' ? $loan->term_months : rand(1, $loan->term_months - 1);
                
                for ($i = 0; $i < $paymentsCount; $i++) {
                    LoanRepayment::create([
                        'loan_id' => $loan->id,
                        'amount' => $monthlyPayment + rand(-50, 50),
                        'payment_date' => Carbon::now()->subDays(rand(5, 90)),
                        'payment_method' => ['cash', 'mobile_money', 'bank_transfer'][rand(0, 2)],
                        'received_by' => 1, // Admin user
                    ]);
                }
            }
        }

        // Create farm equipment
        $equipment = [
            ['name' => 'Tractor - John Deere 5055E', 'type' => 'tractor', 'value' => 45000, 'rental_cost' => 200],
            ['name' => 'Plough - 3-Disc', 'type' => 'plough', 'value' => 1500, 'rental_cost' => 50],
            ['name' => 'Harrow - 20-Disc', 'type' => 'harrow', 'value' => 2500, 'rental_cost' => 75],
            ['name' => 'Seed Drill - 12-Row', 'type' => 'planter', 'value' => 8000, 'rental_cost' => 120],
            ['name' => 'Harvester - Combine', 'type' => 'harvester', 'value' => 85000, 'rental_cost' => 300],
            ['name' => 'Irrigation Pump', 'type' => 'pump', 'value' => 3500, 'rental_cost' => 80],
        ];

        $equipmentIds = [];
        foreach ($equipment as $item) {
            $equipmentRecord = FarmEquipment::create([
                'name' => $item['name'],
                'type' => $item['type'],
                'description' => 'High-quality farm equipment for cooperative members',
                'value' => $item['value'],
                'rental_cost_per_day' => $item['rental_cost'],
                'purchase_date' => Carbon::now()->subDays(rand(100, 500)),
                'is_available' => rand(0, 1),
                'available_for_rent' => true,
            ]);
            $equipmentIds[] = $equipmentRecord->id;
        }

        // Create equipment requests
        foreach ($farmers->take(5) as $farmer) {
            $equipmentId = $equipmentIds[rand(0, count($equipmentIds) - 1)];
            $equipment = FarmEquipment::find($equipmentId);
            $days = rand(1, 7);
            
            EquipmentRequest::create([
                'farmer_id' => $farmer->id,
                'equipment_id' => $equipmentId,
                'request_date' => Carbon::now()->subDays(rand(1, 30)),
                'start_date' => Carbon::now()->addDays(rand(1, 10)),
                'end_date' => Carbon::now()->addDays(rand(11, 20)),
                'purpose' => ['Land preparation', 'Planting', 'Harvesting', 'Irrigation'][rand(0, 3)],
                'status' => ['pending', 'approved', 'completed', 'rejected'][rand(0, 3)],
                'rental_cost_per_day' => $equipment->rental_cost_per_day,
                'total_rental_cost' => $equipment->rental_cost_per_day * $days,
                'payment_status' => ['pending', 'paid', 'overdue'][rand(0, 2)],
            ]);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . ($farmers->count() * 6) . ' contributions');
        $this->command->info('- ' . $farmers->count() . ' savings accounts');
        $this->command->info('- 6 loans with repayments');
        $this->command->info('- 6 pieces of farm equipment');
        $this->command->info('- 5 equipment requests');
    }

    private function createSampleFarmers()
    {
        $memberRole = Role::where('name', 'member')->first();

        if (!$memberRole) {
            $this->command->error('Member role not found. Please run the role seeder first.');
            return collect();
        }

        $sampleMembers = [
            ['name' => 'John Mwanza', 'email' => '<EMAIL>'],
            ['name' => 'Mary Banda', 'email' => '<EMAIL>'],
            ['name' => 'Peter Phiri', 'email' => '<EMAIL>'],
            ['name' => 'Sarah Tembo', 'email' => '<EMAIL>'],
            ['name' => 'James Mulenga', 'email' => '<EMAIL>'],
            ['name' => 'Grace Chanda', 'email' => '<EMAIL>'],
        ];

        $farmers = collect();

        foreach ($sampleMembers as $memberData) {
            // Create or get existing user
            $user = User::firstOrCreate(
                ['email' => $memberData['email']],
                [
                    'name' => $memberData['name'],
                    'password' => Hash::make('password123'),
                    'role_id' => $memberRole->id,
                    'is_active' => true,
                ]
            );

            // Create farmer profile if it doesn't exist
            $farmer = Farmer::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'identification_number' => 'ID' . rand(100000, 999999),
                    'phone' => '+26097712' . rand(1000, 9999),
                    'address' => 'Lusaka, Zambia',
                    'land_size' => rand(1, 10),
                    'farm_location' => ['Chongwe District', 'Kafue District', 'Chilanga District'][rand(0, 2)],
                    'crops_grown' => ['Maize, Soybeans', 'Maize, Vegetables', 'Soybeans, Groundnuts'][rand(0, 2)],
                    'farming_experience' => rand(2, 20),
                    'status' => 'active',
                    'membership_date' => Carbon::now()->subDays(rand(30, 365)),
                ]
            );

            $farmers->push($farmer);
        }

        $this->command->info('Created ' . $farmers->count() . ' sample farmers.');
        return $farmers;
    }
}
