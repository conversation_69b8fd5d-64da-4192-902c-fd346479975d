import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';

export default function AdminLayout({ auth, children }) {
    const [sidebarOpen, setSidebarOpen] = useState(false);

    return (
        <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex flex-col lg:flex-row">
            <Head title="Admin Panel" />

            {/* Mobile sidebar overlay */}
            {sidebarOpen && (
                <div className="fixed inset-0 z-40 lg:hidden">
                    <div 
                        className="fixed inset-0 bg-gray-600 bg-opacity-75" 
                        onClick={() => setSidebarOpen(false)}
                        aria-hidden="true"
                    ></div>
                    <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white h-full">
                        <div className="absolute top-0 right-0 -mr-12 pt-2">
                            <button
                                className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                                onClick={() => setSidebarOpen(false)}
                            >
                                <span className="sr-only">Close sidebar</span>
                                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                            <div className="flex-shrink-0 flex items-center px-4">
                                <div className="flex items-center">
                                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                        <span className="text-white font-bold text-lg">S</span>
                                    </div>
                                    <div className="ml-3">
                                        <h1 className="text-lg font-bold text-gray-900">Siyamphanje</h1>
                                        <p className="text-sm text-gray-500">Admin Panel</p>
                                    </div>
                                </div>
                            </div>
                            <nav className="mt-5 px-2 space-y-1">
                                <Link
                                    href="/dashboard/admin"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-9 9a1 1 0 001.414 1.414L2 12.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-4.586l.293.293a1 1 0 001.414-1.414l-9-9z" />
                                    </svg>
                                    Dashboard
                                </Link>
                                <Link
                                    href="/admin/members"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                                    </svg>
                                    Manage Members
                                </Link>
                                <Link
                                    href="/admin/applications"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                    </svg>
                                    Member Applications
                                </Link>
                                <Link
                                    href="/admin/contributions"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                                    </svg>
                                    Contributions
                                </Link>
                                <Link
                                    href="/admin/loans"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                    </svg>
                                    Loans
                                </Link>
                                <Link
                                    href="/admin/equipment-requests"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                    </svg>
                                    Equipment Requests
                                </Link>
                                <Link
                                    href="/admin/equipment"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                                    </svg>
                                    Equipment Management
                                </Link>
                                <Link
                                    href="/admin/documents"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                    </svg>
                                    Document Management
                                </Link>
                                <Link
                                    href="/admin/transport"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                                        <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-.293-.707L15 4.586A1 1 0 0014.414 4H14v3z" />
                                    </svg>
                                    Transport Management
                                </Link>
                                <Link
                                    href="/admin/feedback"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                    Feedback
                                </Link>
                                <Link
                                    href="/admin/events"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                    </svg>
                                    Events
                                </Link>
                                <Link
                                    href="/admin/meetings"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                    </svg>
                                    Meetings
                                </Link>
                                <Link
                                    href="/admin/emails"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    Email Notifications
                                </Link>
                                <Link
                                    href="/admin/notifications"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                                    </svg>
                                    Notifications
                                </Link>
                                <Link
                                    href="/admin/reports"
                                    className="text-gray-700 hover:bg-gray-100 group flex items-center px-2 py-2 text-base font-medium rounded-md"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <svg className="text-gray-400 group-hover:text-gray-500 mr-4 h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                    </svg>
                                    Reports
                                </Link>
                            </nav>
                        </div>
                        {/* Mobile user profile */}
                        <div className="flex-shrink-0 border-t border-gray-200 p-4">
                            <div className="flex items-center">
                                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                                    <span className="text-white font-semibold text-sm">
                                        {auth?.user?.name?.charAt(0)?.toUpperCase() || 'A'}
                                    </span>
                                </div>
                                <div className="ml-3 flex-1">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                        {auth?.user?.name || 'Administrator'}
                                    </p>
                                    <p className="text-xs text-gray-500 truncate">
                                        {auth?.user?.email || '<EMAIL>'}
                                    </p>
                                </div>
                                <Link
                                    href="/logout"
                                    method="post"
                                    className="ml-2 p-1 text-gray-400 hover:text-gray-500"
                                >
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
                                    </svg>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Desktop Sidebar */}
            <div className="hidden lg:flex lg:w-64 lg:flex-col">
                <div className="flex flex-col flex-grow bg-white shadow-xl h-screen sticky top-0">
                    {/* Logo */}
                    <div className="flex items-center flex-shrink-0 px-6 py-6 border-b border-gray-200">
                        <div className="flex items-center">
                            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold text-lg">S</span>
                            </div>
                            <div className="ml-3">
                                <h1 className="text-lg font-bold text-gray-900">Siyamphanje</h1>
                                <p className="text-sm text-gray-500">Admin Panel</p>
                            </div>
                        </div>
                    </div>

                    {/* Navigation */}
                    <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                        <Link
                            href="/dashboard/admin"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-9 9a1 1 0 001.414 1.414L2 12.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-4.586l.293.293a1 1 0 001.414-1.414l-9-9z" />
                            </svg>
                            Dashboard
                        </Link>

                        <Link
                            href="/admin/members"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                            </svg>
                            Manage Members
                        </Link>

                        <Link
                            href="/admin/applications"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            Member Applications
                        </Link>

                        <Link
                            href="/admin/contributions"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                            </svg>
                            Contributions
                        </Link>

                        <Link
                            href="/admin/loans"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            Loans
                        </Link>

                        <Link
                            href="/admin/equipment-requests"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                            </svg>
                            Equipment Requests
                        </Link>

                        <Link
                            href="/admin/equipment"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                            </svg>
                            Equipment Management
                        </Link>

                        <Link
                            href="/admin/documents"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                            </svg>
                            Document Management
                        </Link>

                        <Link
                            href="/admin/transport"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                                <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-.293-.707L15 4.586A1 1 0 0014.414 4H14v3z" />
                            </svg>
                            Transport Management
                        </Link>

                        <Link
                            href="/admin/feedback"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                            </svg>
                            Feedback
                        </Link>

                        <Link
                            href="/admin/events"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            Events
                        </Link>

                        <Link
                            href="/admin/meetings"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            Meetings
                        </Link>

                        <Link
                            href="/admin/emails"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Email Notifications
                        </Link>

                        <Link
                            href="/admin/notifications"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                            </svg>
                            Notifications
                        </Link>

                        <Link
                            href="/admin/reports"
                            className="text-gray-700 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-lg"
                        >
                            <svg className="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                            </svg>
                            Reports
                        </Link>
                    </nav>

                    {/* User Profile Section */}
                    <div className="flex-shrink-0 border-t border-gray-200 p-4">
                        <div className="flex items-center">
                            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                                <span className="text-white font-semibold text-sm">
                                    {auth?.user?.name?.charAt(0)?.toUpperCase() || 'A'}
                                </span>
                            </div>
                            <div className="ml-3 flex-1">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                    {auth?.user?.name || 'Administrator'}
                                </p>
                                <p className="text-xs text-gray-500 truncate">
                                    {auth?.user?.email || '<EMAIL>'}
                                </p>
                            </div>
                            <Link
                                href="/logout"
                                method="post"
                                className="ml-2 p-1 text-gray-400 hover:text-gray-500"
                            >
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
                                </svg>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main content */}
            <div className="flex-1 flex flex-col overflow-hidden">
                {/* Mobile header */}
                <div className="lg:hidden bg-white shadow-sm border-b border-gray-200 px-4 py-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center">
                            <button
                                className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500 mr-2"
                                onClick={() => setSidebarOpen(true)}
                                aria-label="Open sidebar"
                            >
                                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold text-sm">S</span>
                            </div>
                            <h1 className="ml-2 text-lg font-bold text-gray-900">Admin Panel</h1>
                        </div>
                        <Link
                            href="/logout"
                            method="post"
                            className="p-2 text-gray-400 hover:text-gray-500"
                            aria-label="Logout"
                        >
                            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
                            </svg>
                        </Link>
                    </div>
                </div>

                <main className="flex-1 overflow-y-auto p-4 sm:px-6 lg:px-8 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
                    <div className="max-w-7xl mx-auto">
                        {children}
                    </div>
                </main>
            </div>
        </div>
    );
}