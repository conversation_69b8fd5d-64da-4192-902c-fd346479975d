<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class InventoryItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'category',
        'sku',
        'quantity',
        'unit',
        'unit_cost',
        'total_value',
        'location',
        'minimum_stock',
        'status',
        'purchase_date',
        'expiry_date',
        'supplier',
        'notes',
        'added_by',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_value' => 'decimal:2',
        'minimum_stock' => 'integer',
        'purchase_date' => 'date',
        'expiry_date' => 'date',
    ];

    /**
     * Get the user who added the item.
     */
    public function addedBy()
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    /**
     * Check if item is low stock.
     */
    public function getIsLowStockAttribute()
    {
        return $this->quantity <= $this->minimum_stock && $this->quantity > 0;
    }

    /**
     * Check if item is out of stock.
     */
    public function getIsOutOfStockAttribute()
    {
        return $this->quantity <= 0;
    }

    /**
     * Check if item is expired.
     */
    public function getIsExpiredAttribute()
    {
        return $this->expiry_date && $this->expiry_date < Carbon::today();
    }

    /**
     * Check if item is expiring soon (within 30 days).
     */
    public function getIsExpiringSoonAttribute()
    {
        return $this->expiry_date && 
               $this->expiry_date >= Carbon::today() && 
               $this->expiry_date <= Carbon::today()->addDays(30);
    }

    /**
     * Get formatted total value.
     */
    public function getFormattedTotalValueAttribute()
    {
        return 'MW ' . number_format($this->total_value, 2);
    }

    /**
     * Get formatted unit cost.
     */
    public function getFormattedUnitCostAttribute()
    {
        return 'MW ' . number_format($this->unit_cost, 2);
    }

    /**
     * Update status based on quantity and expiry.
     */
    public function updateStatus()
    {
        if ($this->is_expired) {
            $this->status = 'out_of_stock';
        } elseif ($this->is_out_of_stock) {
            $this->status = 'out_of_stock';
        } elseif ($this->is_low_stock) {
            $this->status = 'low_stock';
        } else {
            $this->status = 'available';
        }
        
        $this->save();
    }

    /**
     * Calculate total value based on quantity and unit cost.
     */
    public function calculateTotalValue()
    {
        if ($this->unit_cost) {
            $this->total_value = $this->quantity * $this->unit_cost;
            $this->save();
        }
    }
}
