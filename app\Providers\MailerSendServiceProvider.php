<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Mail;
use App\Mail\Transport\MailerSendTransport;
use Symfony\Component\Mailer\Transport\Dsn;

class MailerSendServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Mail::extend('mailersend', function (array $config = []) {
            return new MailerSendTransport(
                $config['api_key'] ?? config('services.mailersend.api_key')
            );
        });
    }
}
