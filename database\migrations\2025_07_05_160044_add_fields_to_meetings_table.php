<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('meetings', function (Blueprint $table) {
            $table->text('description')->nullable()->after('title');
            $table->date('meeting_date')->nullable()->after('agenda');
            $table->enum('meeting_type', ['general', 'board', 'emergency', 'training', 'annual'])->default('general')->after('location');
            $table->enum('status', ['scheduled', 'completed', 'cancelled'])->default('scheduled')->after('meeting_type');
            $table->integer('max_attendees')->nullable()->after('status');
            $table->unsignedBigInteger('created_by')->nullable()->after('max_attendees');

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('meetings', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropColumn(['description', 'meeting_date', 'meeting_type', 'status', 'max_attendees', 'created_by']);
        });
    }
};
