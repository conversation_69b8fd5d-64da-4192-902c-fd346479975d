import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function MeetingDetails({ auth, meeting }) {
    const [showEditModal, setShowEditModal] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm({
        title: meeting?.title || '',
        description: meeting?.description || '',
        meeting_date: meeting?.meeting_date || '',
        meeting_time: meeting?.meeting_time ? meeting.meeting_time.substring(11, 16) : '',
        location: meeting?.location || '',
        meeting_type: meeting?.meeting_type || 'general',
        agenda: meeting?.agenda || '',
        status: meeting?.status || 'scheduled',
    });

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-ZM', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (timeString) => {
        // Handle both datetime strings and time-only strings
        if (timeString.includes(' ')) {
            // It's a full datetime string, extract just the time part
            return new Date(timeString).toLocaleTimeString('en-ZM', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            // It's just a time string
            return new Date(`2000-01-01 ${timeString}`).toLocaleTimeString('en-ZM', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    };

    const getMeetingTypeColor = (type) => {
        switch (type) {
            case 'general': return 'bg-blue-100 text-blue-800';
            case 'board': return 'bg-purple-100 text-purple-800';
            case 'emergency': return 'bg-red-100 text-red-800';
            case 'training': return 'bg-green-100 text-green-800';
            case 'annual': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'scheduled': return 'bg-blue-100 text-blue-800';
            case 'completed': return 'bg-green-100 text-green-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getResponseStatusColor = (status) => {
        switch (status) {
            case 'attending': return 'bg-green-100 text-green-800';
            case 'not_attending': return 'bg-red-100 text-red-800';
            case 'maybe': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const handleUpdate = (e) => {
        e.preventDefault();
        put(`/admin/meetings/${meeting.id}`, {
            onSuccess: () => {
                setShowEditModal(false);
                reset();
            }
        });
    };

    if (!meeting) {
        return (
            <AdminLayout auth={auth}>
                <Head title="Meeting Not Found - Admin" />
                <div className="min-h-screen bg-gray-50 p-6">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center py-12">
                            <h1 className="text-2xl font-bold text-gray-900 mb-4">Meeting Not Found</h1>
                            <p className="text-gray-600 mb-6">The meeting you're looking for doesn't exist.</p>
                            <Link
                                href="/admin/meetings"
                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                            >
                                ← Back to Meetings
                            </Link>
                        </div>
                    </div>
                </div>
            </AdminLayout>
        );
    }

    return (
        <AdminLayout auth={auth}>
            <Head title={`${meeting.title} - Meeting Details`} />
            
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-6xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">{meeting.title}</h1>
                                <p className="mt-2 text-gray-600">Meeting details and attendance management</p>
                            </div>
                            <div className="flex space-x-4">
                                <Link
                                    href="/admin/meetings"
                                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    ← Back to Meetings
                                </Link>
                                <button
                                    onClick={() => setShowEditModal(true)}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                                >
                                    Edit Meeting
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Meeting Information */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                        <div className="lg:col-span-2">
                            <div className="bg-white rounded-xl shadow-lg p-6">
                                <h2 className="text-xl font-bold text-gray-900 mb-6">Meeting Information</h2>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-700 mb-2">Meeting Type</h3>
                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getMeetingTypeColor(meeting.meeting_type)}`}>
                                            {meeting.meeting_type}
                                        </span>
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium text-gray-700 mb-2">Status</h3>
                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(meeting.status)}`}>
                                            {meeting.status}
                                        </span>
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium text-gray-700 mb-2">Date</h3>
                                        <p className="text-gray-900">{formatDate(meeting.meeting_date)}</p>
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium text-gray-700 mb-2">Time</h3>
                                        <p className="text-gray-900">{formatTime(meeting.meeting_time)}</p>
                                    </div>

                                    <div className="md:col-span-2">
                                        <h3 className="text-sm font-medium text-gray-700 mb-2">Location</h3>
                                        <p className="text-gray-900">{meeting.location}</p>
                                    </div>
                                </div>

                                <div className="mt-6">
                                    <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
                                    <p className="text-gray-900">{meeting.description}</p>
                                </div>

                                {meeting.agenda && (
                                    <div className="mt-6">
                                        <h3 className="text-sm font-medium text-gray-700 mb-2">Agenda</h3>
                                        <div className="bg-gray-50 rounded-lg p-4">
                                            <pre className="text-sm text-gray-900 whitespace-pre-wrap">{meeting.agenda}</pre>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div>
                            <div className="bg-white rounded-xl shadow-lg p-6">
                                <h2 className="text-xl font-bold text-gray-900 mb-6">Attendance Summary</h2>
                                
                                <div className="space-y-4">
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600">Total Invited:</span>
                                        <span className="font-semibold text-gray-900">{meeting.attendances?.length || 0}</span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600">Attending:</span>
                                        <span className="font-semibold text-green-600">
                                            {meeting.attendances?.filter(a => a.response_status === 'attending').length || 0}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600">Not Attending:</span>
                                        <span className="font-semibold text-red-600">
                                            {meeting.attendances?.filter(a => a.response_status === 'not_attending').length || 0}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600">Maybe:</span>
                                        <span className="font-semibold text-yellow-600">
                                            {meeting.attendances?.filter(a => a.response_status === 'maybe').length || 0}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600">No Response:</span>
                                        <span className="font-semibold text-gray-600">
                                            {meeting.attendances?.filter(a => a.response_status === 'pending').length || 0}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Attendance List */}
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">
                                Member Attendance ({meeting.attendances?.length || 0})
                            </h3>
                        </div>
                        
                        {meeting.attendances && meeting.attendances.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Member
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Response Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Attended
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Comments
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {meeting.attendances.map((attendance) => (
                                            <tr key={attendance.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {attendance.farmer_name}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getResponseStatusColor(attendance.response_status)}`}>
                                                        {attendance.response_status}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                        attendance.attended 
                                                            ? 'bg-green-100 text-green-800' 
                                                            : 'bg-gray-100 text-gray-800'
                                                    }`}>
                                                        {attendance.attended ? 'Yes' : 'No'}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4">
                                                    <div className="text-sm text-gray-900">
                                                        {attendance.comments || '-'}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <p className="text-gray-500">No attendance records found.</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Edit Meeting Modal */}
            {showEditModal && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Edit Meeting
                            </h3>

                            <form onSubmit={handleUpdate} className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Meeting Title <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            value={data.title}
                                            onChange={(e) => setData('title', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        />
                                        {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Status <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            value={data.status}
                                            onChange={(e) => setData('status', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        >
                                            <option value="scheduled">Scheduled</option>
                                            <option value="completed">Completed</option>
                                            <option value="cancelled">Cancelled</option>
                                        </select>
                                        {errors.status && <p className="mt-1 text-sm text-red-600">{errors.status}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Meeting Date <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="date"
                                            value={data.meeting_date}
                                            onChange={(e) => setData('meeting_date', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        />
                                        {errors.meeting_date && <p className="mt-1 text-sm text-red-600">{errors.meeting_date}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Meeting Time <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="time"
                                            value={data.meeting_time}
                                            onChange={(e) => setData('meeting_time', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            required
                                        />
                                        {errors.meeting_time && <p className="mt-1 text-sm text-red-600">{errors.meeting_time}</p>}
                                    </div>

                                    <div className="md:col-span-2">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Location <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            value={data.location}
                                            onChange={(e) => setData('location', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            placeholder="Meeting venue or address"
                                            required
                                        />
                                        {errors.location && <p className="mt-1 text-sm text-red-600">{errors.location}</p>}
                                    </div>

                                    <div className="md:col-span-2">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Description <span className="text-red-500">*</span>
                                        </label>
                                        <textarea
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            placeholder="Brief description of the meeting purpose"
                                            required
                                        />
                                        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                                    </div>
                                </div>

                                <div className="flex space-x-3 pt-4">
                                    <button
                                        type="submit"
                                        disabled={processing}
                                        className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                                    >
                                        {processing ? 'Updating...' : 'Update Meeting'}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowEditModal(false);
                                            reset();
                                        }}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}
        </AdminLayout>
    );
}
