<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Transport Report - <?php echo e($farmer->user->name ?? 'Member'); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 24px;
        }
        
        .header h2 {
            color: #666;
            margin: 5px 0;
            font-size: 16px;
            font-weight: normal;
        }
        
        .member-info {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .member-info h3 {
            margin: 0 0 10px 0;
            color: #2563eb;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .info-item {
            margin-bottom: 5px;
        }
        
        .info-label {
            font-weight: bold;
            color: #374151;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background-color: #f0f9ff;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #bae6fd;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #0369a1;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 11px;
        }
        
        .deliveries-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .deliveries-table th,
        .deliveries-table td {
            border: 1px solid #d1d5db;
            padding: 8px;
            text-align: left;
        }
        
        .deliveries-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }
        
        .deliveries-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        
        .currency {
            text-align: right;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #d1d5db;
            text-align: center;
            color: #6b7280;
            font-size: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Siyamphanje Cooperative</h1>
        <h2>Transport Report - <?php echo e($year); ?></h2>
        <p>Generated on <?php echo e($generatedAt); ?></p>
    </div>

    <div class="member-info">
        <h3>Member Information</h3>
        <div class="info-grid">
            <div>
                <div class="info-item">
                    <span class="info-label">Name:</span> <?php echo e($user->name ?? 'N/A'); ?>

                </div>
                <div class="info-item">
                    <span class="info-label">Member ID:</span> <?php echo e($farmer->id ?? 'N/A'); ?>

                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span> <?php echo e($user->email ?? 'N/A'); ?>

                </div>
            </div>
            <div>
                <div class="info-item">
                    <span class="info-label">Phone:</span> <?php echo e($user->phone ?? 'N/A'); ?>

                </div>
                <div class="info-item">
                    <span class="info-label">Farm Size:</span> <?php echo e($farmer->farm_size ?? 'N/A'); ?> hectares
                </div>
                <div class="info-item">
                    <span class="info-label">Report Period:</span> <?php echo e($year); ?>

                </div>
            </div>
        </div>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value"><?php echo e($stats['totalDeliveries']); ?></div>
            <div class="stat-label">Total Deliveries</div>
        </div>
        <div class="stat-card">
            <div class="stat-value"><?php echo e(number_format($stats['totalTonsDelivered'], 2)); ?></div>
            <div class="stat-label">Total Tons Delivered</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">MWK <?php echo e(number_format($stats['totalTransportCosts'], 0)); ?></div>
            <div class="stat-label">Total Transport Costs</div>
        </div>
        <div class="stat-card">
            <div class="stat-value"><?php echo e(number_format($stats['totalDistanceTraveled'], 1)); ?> km</div>
            <div class="stat-label">Total Distance</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">MWK <?php echo e(number_format($stats['averageCostPerTon'], 0)); ?></div>
            <div class="stat-label">Average Cost per Ton</div>
        </div>
        <div class="stat-card">
            <div class="stat-value"><?php echo e($deliveries->where('status', 'completed')->count()); ?></div>
            <div class="stat-label">Completed Deliveries</div>
        </div>
    </div>

    <?php if($deliveries->count() > 0): ?>
        <h3 style="color: #2563eb; margin-bottom: 15px;">Delivery Details</h3>
        <table class="deliveries-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Vehicle</th>
                    <th>Weight (Tons)</th>
                    <th>Distance (km)</th>
                    <th>Rate/Ton</th>
                    <th>Transport Cost</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $deliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($delivery->delivery_date->format('Y-m-d')); ?></td>
                    <td><?php echo e($delivery->vehicle->registration_number ?? 'N/A'); ?></td>
                    <td><?php echo e(number_format($delivery->sugarcane_weight_tons, 2)); ?></td>
                    <td><?php echo e(number_format($delivery->distance_km, 1)); ?></td>
                    <td class="currency">MWK <?php echo e(number_format($delivery->transport_rate_per_ton, 0)); ?></td>
                    <td class="currency">MWK <?php echo e(number_format($delivery->total_transport_cost, 0)); ?></td>
                    <td style="text-transform: capitalize;"><?php echo e($delivery->status); ?></td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
            <tfoot>
                <tr style="background-color: #e5e7eb; font-weight: bold;">
                    <td colspan="2">TOTALS</td>
                    <td><?php echo e(number_format($stats['totalTonsDelivered'], 2)); ?></td>
                    <td><?php echo e(number_format($stats['totalDistanceTraveled'], 1)); ?></td>
                    <td>-</td>
                    <td class="currency">MWK <?php echo e(number_format($stats['totalTransportCosts'], 0)); ?></td>
                    <td>-</td>
                </tr>
            </tfoot>
        </table>
    <?php else: ?>
        <div style="text-align: center; padding: 40px; color: #6b7280;">
            <h3>No Transport Deliveries Found</h3>
            <p>No transport deliveries were recorded for the selected period.</p>
        </div>
    <?php endif; ?>

    <?php if($deliveries->count() > 0): ?>
        <div style="margin-top: 30px; background-color: #f0f9ff; padding: 15px; border-radius: 5px;">
            <h4 style="color: #2563eb; margin: 0 0 10px 0;">Summary</h4>
            <p style="margin: 5px 0;">
                During <?php echo e($year); ?>, you had <strong><?php echo e($stats['totalDeliveries']); ?></strong> transport deliveries 
                totaling <strong><?php echo e(number_format($stats['totalTonsDelivered'], 2)); ?> tons</strong> of sugarcane.
            </p>
            <p style="margin: 5px 0;">
                Total transport costs amounted to <strong>MWK <?php echo e(number_format($stats['totalTransportCosts'], 0)); ?></strong>, 
                with an average cost of <strong>MWK <?php echo e(number_format($stats['averageCostPerTon'], 0)); ?></strong> per ton.
            </p>
            <p style="margin: 5px 0;">
                The cooperative vehicles traveled a total distance of <strong><?php echo e(number_format($stats['totalDistanceTraveled'], 1)); ?> km</strong> 
                for your deliveries.
            </p>
        </div>
    <?php endif; ?>

    <div class="footer">
        <p>This report was generated automatically by the Siyamphanje Cooperative Management System.</p>
        <p>For any questions or concerns, please contact the cooperative administration.</p>
        <p>Report generated on <?php echo e($generatedAt); ?></p>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\my-backend\resources\views/reports/member-transport.blade.php ENDPATH**/ ?>