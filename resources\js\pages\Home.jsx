import React from "react";
import { Head } from "@inertiajs/react";
import { Link, usePage, useForm, router } from "@inertiajs/react";

const Home = () => {
    return (
        <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
            {/* Navigation Bar */}
            <nav className="bg-white/80 backdrop-blur-md sticky top-0 z-50 border-b border-green-100 shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center space-x-3">
                            <span className="text-3xl animate-bounce">🌱</span>
                            <h1 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                                SIYAMPHANJE
                            </h1>
                        </div>

                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => router.get("/members/register")}
                                className="px-6 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-full hover:from-green-700 hover:to-emerald-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
                            >
                                Join Our Cooperative
                            </button>

                            <Link href="/login" className="group">
                                <button className="px-6 py-2 text-green-700 font-medium hover:text-green-800 transition-colors duration-200 relative">
                                    Login
                                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-red-600 group-hover:w-full transition-all duration-300"></span>
                                </button>
                            </Link>
                        </div>
                    </div>
                </div>
            </nav>

            {/* Hero Section */}
            <header className="relative overflow-hidden py-20 lg:py-32">
                <div className="absolute inset-0">
                    <div className="absolute top-20 left-20 w-64 h-64 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
                    <div className="absolute top-40 right-20 w-64 h-64 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-2000"></div>
                    <div className="absolute -bottom-8 left-40 w-64 h-64 bg-lime-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-4000"></div>
                </div>

                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
                    <div className="grid lg:grid-cols-2 gap-12 items-center">
                        <div className="space-y-8">
                            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                                <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                                    Empowering
                                </span>{" "}
                                Farmers Through Collective Strength
                            </h1>
                            <p className="text-xl text-gray-600 leading-relaxed">
                                Join Malawi's fastest growing agricultural
                                cooperative to access better markets, fair
                                prices, and community support.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4">
                                <Link href="/members/register">
                                    <button className="w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-700 hover:to-emerald-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                        Become a Member
                                    </button>
                                </Link>
                                <a href="/about">
                                    <button className="w-full sm:w-auto px-8 py-4 border-2 border-green-600 text-green-600 font-semibold rounded-xl hover:bg-green-50 transition-all duration-200">
                                        Learn More
                                    </button>
                                </a>
                            </div>
                        </div>

                        <div className="relative">
                            <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl transform hover:scale-105 transition-all duration-500 hover:shadow-3xl">
                                <div className="w-full h-96 bg-gradient-to-br from-green-400 to-emerald-500 relative">
                                    {/* Try multiple image sources */}
                                    <img
                                        src="/images/cooperative-farmers.jpg"
                                        alt="Siyamphanje Cooperative Farmers"
                                        className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                                        onError={(e) => {
                                            // Try alternative image sources
                                            const alternatives = [
                                                '/images/phanda.jpeg',
                                                '/images/agriculture-malawi.jpg',
                                                '/images/cooperative-members.jpg',
                                                'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'
                                            ];

                                            const currentSrc = e.target.src;
                                            const currentIndex = alternatives.findIndex(src => currentSrc.includes(src.split('/').pop()));

                                            if (currentIndex < alternatives.length - 1) {
                                                e.target.src = alternatives[currentIndex + 1];
                                            } else {
                                                // All images failed, show fallback
                                                e.target.style.display = 'none';
                                                e.target.nextElementSibling.style.display = 'flex';
                                            }
                                        }}
                                    />
                                    {/* Fallback content */}
                                    <div className="w-full h-full bg-gradient-to-br from-green-400 to-emerald-500 flex items-center justify-center" style={{display: 'none'}}>
                                        <div className="text-center text-white">
                                            <span className="text-8xl block mb-4">👨‍🌾</span>
                                            <p className="text-xl font-semibold">Siyamphanje Cooperative</p>
                                            <p className="text-lg opacity-90">Growing Together</p>
                                        </div>
                                    </div>
                                    {/* Gradient overlay for better text contrast */}
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>

                                    {/* Optional text overlay on image */}
                                    <div className="absolute bottom-4 left-4 text-white">
                                        <h3 className="text-2xl font-bold mb-1">Our Community</h3>
                                        <p className="text-lg opacity-90">Empowering farmers across Malawi</p>
                                    </div>
                                </div>
                            </div>

                            {/* Floating Icons */}
                            <div className="absolute -top-4 -left-4 w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center shadow-lg animate-float">
                                <span className="text-2xl">🌽</span>
                            </div>
                            <div className="absolute top-8 -right-4 w-16 h-16 bg-red-100 rounded-full flex items-center justify-center shadow-lg animate-float animation-delay-1000">
                                <span className="text-2xl">🫘</span>
                            </div>
                            <div className="absolute -bottom-4 right-8 w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center shadow-lg animate-float animation-delay-2000">
                                <span className="text-2xl">☕</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            {/* Stats Bar */}
            <section className="py-16 bg-white/50 backdrop-blur-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
                        <div className="text-center group">
                            <h3 className="text-4xl font-bold text-green-600 group-hover:scale-110 transition-transform duration-200">
                                10+
                            </h3>
                            <p className="text-gray-600 font-medium">
                                Member Farmers
                            </p>
                        </div>
                        <div className="text-center group">
                            <h3 className="text-4xl font-bold text-emerald-600 group-hover:scale-110 transition-transform duration-200">
                                K 2.5B+
                            </h3>
                            <p className="text-gray-600 font-medium">
                                Annual Sales
                            </p>
                        </div>
                        <div className="text-center group">
                            <h3 className="text-4xl font-bold text-lime-600 group-hover:scale-110 transition-transform duration-200">
                                10+
                            </h3>
                            <p className="text-gray-600 font-medium">
                                Crop Types
                            </p>
                        </div>
                        <div className="text-center group">
                            <h3 className="text-4xl font-bold text-teal-600 group-hover:scale-110 transition-transform duration-200">
                                10+
                            </h3>
                            <p className="text-gray-600 font-medium">
                                Market Partners
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl font-bold text-gray-900 mb-4">
                            Why Join Siyamphanje?
                        </h2>
                        <div className="w-24 h-1 bg-gradient-to-r from-green-600 to-emerald-600 mx-auto rounded-full"></div>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                        <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-green-50">
                            <div className="text-5xl mb-4 group-hover:scale-110 transition-transform duration-200">
                                💰
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-3">
                                Better Prices
                            </h3>
                            <p className="text-gray-600">
                                Get 30-50% higher prices through our collective
                                bargaining power
                            </p>
                        </div>

                        <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-green-50">
                            <div className="text-5xl mb-4 group-hover:scale-110 transition-transform duration-200">
                                🛡️
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-3">
                                Risk Protection
                            </h3>
                            <p className="text-gray-600">
                                Access crop insurance and emergency funds when
                                needed
                            </p>
                        </div>

                        <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-green-50">
                            <div className="text-5xl mb-4 group-hover:scale-110 transition-transform duration-200">
                                📚
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-3">
                                Training
                            </h3>
                            <p className="text-gray-600">
                                Free workshops on modern farming techniques and
                                sustainability
                            </p>
                        </div>

                        <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-green-50">
                            <div className="text-5xl mb-4 group-hover:scale-110 transition-transform duration-200">
                                🚜
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-3">
                                Equipment Sharing
                            </h3>
                            <p className="text-gray-600">
                                Access to shared farming tools and machinery
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Testimonials */}
            <section className="py-20 bg-gradient-to-r from-green-50 to-emerald-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl font-bold text-gray-900 mb-4">
                            What Our Members Say
                        </h2>
                        <div className="w-24 h-1 bg-gradient-to-r from-green-600 to-emerald-600 mx-auto rounded-full"></div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-8">
                        <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100">
                            <div className="flex mb-4">
                                {[...Array(5)].map((_, i) => (
                                    <span
                                        key={i}
                                        className="text-yellow-400 text-xl"
                                    >
                                        ⭐
                                    </span>
                                ))}
                            </div>
                            <p className="text-gray-700 mb-6 text-lg leading-relaxed italic">
                                "Since joining Siyamphanje, my income has
                                doubled. The cooperative helped me get fair
                                prices for my maize."
                            </p>
                            <div className="flex items-center">
                                <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                    JK
                                </div>
                                <div className="ml-4">
                                    <h4 className="font-bold text-gray-900">
                                        John Kato
                                    </h4>
                                    <p className="text-gray-600">
                                        Maize Farmer, 3 years member
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100">
                            <div className="flex mb-4">
                                {[...Array(5)].map((_, i) => (
                                    <span
                                        key={i}
                                        className="text-yellow-400 text-xl"
                                    >
                                        ⭐
                                    </span>
                                ))}
                            </div>
                            <p className="text-gray-700 mb-6 text-lg leading-relaxed italic">
                                "The training programs transformed how I farm.
                                My coffee yield increased by 70% using their
                                techniques."
                            </p>
                            <div className="flex items-center">
                                <div className="w-12 h-12 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                    SN
                                </div>
                                <div className="ml-4">
                                    <h4 className="font-bold text-gray-900">
                                        Sarah Nalwoga
                                    </h4>
                                    <p className="text-gray-600">
                                        Coffee Farmer, 2 years member
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Call to Action */}
            <section className="py-20 bg-gradient-to-r from-green-600 to-emerald-600 relative overflow-hidden">
                <div className="absolute inset-0">
                    <div className="absolute top-0 left-0 w-full h-full bg-black/10"></div>
                    <div className="absolute top-20 right-20 w-32 h-32 bg-white/10 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-20 left-20 w-24 h-24 bg-white/10 rounded-full animate-pulse animation-delay-1000"></div>
                </div>

                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
                    <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
                        Ready to Grow With Us?
                    </h2>
                    <p className="text-xl text-green-100 mb-8">
                        Join hundreds of farmers benefiting from collective
                        farming today
                    </p>
                    <Link href="/members/register">
                        <button className="px-12 py-4 bg-white text-green-600 font-bold text-lg rounded-xl hover:bg-green-50 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                            Register Now
                        </button>
                    </Link>
                </div>
            </section>

            {/* Footer */}
            <footer className="bg-gray-900 text-white py-16">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid lg:grid-cols-4 gap-8">
                        <div className="lg:col-span-1">
                            <div className="flex items-center space-x-3 mb-4">
                                <span className="text-3xl">🌱</span>
                                <div>
                                    <h3 className="text-2xl font-bold">
                                        SIYAMPANJE
                                    </h3>
                                    <p className="text-gray-400">
                                        Agricultural Cooperative
                                    </p>
                                </div>
                            </div>
                            <p className="text-gray-400 leading-relaxed">
                                Empowering farmers through collective strength
                                and sustainable agriculture.
                            </p>
                        </div>

                        <div>
                            <h4 className="text-lg font-bold mb-4 text-green-400">
                                About
                            </h4>
                            <div className="space-y-3">
                                <a
                                    href="/about"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    Our Story
                                </a>
                                <a
                                    href="/team"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    Leadership
                                </a>
                                <a
                                    href="/values"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    Values
                                </a>
                            </div>
                        </div>

                        <div>
                            <h4 className="text-lg font-bold mb-4 text-green-400">
                                Members
                            </h4>
                            <div className="space-y-3">
                                <a
                                    href="/benefits"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    Benefits
                                </a>
                                <a
                                    href="/requirements"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    Requirements
                                </a>
                                <a
                                    href="/faq"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    FAQ
                                </a>
                            </div>
                        </div>

                        <div>
                            <h4 className="text-lg font-bold mb-4 text-green-400">
                                Connect
                            </h4>
                            <div className="space-y-3">
                                <a
                                    href="/contact"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    Contact
                                </a>
                                <a
                                    href="https://facebook.com"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    Facebook
                                </a>
                                <a
                                    href="https://whatsapp.com"
                                    className="block text-gray-400 hover:text-white transition-colors duration-200"
                                >
                                    WhatsApp
                                </a>
                            </div>
                        </div>
                    </div>

                    <div className="border-t border-gray-800 mt-12 pt-8 text-center">
                        <p className="text-gray-400">
                            © {new Date().getFullYear()} Siyampanje Agricultural
                            Cooperative. All rights reserved.
                        </p>
                    </div>
                </div>
            </footer>

            <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-10px) rotate(5deg); }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
        .animation-delay-1000 {
          animation-delay: 1s;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
        </div>
    );
};

export default Home;
