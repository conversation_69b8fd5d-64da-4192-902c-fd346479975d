<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\InventoryItem;

class InventoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the inventory.
     */
    public function index(Request $request)
    {
        $search = $request->get('search');
        $category = $request->get('category');
        $status = $request->get('status');
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        $query = InventoryItem::with('addedBy');

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('supplier', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($category && $category !== 'all') {
            $query->where('category', $category);
        }

        // Filter by status
        if ($status && $status !== 'all') {
            $query->where('status', $status);
        }

        // Sorting
        $query->orderBy($sortBy, $sortOrder);

        $items = $query->paginate(20)->through(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'description' => $item->description,
                'category' => $item->category,
                'sku' => $item->sku,
                'quantity' => $item->quantity,
                'unit' => $item->unit,
                'unit_cost' => $item->unit_cost,
                'total_value' => $item->total_value,
                'formatted_unit_cost' => $item->formatted_unit_cost,
                'formatted_total_value' => $item->formatted_total_value,
                'location' => $item->location,
                'minimum_stock' => $item->minimum_stock,
                'status' => $item->status,
                'purchase_date' => $item->purchase_date,
                'expiry_date' => $item->expiry_date,
                'supplier' => $item->supplier,
                'added_by' => $item->addedBy->name ?? 'Unknown',
                'is_low_stock' => $item->is_low_stock,
                'is_out_of_stock' => $item->is_out_of_stock,
                'is_expired' => $item->is_expired,
                'is_expiring_soon' => $item->is_expiring_soon,
                'created_at' => $item->created_at,
            ];
        });

        $stats = [
            'total_items' => InventoryItem::count(),
            'total_value' => InventoryItem::sum('total_value'),
            'low_stock_items' => InventoryItem::whereColumn('quantity', '<=', 'minimum_stock')
                ->where('quantity', '>', 0)->count(),
            'out_of_stock_items' => InventoryItem::where('quantity', '<=', 0)->count(),
            'expiring_soon' => InventoryItem::where('expiry_date', '>=', now())
                ->where('expiry_date', '<=', now()->addDays(30))->count(),
        ];

        // Category breakdown
        $categoryStats = InventoryItem::selectRaw('category, COUNT(*) as count, SUM(total_value) as value')
            ->groupBy('category')
            ->get()
            ->keyBy('category');

        return Inertia::render('Inventory/Index', [
            'items' => $items,
            'stats' => $stats,
            'categoryStats' => $categoryStats,
            'filters' => [
                'search' => $search,
                'category' => $category,
                'status' => $status,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
            ],
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Store a new inventory item (Admin only).
     */
    public function store(Request $request)
    {
        if (auth()->user()->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|in:equipment,supplies,seeds,fertilizer,tools,other',
            'sku' => 'nullable|string|unique:inventory_items,sku',
            'quantity' => 'required|integer|min:0',
            'unit' => 'required|string|max:50',
            'unit_cost' => 'nullable|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'minimum_stock' => 'required|integer|min:0',
            'purchase_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:purchase_date',
            'supplier' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        $item = InventoryItem::create([
            'name' => $request->name,
            'description' => $request->description,
            'category' => $request->category,
            'sku' => $request->sku,
            'quantity' => $request->quantity,
            'unit' => $request->unit,
            'unit_cost' => $request->unit_cost,
            'location' => $request->location,
            'minimum_stock' => $request->minimum_stock,
            'purchase_date' => $request->purchase_date,
            'expiry_date' => $request->expiry_date,
            'supplier' => $request->supplier,
            'notes' => $request->notes,
            'added_by' => auth()->id(),
        ]);

        // Calculate total value and update status
        $item->calculateTotalValue();
        $item->updateStatus();

        return redirect()->back()->with('success', 'Inventory item added successfully!');
    }

    /**
     * Update an inventory item (Admin only).
     */
    public function update(Request $request, $id)
    {
        if (auth()->user()->role->name !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $item = InventoryItem::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|in:equipment,supplies,seeds,fertilizer,tools,other',
            'sku' => 'nullable|string|unique:inventory_items,sku,' . $id,
            'quantity' => 'required|integer|min:0',
            'unit' => 'required|string|max:50',
            'unit_cost' => 'nullable|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'minimum_stock' => 'required|integer|min:0',
            'purchase_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:purchase_date',
            'supplier' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        $item->update($request->only([
            'name', 'description', 'category', 'sku', 'quantity', 'unit',
            'unit_cost', 'location', 'minimum_stock', 'purchase_date',
            'expiry_date', 'supplier', 'notes'
        ]));

        // Recalculate total value and update status
        $item->calculateTotalValue();
        $item->updateStatus();

        return redirect()->back()->with('success', 'Inventory item updated successfully!');
    }

    /**
     * Delete an inventory item (Admin only).
     */
    public function destroy($id)
    {
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Unauthorized');
        }

        $item = InventoryItem::findOrFail($id);
        $item->delete();

        return redirect()->back()->with('success', 'Inventory item deleted successfully!');
    }

    /**
     * Get low stock alerts.
     */
    public function lowStockAlerts()
    {
        $lowStockItems = InventoryItem::whereColumn('quantity', '<=', 'minimum_stock')
            ->where('quantity', '>', 0)
            ->orderBy('quantity')
            ->get();

        $outOfStockItems = InventoryItem::where('quantity', '<=', 0)
            ->orderBy('name')
            ->get();

        $expiringSoonItems = InventoryItem::where('expiry_date', '>=', now())
            ->where('expiry_date', '<=', now()->addDays(30))
            ->orderBy('expiry_date')
            ->get();

        return response()->json([
            'low_stock' => $lowStockItems,
            'out_of_stock' => $outOfStockItems,
            'expiring_soon' => $expiringSoonItems,
        ]);
    }
}
