{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@inertiajs/inertia-react": "^0.8.1", "@inertiajs/progress": "^0.2.7", "@inertiajs/react": "^2.0.12", "antd": "^5.26.3", "framer-motion": "^12.17.0", "lucide-react": "^0.513.0", "react": "^18.2.0", "react-dom": "^18.2.0", "ziggy-js": "^2.5.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "axios": "^1.6.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "laravel-vite-plugin": "^1.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^5.0.0"}}