import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';

const MembershipRegistration = () => {
  
  const { data, setData, post, processing, errors, reset } = useForm({
    full_name: '',
    national_id: '',
    phone_number: '',
    email: '',
    land_size: '',
    village_location: '',
    membership_fee_paid: false,
  });



  const handleSubmit = (e) => {
    e.preventDefault();
    post('/members/register', {
      onSuccess: () => {
        reset();
      }
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
      <Head>
        <title>Join Siyamphanje Cooperative | Membership Registration</title>
      </Head>

      {/* Navigation Bar */}
      <nav className="bg-white/80 backdrop-blur-md sticky top-0 z-50 border-b border-green-100 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-3">
              <span className="text-3xl">🌱</span>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                SIYAMPHANJE
              </h1>
            </Link>

            <div className="flex items-center space-x-4">
              <Link href="/" className="text-green-700 hover:text-green-800 font-medium transition-colors">
                Back to Home
              </Link>
              <Link href="/login" className="px-4 py-2 text-green-700 font-medium hover:text-green-800 transition-colors">
                Login
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Registration Form */}
      <div className="py-12">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-8 py-6">
              <h2 className="text-3xl font-bold text-white text-center">
                Join Our Cooperative
              </h2>
              <p className="text-green-100 text-center mt-2">
                Complete the form below to become a member of Siyamphanje Agricultural Cooperative
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="px-8 py-8 space-y-6">
              {/* Full Name */}
              <div>
                <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="full_name"
                  value={data.full_name}
                  onChange={(e) => setData('full_name', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  placeholder="Enter your full name"
                  required
                />
                {errors.full_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.full_name}</p>
                )}
              </div>

              {/* National ID */}
              <div>
                <label htmlFor="national_id" className="block text-sm font-medium text-gray-700 mb-2">
                  National ID Number *
                </label>
                <input
                  type="text"
                  id="national_id"
                  value={data.national_id}
                  onChange={(e) => setData('national_id', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  placeholder="Enter your national ID number"
                  required
                />
                {errors.national_id && (
                  <p className="mt-1 text-sm text-red-600">{errors.national_id}</p>
                )}
              </div>

              {/* Phone Number */}
              <div>
                <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  id="phone_number"
                  value={data.phone_number}
                  onChange={(e) => setData('phone_number', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  placeholder="e.g., +265 991 234 567"
                  required
                />
                {errors.phone_number && (
                  <p className="mt-1 text-sm text-red-600">{errors.phone_number}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  value={data.email}
                  onChange={(e) => setData('email', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  placeholder="Enter your email address"
                  required
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              {/* Password Info (Hidden field) */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <span className="text-2xl">🔐</span>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      Login Credentials
                    </h3>
                    <p className="text-sm text-blue-700 mt-1">
                      Your login password will be automatically generated and sent to your email address after your membership is approved.
                    </p>
                  </div>
                </div>
              </div>

              {/* Land Size */}
              <div>
                <label htmlFor="land_size" className="block text-sm font-medium text-gray-700 mb-2">
                  Land Size (Hectares) *
                </label>
                <input
                  type="number"
                  id="land_size"
                  step="0.1"
                  min="0.5"
                  value={data.land_size}
                  onChange={(e) => setData('land_size', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  placeholder="e.g., 2.5"
                  required
                />
                {errors.land_size && (
                  <p className="mt-1 text-sm text-red-600">{errors.land_size}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Minimum land size required: 0.5 hectares
                </p>
              </div>

              {/* Village/Location */}
              <div>
                <label htmlFor="village_location" className="block text-sm font-medium text-gray-700 mb-2">
                  Village / Location *
                </label>
                <input
                  type="text"
                  id="village_location"
                  value={data.village_location}
                  onChange={(e) => setData('village_location', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  placeholder="Enter your village or location"
                  required
                />
                {errors.village_location && (
                  <p className="mt-1 text-sm text-red-600">{errors.village_location}</p>
                )}
              </div>

              {/* Membership Fee Paid */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="membership_fee_paid"
                    checked={data.membership_fee_paid}
                    onChange={(e) => setData('membership_fee_paid', e.target.checked)}
                    className="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    required
                  />
                  <div className="ml-3">
                    <label htmlFor="membership_fee_paid" className="text-sm font-medium text-gray-700">
                      I confirm that I have paid the membership fee *
                    </label>
                    <p className="text-xs text-gray-600 mt-1">
                      Please ensure you have paid the required membership fee before submitting this application.
                      Contact our office for payment details if needed.
                    </p>
                  </div>
                </div>
                {errors.membership_fee_paid && (
                  <p className="mt-2 text-sm text-red-600">{errors.membership_fee_paid}</p>
                )}
              </div>

              {/* Submit Button */}
              <div className="pt-6">
                <button
                  type="submit"
                  disabled={processing}
                  className="w-full px-6 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-lg hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {processing ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing Application...
                    </div>
                  ) : (
                    'Submit Membership Application'
                  )}
                </button>
              </div>

              {/* Info Text */}
              <div className="text-center text-sm text-gray-600 pt-4">
                <p>
                  By submitting this form, you agree to our terms and conditions.
                  Your application will be reviewed and you'll receive an email with your login credentials upon approval.
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MembershipRegistration;
