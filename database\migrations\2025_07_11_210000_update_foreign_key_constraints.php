<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update farmers table foreign key constraint
        Schema::table('farmers', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // Update equipment_requests table foreign key constraint
        if (Schema::hasTable('equipment_requests')) {
            Schema::table('equipment_requests', function (Blueprint $table) {
                if (Schema::hasColumn('equipment_requests', 'user_id')) {
                    $table->dropForeign(['user_id']);
                    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                }
            });
        }

        // Update contributions table foreign key constraint
        Schema::table('contributions', function (Blueprint $table) {
            if (Schema::hasColumn('contributions', 'farmer_id')) {
                $table->dropForeign(['farmer_id']);
                $table->foreign('farmer_id')->references('id')->on('farmers')->onDelete('cascade');
            }
        });

        // Update loans table foreign key constraint
        Schema::table('loans', function (Blueprint $table) {
            $table->dropForeign(['farmer_id']);
            $table->foreign('farmer_id')->references('id')->on('farmers')->onDelete('cascade');
        });

        // Update savings_accounts table foreign key constraint
        Schema::table('savings_accounts', function (Blueprint $table) {
            $table->dropForeign(['farmer_id']);
            $table->foreign('farmer_id')->references('id')->on('farmers')->onDelete('cascade');
        });

        // Update meeting_attendances table foreign key constraint
        Schema::table('meeting_attendances', function (Blueprint $table) {
            $table->dropForeign(['farmer_id']);
            $table->foreign('farmer_id')->references('id')->on('farmers')->onDelete('cascade');
        });

        // Update transport_deliveries table foreign key constraint if it exists
        if (Schema::hasTable('transport_deliveries')) {
            Schema::table('transport_deliveries', function (Blueprint $table) {
                if (Schema::hasColumn('transport_deliveries', 'farmer_id')) {
                    $table->dropForeign(['farmer_id']);
                    $table->foreign('farmer_id')->references('id')->on('farmers')->onDelete('cascade');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert farmers table foreign key constraint
        Schema::table('farmers', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->foreign('user_id')->references('id')->on('users');
        });

        // Revert equipment_requests table foreign key constraint
        if (Schema::hasTable('equipment_requests')) {
            Schema::table('equipment_requests', function (Blueprint $table) {
                if (Schema::hasColumn('equipment_requests', 'user_id')) {
                    $table->dropForeign(['user_id']);
                    $table->foreign('user_id')->references('id')->on('users');
                }
            });
        }

        // Revert contributions table foreign key constraint
        Schema::table('contributions', function (Blueprint $table) {
            if (Schema::hasColumn('contributions', 'farmer_id')) {
                $table->dropForeign(['farmer_id']);
                $table->foreign('farmer_id')->references('id')->on('farmers');
            }
        });

        // Revert loans table foreign key constraint
        Schema::table('loans', function (Blueprint $table) {
            $table->dropForeign(['farmer_id']);
            $table->foreign('farmer_id')->references('id')->on('farmers');
        });

        // Revert savings_accounts table foreign key constraint
        Schema::table('savings_accounts', function (Blueprint $table) {
            $table->dropForeign(['farmer_id']);
            $table->foreign('farmer_id')->references('id')->on('farmers');
        });

        // Revert meeting_attendances table foreign key constraint
        Schema::table('meeting_attendances', function (Blueprint $table) {
            $table->dropForeign(['farmer_id']);
            $table->foreign('farmer_id')->references('id')->on('farmers');
        });

        // Revert transport_deliveries table foreign key constraint if it exists
        if (Schema::hasTable('transport_deliveries')) {
            Schema::table('transport_deliveries', function (Blueprint $table) {
                if (Schema::hasColumn('transport_deliveries', 'farmer_id')) {
                    $table->dropForeign(['farmer_id']);
                    $table->foreign('farmer_id')->references('id')->on('farmers');
                }
            });
        }
    }
};
