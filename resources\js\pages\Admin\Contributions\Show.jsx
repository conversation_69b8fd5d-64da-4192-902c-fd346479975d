import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function ShowContribution({ auth, contribution }) {
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-ZM', {
            style: 'currency',
            currency: 'MW',
            minimumFractionDigits: 2,
        }).format(amount);
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-ZM', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
            confirmed: { bg: 'bg-green-100', text: 'text-green-800', label: 'Confirmed' },
            rejected: { bg: 'bg-red-100', text: 'text-red-800', label: 'Rejected' },
        };

        const config = statusConfig[status] || statusConfig.pending;
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
                {config.label}
            </span>
        );
    };

    const getTypeBadge = (type) => {
        const typeConfig = {
            monthly: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Monthly' },
            special: { bg: 'bg-purple-100', text: 'text-purple-800', label: 'Special' },
            emergency: { bg: 'bg-red-100', text: 'text-red-800', label: 'Emergency' },
            development: { bg: 'bg-green-100', text: 'text-green-800', label: 'Development' },
        };

        const config = typeConfig[type] || typeConfig.monthly;
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
                {config.label}
            </span>
        );
    };

    const getPaymentMethodBadge = (method) => {
        const methodConfig = {
            cash: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Cash' },
            bank_transfer: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Bank Transfer' },
            mobile_money: { bg: 'bg-green-100', text: 'text-green-800', label: 'Mobile Money' },
        };

        const config = methodConfig[method] || methodConfig.cash;
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
                {config.label}
            </span>
        );
    };

    return (
        <AdminLayout auth={auth}>
            <Head title={`Contribution Details`} />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <Link
                                    href="/admin/contributions"
                                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
                                >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                    </svg>
                                    Back to Contributions
                                </Link>
                                <h1 className="text-3xl font-bold text-gray-900">Contribution Details</h1>
                                <p className="mt-2 text-gray-600">View contribution information and details</p>
                            </div>
                            <div className="flex items-center space-x-3">
                                {getStatusBadge(contribution.status)}
                                <Link
                                    href={`/admin/contributions/${contribution.id}/edit`}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                                >
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    Edit Contribution
                                </Link>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Main Information */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Contribution Details */}
                            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-medium text-gray-900">Contribution Information</h3>
                                </div>
                                <div className="px-6 py-4">
                                    <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Amount</dt>
                                            <dd className="mt-1 text-lg font-bold text-green-600">{formatCurrency(contribution.amount)}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Type</dt>
                                            <dd className="mt-1">{getTypeBadge(contribution.type)}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Payment Method</dt>
                                            <dd className="mt-1">{getPaymentMethodBadge(contribution.payment_method)}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Reference Number</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{contribution.reference_number || 'N/A'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Contribution Date</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{formatDate(contribution.contribution_date)}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Status</dt>
                                            <dd className="mt-1">{getStatusBadge(contribution.status)}</dd>
                                        </div>
                                        {contribution.description && (
                                            <div className="sm:col-span-2">
                                                <dt className="text-sm font-medium text-gray-500">Description</dt>
                                                <dd className="mt-1 text-sm text-gray-900">{contribution.description}</dd>
                                            </div>
                                        )}
                                    </dl>
                                </div>
                            </div>

                            {/* Member Information */}
                            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-medium text-gray-900">Member Information</h3>
                                </div>
                                <div className="px-6 py-4">
                                    <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Member Name</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{contribution.member?.name}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Email</dt>
                                            <dd className="mt-1 text-sm text-gray-900">{contribution.member?.email}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Member ID</dt>
                                            <dd className="mt-1 text-sm text-gray-900">#{contribution.member?.id}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Farm Size</dt>
                                            <dd className="mt-1 text-sm text-gray-900">
                                                {contribution.member?.farmer?.farm_size ? `${contribution.member.farmer.farm_size} acres` : 'N/A'}
                                            </dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Record Information */}
                            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-medium text-gray-900">Record Information</h3>
                                </div>
                                <div className="px-6 py-4 space-y-4">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Recorded By</dt>
                                        <dd className="mt-1 text-sm text-gray-900">{contribution.recordedBy?.name || 'System'}</dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Created Date</dt>
                                        <dd className="mt-1 text-sm text-gray-900">{formatDate(contribution.created_at)}</dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                                        <dd className="mt-1 text-sm text-gray-900">{formatDate(contribution.updated_at)}</dd>
                                    </div>
                                </div>
                            </div>

                            {/* Quick Actions */}
                            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
                                </div>
                                <div className="px-6 py-4 space-y-3">
                                    <Link
                                        href={`/admin/contributions/${contribution.id}/edit`}
                                        className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                                    >
                                        Edit Contribution
                                    </Link>
                                    <Link
                                        href={`/admin/members/${contribution.member?.id}`}
                                        className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        View Member
                                    </Link>
                                    <Link
                                        href="/admin/contributions/create"
                                        className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        Record New Contribution
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
