/* Home.css */

/* ===== Base Styles ===== */
:root {
  --primary-color: #2a5a3e;       /* Dark green */
  --secondary-color: #4a8c63;     /* Medium green */
  --accent-color: #6bbd99;        /* Light green */
  --highlight-color: #f8c537;     /* Yellow for highlights */
  --text-color: #2d3748;          /* Dark gray */
  --light-text: #718096;          /* Medium gray */
  --background-light: #f8fafc;    /* Light background */
  --background-dark: #1a2e22;     /* Dark background */
  --white: #ffffff;
  --card-bg: #ffffff;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-color);
  background-color: var(--background-light);
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: inherit;
}

.highlight {
  color: var(--primary-color);
  font-weight: 700;
}

/* ===== Navigation ===== */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 5%;
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  font-size: 2rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.nav-buttons {
  display: flex;
  gap: 1rem;
}

.nav-link {
  transition: var(--transition);
}

.login-btn {
  padding: 0.6rem 1.5rem;
  border: 2px solid var(--primary-color);
  border-radius: 5px;
  background: transparent;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.login-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.register-btn {
  padding: 0.6rem 1.5rem;
  border: none;
  border-radius: 5px;
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.register-btn:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

/* ===== Hero Section ===== */
.hero-section {
  padding: 4rem 5% 6rem;
  background: linear-gradient(135deg, rgba(42, 90, 62, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.hero-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  gap: 3rem;
}

.hero-text {
  flex: 1;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: var(--background-dark);
}

.hero-subtitle {
  font-size: 1.2rem;
  color: var(--light-text);
  margin-bottom: 2rem;
  max-width: 80%;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.primary-cta {
  padding: 0.8rem 2rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 4px 6px rgba(42, 90, 62, 0.2);
}

.primary-cta:hover {
  background-color: var(--secondary-color);
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(42, 90, 62, 0.3);
}

.secondary-cta {
  padding: 0.8rem 2rem;
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  border-radius: 5px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
}

.secondary-cta:hover {
  background-color: rgba(42, 90, 62, 0.05);
  transform: translateY(-3px);
}

.hero-image {
  flex: 1;
  position: relative;
}

.hero-main-image {
  width: 100%;
  max-width: 500px;
  border-radius: 10px;
  box-shadow: var(--shadow);
  animation: float 6s ease-in-out infinite;
}

.floating-icons {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.floating-icon {
  position: absolute;
  width: 50px;
  height: 50px;
  animation: float 4s ease-in-out infinite;
}

.floating-icon.maize {
  top: 10%;
  left: -5%;
  animation-delay: 0.5s;
}

.floating-icon.beans {
  top: 70%;
  left: -8%;
  animation-delay: 1s;
}

.floating-icon.coffee {
  top: 40%;
  right: -5%;
  animation-delay: 1.5s;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
  100% { transform: translateY(0px); }
}

/* ===== Stats Bar ===== */
.stats-bar {
  display: flex;
  justify-content: space-around;
  padding: 3rem 5%;
  background-color: var(--primary-color);
  color: white;
  margin-top: -2rem;
  border-radius: 10px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
  box-shadow: var(--shadow);
}

.stat-item {
  text-align: center;
}

.stat-item h3 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-item p {
  font-size: 1rem;
  opacity: 0.9;
}

/* ===== Features Section ===== */
.features-section {
  padding: 6rem 5%;
  background-color: var(--white);
}

.section-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background-color: var(--card-bg);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: var(--shadow);
  transition: var(--transition);
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  display: inline-block;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.feature-card p {
  color: var(--light-text);
}

/* ===== Testimonials ===== */
.testimonials {
  padding: 4rem 5%;
  background-color: var(--background-light);
}

.testimonial-cards {
  display: flex;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  justify-content: center;
  flex-wrap: wrap;
}

.testimonial-card {
  background-color: var(--white);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: var(--shadow);
  max-width: 500px;
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-5px);
}

.testimonial-text {
  font-style: italic;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  color: var(--primary-color);
  margin-bottom: 0.3rem;
}

.author-info p {
  color: var(--light-text);
  font-size: 0.9rem;
}

/* ===== Call to Action ===== */
.cta-section {
  padding: 6rem 5%;
  background: linear-gradient(rgba(42, 90, 62, 0.9), rgba(42, 90, 62, 0.9)), 
              url('/assets/farm-field.jpg') center/cover no-repeat;
  color: white;
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* ===== Footer ===== */
.footer {
  background-color: var(--background-dark);
  color: white;
  padding: 4rem 5% 2rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-logo {
  flex: 1;
  min-width: 250px;
}

.footer-logo h3 {
  font-size: 1.5rem;
  margin: 0.5rem 0;
}

.footer-logo p {
  opacity: 0.8;
}

.footer-links {
  flex: 2;
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
}

.link-group {
  min-width: 150px;
}

.link-group h4 {
  margin-bottom: 1.2rem;
  font-size: 1.1rem;
}

.link-group a {
  display: block;
  margin-bottom: 0.8rem;
  opacity: 0.8;
  transition: var(--transition);
}

.link-group a:hover {
  opacity: 1;
  color: var(--accent-color);
}

.footer-bottom {
  text-align: center;
  padding-top: 3rem;
  margin-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0.7;
  font-size: 0.9rem;
}

/* ===== Responsive Design ===== */
@media (max-width: 992px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
  }
  
  .hero-subtitle {
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  .stats-bar {
    flex-wrap: wrap;
    gap: 2rem;
  }
  
  .stat-item {
    flex: 1 1 40%;
  }
}

@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
  }
  
  .hero-title {
    font-size: 2.2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-cta {
    flex-direction: column;
  }
  
  .stats-bar {
    flex-direction: column;
    gap: 2rem;
    padding: 2rem;
    margin-top: 0;
    border-radius: 0;
  }
  
  .testimonial-cards {
    flex-direction: column;
    align-items: center;
  }
  
  .footer-content {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
  
  .floating-icon {
    width: 40px;
    height: 40px;
  }
}