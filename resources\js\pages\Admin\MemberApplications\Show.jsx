import React, { useState } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function ShowMemberApplication({ auth, application }) {
    const [showApproveModal, setShowApproveModal] = useState(false);
    const [showRejectModal, setShowRejectModal] = useState(false);

    const { data: approveData, setData: setApproveData, post: postApprove, processing: approveProcessing, errors: approveErrors } = useForm({
        password: '',
        password_confirmation: '',
        admin_notes: '',
    });

    const { data: rejectData, setData: setRejectData, post: postReject, processing: rejectProcessing, errors: rejectErrors } = useForm({
        rejection_reason: '',
        admin_notes: '',
    });

    const handleApprove = (e) => {
        e.preventDefault();
        postApprove(`/admin/applications/${application.id}/approve`, {
            onSuccess: () => setShowApproveModal(false),
        });
    };

    const handleReject = (e) => {
        e.preventDefault();
        postReject(`/admin/applications/${application.id}/reject`, {
            onSuccess: () => setShowRejectModal(false),
        });
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-GB', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getStatusBadge = (status) => {
        const statusClasses = {
            pending: 'bg-yellow-100 text-yellow-800',
            approved: 'bg-green-100 text-green-800',
            rejected: 'bg-red-100 text-red-800',
        };

        return (
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusClasses[status] || 'bg-gray-100 text-gray-800'}`}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
        );
    };

    return (
        <AdminLayout auth={auth}>
            <Head title={`Application - ${application.name}`} />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <Link
                                    href="/admin/applications"
                                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
                                >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                    </svg>
                                    Back to Applications
                                </Link>
                                <h1 className="text-3xl font-bold text-gray-900">Member Application</h1>
                                <p className="mt-2 text-gray-600">Review application details and take action</p>
                            </div>
                            <div className="flex items-center space-x-3">
                                {getStatusBadge(application.status)}
                                {application.status === 'pending' && (
                                    <>
                                        <button
                                            onClick={() => setShowApproveModal(true)}
                                            className="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 transition ease-in-out duration-150"
                                        >
                                            Approve
                                        </button>
                                        <button
                                            onClick={() => setShowRejectModal(true)}
                                            className="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 transition ease-in-out duration-150"
                                        >
                                            Reject
                                        </button>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Application Details */}
                    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div className="px-4 py-5 sm:px-6">
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Application Information</h3>
                            <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                Submitted on {formatDate(application.created_at)}
                            </p>
                        </div>
                        <div className="border-t border-gray-200">
                            <dl>
                                {/* Personal Information */}
                                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt className="text-sm font-medium text-gray-500">Personal Information</dt>
                                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <div className="space-y-2">
                                            <div><strong>Name:</strong> {application.name}</div>
                                            <div><strong>Email:</strong> {application.email}</div>
                                            {application.phone && <div><strong>Phone:</strong> {application.phone}</div>}
                                            {application.gender && <div><strong>Gender:</strong> {application.gender}</div>}
                                            {application.identification_number && (
                                                <div><strong>ID Number:</strong> {application.identification_number}</div>
                                            )}
                                        </div>
                                    </dd>
                                </div>

                                {/* Address */}
                                {application.address && (
                                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt className="text-sm font-medium text-gray-500">Address</dt>
                                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            {application.address}
                                        </dd>
                                    </div>
                                )}

                                {/* Farming Details */}
                                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt className="text-sm font-medium text-gray-500">Farming Information</dt>
                                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <div className="space-y-2">
                                            {application.farm_size && <div><strong>Farm Size:</strong> {application.farm_size}</div>}
                                            {application.land_size && <div><strong>Land Size:</strong> {application.land_size} hectares</div>}
                                            {application.location_coordinates && (
                                                <div><strong>Location:</strong> {application.location_coordinates}</div>
                                            )}
                                        </div>
                                    </dd>
                                </div>

                                {/* Banking Details */}
                                {(application.bank_name || application.bank_account_number) && (
                                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt className="text-sm font-medium text-gray-500">Banking Information</dt>
                                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            <div className="space-y-2">
                                                {application.bank_name && <div><strong>Bank:</strong> {application.bank_name}</div>}
                                                {application.bank_account_number && (
                                                    <div><strong>Account Number:</strong> {application.bank_account_number}</div>
                                                )}
                                            </div>
                                        </dd>
                                    </div>
                                )}

                                {/* Review Information */}
                                {application.status !== 'pending' && (
                                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt className="text-sm font-medium text-gray-500">Review Details</dt>
                                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            <div className="space-y-2">
                                                <div><strong>Status:</strong> {getStatusBadge(application.status)}</div>
                                                {application.reviewed_at && (
                                                    <div><strong>Reviewed:</strong> {formatDate(application.reviewed_at)}</div>
                                                )}
                                                {application.reviewed_by && application.reviewed_by_user && (
                                                    <div><strong>Reviewed by:</strong> {application.reviewed_by_user.name}</div>
                                                )}
                                                {application.rejection_reason && (
                                                    <div><strong>Rejection Reason:</strong> {application.rejection_reason}</div>
                                                )}
                                                {application.admin_notes && (
                                                    <div><strong>Admin Notes:</strong> {application.admin_notes}</div>
                                                )}
                                            </div>
                                        </dd>
                                    </div>
                                )}
                            </dl>
                        </div>
                    </div>

                    {/* Approve Modal */}
                    {showApproveModal && (
                        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                            <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
                                <div className="mt-3">
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="text-lg font-bold text-gray-900">Approve Application</h3>
                                        <button
                                            onClick={() => setShowApproveModal(false)}
                                            className="text-gray-400 hover:text-gray-600"
                                        >
                                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>

                                    <form onSubmit={handleApprove} className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Assign Password <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="password"
                                                value={approveData.password}
                                                onChange={(e) => setApproveData('password', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Enter password for new member"
                                                required
                                            />
                                            {approveErrors.password && <p className="mt-1 text-sm text-red-600">{approveErrors.password}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Confirm Password <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="password"
                                                value={approveData.password_confirmation}
                                                onChange={(e) => setApproveData('password_confirmation', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Confirm password"
                                                required
                                            />
                                            {approveErrors.password_confirmation && <p className="mt-1 text-sm text-red-600">{approveErrors.password_confirmation}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Admin Notes
                                            </label>
                                            <textarea
                                                value={approveData.admin_notes}
                                                onChange={(e) => setApproveData('admin_notes', e.target.value)}
                                                rows={3}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Optional notes about the approval..."
                                            />
                                            {approveErrors.admin_notes && <p className="mt-1 text-sm text-red-600">{approveErrors.admin_notes}</p>}
                                        </div>

                                        <div className="flex justify-end space-x-3 pt-4">
                                            <button
                                                type="button"
                                                onClick={() => setShowApproveModal(false)}
                                                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={approveProcessing}
                                                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                                            >
                                                {approveProcessing ? 'Approving...' : 'Approve & Create Account'}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Reject Modal */}
                    {showRejectModal && (
                        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                            <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
                                <div className="mt-3">
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="text-lg font-bold text-gray-900">Reject Application</h3>
                                        <button
                                            onClick={() => setShowRejectModal(false)}
                                            className="text-gray-400 hover:text-gray-600"
                                        >
                                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>

                                    <form onSubmit={handleReject} className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Rejection Reason <span className="text-red-500">*</span>
                                            </label>
                                            <textarea
                                                value={rejectData.rejection_reason}
                                                onChange={(e) => setRejectData('rejection_reason', e.target.value)}
                                                rows={3}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"
                                                placeholder="Explain why this application is being rejected..."
                                                required
                                            />
                                            {rejectErrors.rejection_reason && <p className="mt-1 text-sm text-red-600">{rejectErrors.rejection_reason}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Admin Notes
                                            </label>
                                            <textarea
                                                value={rejectData.admin_notes}
                                                onChange={(e) => setRejectData('admin_notes', e.target.value)}
                                                rows={2}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"
                                                placeholder="Optional internal notes..."
                                            />
                                            {rejectErrors.admin_notes && <p className="mt-1 text-sm text-red-600">{rejectErrors.admin_notes}</p>}
                                        </div>

                                        <div className="flex justify-end space-x-3 pt-4">
                                            <button
                                                type="button"
                                                onClick={() => setShowRejectModal(false)}
                                                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={rejectProcessing}
                                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                                            >
                                                {rejectProcessing ? 'Rejecting...' : 'Reject Application'}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AdminLayout>
    );
}
