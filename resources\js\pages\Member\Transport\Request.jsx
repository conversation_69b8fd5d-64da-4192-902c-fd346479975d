import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import MemberLayout from '@/Layouts/MemberLayout';

const TransportRequest = ({ transportRates, auth }) => {
  const { data, setData, post, processing, errors, reset } = useForm({
    pickup_location: '',
    estimated_tons: '',
    preferred_date: '',
    notes: '',
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    post('/member/transport/request', {
      onSuccess: () => {
        reset();
      }
    });
  };

  const estimatedCost = data.estimated_tons && transportRates.length > 0 
    ? parseFloat(data.estimated_tons) * transportRates[0].rate_per_ton 
    : 0;

  return (
    <MemberLayout user={auth.user} header="Request Transport">
      <Head>
        <title>Request Transport | Siyamphanje Cooperative</title>
      </Head>

      <div className="py-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Request Transport</h1>
                <p className="mt-2 text-gray-600">Submit a request for transport assistance for your upcoming harvest</p>
              </div>
              <Link
                href="/member/transport"
                className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Transport
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Request Form */}
            <div className="lg:col-span-2">
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Transport Request Form</h3>
                  <p className="mt-1 text-sm text-gray-600">
                    Please provide details about your transport needs
                  </p>
                </div>
                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                  <div>
                    <label htmlFor="pickup_location" className="block text-sm font-medium text-gray-700">
                      Pickup Location *
                    </label>
                    <input
                      type="text"
                      id="pickup_location"
                      value={data.pickup_location}
                      onChange={(e) => setData('pickup_location', e.target.value)}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      placeholder="Enter your farm location or nearest landmark"
                      required
                    />
                    {errors.pickup_location && (
                      <p className="mt-1 text-sm text-red-600">{errors.pickup_location}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="estimated_tons" className="block text-sm font-medium text-gray-700">
                      Estimated Weight (Tons) *
                    </label>
                    <input
                      type="number"
                      id="estimated_tons"
                      step="0.1"
                      min="0.1"
                      value={data.estimated_tons}
                      onChange={(e) => setData('estimated_tons', e.target.value)}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      placeholder="0.0"
                      required
                    />
                    {errors.estimated_tons && (
                      <p className="mt-1 text-sm text-red-600">{errors.estimated_tons}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="preferred_date" className="block text-sm font-medium text-gray-700">
                      Preferred Date *
                    </label>
                    <input
                      type="date"
                      id="preferred_date"
                      value={data.preferred_date}
                      onChange={(e) => setData('preferred_date', e.target.value)}
                      min={new Date(Date.now() + 86400000).toISOString().split('T')[0]} // Tomorrow
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      required
                    />
                    {errors.preferred_date && (
                      <p className="mt-1 text-sm text-red-600">{errors.preferred_date}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                      Additional Notes
                    </label>
                    <textarea
                      id="notes"
                      rows={4}
                      value={data.notes}
                      onChange={(e) => setData('notes', e.target.value)}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      placeholder="Any special requirements or additional information..."
                    />
                    {errors.notes && (
                      <p className="mt-1 text-sm text-red-600">{errors.notes}</p>
                    )}
                  </div>

                  <div className="flex items-center justify-end space-x-3">
                    <Link
                      href="/member/transport"
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </Link>
                    <button
                      type="submit"
                      disabled={processing}
                      className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50"
                    >
                      {processing ? 'Submitting...' : 'Submit Request'}
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Estimated Cost */}
              {estimatedCost > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <h4 className="text-lg font-medium text-green-900 mb-2">Estimated Cost</h4>
                  <div className="text-2xl font-bold text-green-700">
                    {formatCurrency(estimatedCost)}
                  </div>
                  <p className="text-sm text-green-600 mt-1">
                    Based on {data.estimated_tons} tons at current rates
                  </p>
                </div>
              )}

              {/* Transport Rates */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h4 className="text-lg font-medium text-gray-900">Current Transport Rates</h4>
                </div>
                <div className="p-6">
                  {transportRates.length > 0 ? (
                    <div className="space-y-4">
                      {transportRates.map((rate) => (
                        <div key={rate.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <h5 className="font-medium text-gray-900">{rate.region_name}</h5>
                              <p className="text-sm text-gray-600">{rate.distance_km} km</p>
                            </div>
                            <div className="text-right">
                              <div className="font-medium text-gray-900">
                                {formatCurrency(rate.rate_per_ton)}
                              </div>
                              <div className="text-sm text-gray-600">per ton</div>
                            </div>
                          </div>
                          {rate.fuel_surcharge > 0 && (
                            <div className="mt-2 text-sm text-gray-600">
                              Fuel surcharge: {formatCurrency(rate.fuel_surcharge)}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No transport rates available.</p>
                  )}
                </div>
              </div>

              {/* Information */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">Important Information</h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <ul className="list-disc list-inside space-y-1">
                        <li>Requests must be submitted at least 24 hours in advance</li>
                        <li>Transport availability depends on vehicle scheduling</li>
                        <li>Final costs may vary based on actual weight and distance</li>
                        <li>An admin will contact you to confirm your request</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MemberLayout>
  );
};

export default TransportRequest;
