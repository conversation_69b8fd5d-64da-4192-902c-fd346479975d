<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\ActivityLog;
use App\models\Farmer;
use App\Models\Role;



class MemberController extends Controller
{
    public function index()
    {
        $members = Farmer::with(['user', 'savingsAccounts', 'loans', 'contributions'])
            ->get()
            ->map(function ($farmer) {
                $totalSavings = $farmer->savingsAccounts->sum('balance');
                $totalLoans = $farmer->loans->sum('amount');
                $totalContributions = $farmer->contributions->sum('amount');
                $activeLoans = $farmer->loans->whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->count();

                // Get equipment requests using user_id instead of requester_id
                $pendingEquipmentRequests = \App\Models\EquipmentRequest::where('user_id', $farmer->user_id)
                    ->where('status', 'pending')
                    ->count();

                return [
                    'id' => $farmer->id,
                    'user_id' => $farmer->user->id,
                    'name' => $farmer->user->name,
                    'email' => $farmer->user->email,
                    'phone' => $farmer->user->phone ?? 'N/A',
                    'gender' => $farmer->gender,
                    'address' => $farmer->address,
                    'land_size' => $farmer->land_size,
                    'identification_number' => $farmer->identification_number,
                    'bank_account_number' => $farmer->bank_account_number,
                    'bank_name' => $farmer->bank_name,
                    'membership_date' => $farmer->membership_date ? $farmer->membership_date->format('Y-m-d') : null,
                    'status' => $farmer->status,
                    'created_at' => $farmer->created_at,
                    'financial_summary' => [
                        'total_savings' => $totalSavings,
                        'total_loans' => $totalLoans,
                        'total_contributions' => $totalContributions,
                        'active_loans' => $activeLoans,
                        'pending_equipment_requests' => $pendingEquipmentRequests,
                    ],
                ];
            });

        $stats = [
            'total_members' => $members->count(),
            'active_members' => $members->where('status', 'active')->count(),
            'inactive_members' => $members->where('status', 'inactive')->count(),
            'total_land_size' => $members->sum('land_size'),
            'average_land_size' => $members->avg('land_size'),
            'total_savings' => $members->sum('financial_summary.total_savings'),
            'total_loans' => $members->sum('financial_summary.total_loans'),
            'total_contributions' => $members->sum('financial_summary.total_contributions'),
        ];

        return Inertia::render('Admin/Members', [
            'members' => $members,
            'stats' => $stats,
            'auth' => [
                'user' => auth()->user()
            ]
        ]);
    }

    public function registerForm()
    {
        return Inertia::render('MemberRegisterForm');
    }

    public function create()
    {
        return Inertia::render('MemberRegisterForm');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20',
            'gender' => 'required|in:male,female,other',
            'land_size' => 'required|numeric|min:0.1',
            'address' => 'required|string',
            'membership_date' => 'required|date',
            'identification_number' => 'required|string|unique:farmers',
        ]);

        DB::transaction(function () use ($request) {
            $memberRole = Role::where('name', 'member')->firstOrFail();

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make('password'), // Temporary password
                'role_id' => $memberRole->id,
            ]);

            $farmer = Farmer::create([
                'user_id' => $user->id,
                'gender' => $request->gender,
                'address' => $request->address,
                'land_size' => $request->land_size,
                'membership_date' => $request->membership_date,
                'identification_number' => $request->identification_number,
                'status' => 'active',
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => $user->id, // Use the newly created user's ID
                'action' => 'create',
                'subject_type' => Farmer::class,
                'subject_id' => $farmer->id,
                'description' => "New farmer registered: {$request->name}",
            ]);
        });

        return redirect()->route('login')
            ->with('success', 'Registration successful! Please log in.');
    }

    public function show($id)
    {
        $farmer = Farmer::with(['user', 'savingsAccounts', 'loans.repayments', 'contributions', 'equipmentRequests.equipment'])
            ->findOrFail($id);

        $financialSummary = [
            'total_savings' => $farmer->savingsAccounts->sum('balance'),
            'total_loans' => $farmer->loans->sum('amount'),
            'total_contributions' => $farmer->contributions->sum('amount'),
            'active_loans' => $farmer->loans->whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->count(),
            'completed_loans' => $farmer->loans->where('status', 'completed')->count(),
            'total_repayments' => $farmer->loans->flatMap->repayments->sum('amount'),
        ];

        // Merge farmer data with user data for the view
        $memberData = [
            'id' => $farmer->id,
            'name' => $farmer->user->name,
            'email' => $farmer->user->email,
            'phone' => $farmer->phone,
            'address' => $farmer->address,
            'land_size' => $farmer->land_size,
            'farm_location' => $farmer->farm_location,
            'crops_grown' => $farmer->crops_grown,
            'farming_experience' => $farmer->farming_experience,
            'status' => $farmer->status,
            'membership_date' => $farmer->membership_date,
            'financial_summary' => $financialSummary
        ];

        return Inertia::render('Admin/Members/Show', [
            'member' => $memberData,
            'auth' => [
                'user' => auth()->user()
            ]
        ]);
    }

    public function edit($id)
    {
        $farmer = Farmer::with('user')->findOrFail($id);

        // Merge farmer data with user data for the edit form
        $memberData = [
            'id' => $farmer->id,
            'name' => $farmer->user->name,
            'email' => $farmer->user->email,
            'phone' => $farmer->phone,
            'address' => $farmer->address,
            'land_size' => $farmer->land_size,
            'farm_location' => $farmer->farm_location,
            'crops_grown' => $farmer->crops_grown,
            'farming_experience' => $farmer->farming_experience,
            'status' => $farmer->status,
        ];

        return Inertia::render('Admin/Members/Edit', [
            'member' => $memberData,
            'auth' => [
                'user' => auth()->user()
            ]
        ]);
    }

    public function update(Request $request, $id)
    {
        $farmer = Farmer::with('user')->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $farmer->user->id,
            'phone' => 'nullable|string|max:20',
            'gender' => 'required|in:male,female,other',
            'land_size' => 'required|numeric|min:0.1',
            'address' => 'required|string',
            'identification_number' => 'required|string|unique:farmers,identification_number,' . $farmer->id,
            'bank_account_number' => 'nullable|string',
            'bank_name' => 'nullable|string',
            'status' => 'required|in:active,inactive,suspended',
        ]);

        DB::transaction(function () use ($request, $farmer) {
            $farmer->user->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            $farmer->update([
                'gender' => $request->gender,
                'address' => $request->address,
                'land_size' => $request->land_size,
                'identification_number' => $request->identification_number,
                'bank_account_number' => $request->bank_account_number,
                'bank_name' => $request->bank_name,
                'status' => $request->status,
            ]);

            ActivityLog::create([
                'user_id' => auth()->id(),
                'action' => 'update',
                'subject_type' => Farmer::class,
                'subject_id' => $farmer->id,
                'description' => "Updated member profile for {$farmer->user->name}",
            ]);
        });

        return redirect()->route('admin.members')->with('success', 'Member updated successfully.');
    }

    public function toggleStatus($id)
    {
        $farmer = Farmer::with('user')->findOrFail($id);

        $newStatus = $farmer->status === 'active' ? 'inactive' : 'active';
        $farmer->update(['status' => $newStatus]);

        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'status_change',
            'subject_type' => Farmer::class,
            'subject_id' => $farmer->id,
            'description' => "Changed member status to {$newStatus} for {$farmer->user->name}",
        ]);

        return redirect()->back()->with('success', "Member status updated to {$newStatus}.");
    }

    public function destroy($id)
    {
        $farmer = Farmer::with('user')->findOrFail($id);

        // Check if member has active loans or savings
        $hasActiveLoans = $farmer->loans()->whereIn('status', ['approved', 'disbursed', 'partial_repayment'])->exists();
        $hasSavings = $farmer->savingsAccounts()->where('balance', '>', 0)->exists();

        if ($hasActiveLoans || $hasSavings) {
            return redirect()->back()->withErrors(['error' => 'Cannot delete member with active loans or savings balance.']);
        }

        DB::transaction(function () use ($farmer) {
            ActivityLog::create([
                'user_id' => auth()->id(),
                'action' => 'delete',
                'subject_type' => Farmer::class,
                'subject_id' => $farmer->id,
                'description' => "Deleted member {$farmer->user->name}",
            ]);

            $farmer->user->delete();
            $farmer->delete();
        });

        return redirect()->back()->with('success', 'Member deleted successfully.');
    }
}