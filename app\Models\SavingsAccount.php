<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SavingsAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'farmer_id',
        'account_number',
        'balance',
        'opening_date',
        'status'
    ];

    public function farmer()
    {
        return $this->belongsTo(Farmer::class);
    }
}
