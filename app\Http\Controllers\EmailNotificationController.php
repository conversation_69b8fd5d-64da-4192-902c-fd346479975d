<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Mail;
use App\Mail\MeetingNotification;
use App\Mail\GeneralAnnouncement;
use App\Models\User;
use App\Models\Meeting;
use Illuminate\Support\Facades\Log;

class EmailNotificationController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display email management dashboard.
     */
    public function index()
    {
        return Inertia::render('Admin/EmailNotifications', [
            'auth' => ['user' => auth()->user()]
        ]);
    }

    /**
     * Send individual email.
     */
    public function sendEmail(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'type' => 'required|string|in:announcement,notification,custom',
        ]);

        try {
            Log::info('Attempting to send individual email', [
                'email' => $validated['email'],
                'subject' => $validated['subject'],
                'name' => $validated['name'],
                'sent_by' => auth()->id(),
            ]);

            Mail::to($validated['email'])->send(new GeneralAnnouncement(
                $validated['name'],
                $validated['subject'],
                $validated['message']
            ));

            Log::info('Individual email sent successfully', [
                'email' => $validated['email'],
                'subject' => $validated['subject'],
                'sent_by' => auth()->id(),
            ]);

            return redirect()->back()->with('success', 'Email sent successfully to ' . $validated['email'] . '!');
        } catch (\Exception $e) {
            Log::error('Failed to send individual email', [
                'email' => $validated['email'],
                'subject' => $validated['subject'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()->with('error', 'Failed to send email: ' . $e->getMessage());
        }
    }

    /**
     * Send bulk email to members.
     */
    public function sendBulkEmail(Request $request)
    {
        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'recipient_type' => 'required|string|in:all_members,active_members,selected_members',
            'selected_members' => 'nullable|array',
            'selected_members.*' => 'exists:users,id',
        ]);

        // Get recipients based on type
        $recipients = $this->getRecipients($validated['recipient_type'], $validated['selected_members'] ?? []);

        if (empty($recipients)) {
            return redirect()->back()->with('error', 'No recipients found.');
        }

        $successful = 0;
        $failed = 0;

        foreach ($recipients as $recipient) {
            try {
                Mail::to($recipient['email'])->send(new GeneralAnnouncement(
                    $recipient['name'],
                    $validated['subject'],
                    $validated['message']
                ));
                $successful++;
            } catch (\Exception $e) {
                Log::error('Failed to send bulk email to recipient', [
                    'email' => $recipient['email'],
                    'subject' => $validated['subject'],
                    'error' => $e->getMessage(),
                ]);
                $failed++;
            }
        }

        $message = "Bulk email completed: {$successful} sent successfully";
        if ($failed > 0) {
            $message .= ", {$failed} failed";
        }

        Log::info('Bulk email campaign completed', [
            'subject' => $validated['subject'],
            'successful' => $successful,
            'failed' => $failed,
            'sent_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', $message);
    }

    /**
     * Send meeting notification emails.
     */
    public function sendMeetingNotification(Request $request)
    {
        $validated = $request->validate([
            'meeting_id' => 'required|exists:meetings,id',
            'recipient_type' => 'required|string|in:all_members,attending_only',
        ]);

        $meeting = Meeting::with(['attendances.farmer.user'])->findOrFail($validated['meeting_id']);

        $recipients = [];
        if ($validated['recipient_type'] === 'all_members') {
            // Send to all members
            $recipients = User::whereHas('farmer')->with('farmer')->get()->map(function ($user) {
                return [
                    'email' => $user->email,
                    'name' => $user->name,
                ];
            })->toArray();
        } else {
            // Send only to those who are attending
            $recipients = $meeting->attendances
                ->where('response_status', 'attending')
                ->map(function ($attendance) {
                    return [
                        'email' => $attendance->farmer->user->email,
                        'name' => $attendance->farmer->user->name,
                    ];
                })->toArray();
        }

        if (empty($recipients)) {
            return redirect()->back()->with('error', 'No recipients found for meeting notification.');
        }

        $successful = 0;
        $failed = 0;

        foreach ($recipients as $recipient) {
            try {
                Mail::to($recipient['email'])->send(new MeetingNotification(
                    $recipient['name'],
                    $meeting->title,
                    $meeting->meeting_date,
                    $meeting->meeting_time,
                    $meeting->location,
                    $meeting->agenda,
                    url('/meetings/' . $meeting->id)
                ));
                $successful++;
            } catch (\Exception $e) {
                Log::error('Failed to send meeting notification email', [
                    'email' => $recipient['email'],
                    'meeting_id' => $meeting->id,
                    'error' => $e->getMessage(),
                ]);
                $failed++;
            }
        }

        $message = "Meeting notifications sent: {$successful} successful";
        if ($failed > 0) {
            $message .= ", {$failed} failed";
        }

        Log::info('Meeting notification emails sent', [
            'meeting_id' => $meeting->id,
            'successful' => $successful,
            'failed' => $failed,
            'sent_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', $message);
    }

    /**
     * Test email configuration.
     */
    public function testEmail(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
        ]);

        try {
            Mail::to($validated['email'])->send(new GeneralAnnouncement(
                'Test User',
                'Email Configuration Test',
                'This is a test email to verify that your email configuration is working correctly. If you receive this email, your MailerSend integration is functioning properly!'
            ));

            return redirect()->back()->with('success', 'Test email sent successfully!');
        } catch (\Exception $e) {
            Log::error('Test email failed', [
                'email' => $validated['email'],
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()->with('error', 'Test email failed: ' . $e->getMessage());
        }
    }

    /**
     * Get recipients based on type.
     */
    protected function getRecipients($type, $selectedMembers = [])
    {
        switch ($type) {
            case 'all_members':
                return User::whereHas('farmer')->get()->map(function ($user) {
                    return [
                        'email' => $user->email,
                        'name' => $user->name,
                    ];
                })->toArray();

            case 'active_members':
                return User::whereHas('farmer', function ($query) {
                    $query->where('status', 'active');
                })->get()->map(function ($user) {
                    return [
                        'email' => $user->email,
                        'name' => $user->name,
                    ];
                })->toArray();

            case 'selected_members':
                return User::whereIn('id', $selectedMembers)->get()->map(function ($user) {
                    return [
                        'email' => $user->email,
                        'name' => $user->name,
                    ];
                })->toArray();

            default:
                return [];
        }
    }
}
