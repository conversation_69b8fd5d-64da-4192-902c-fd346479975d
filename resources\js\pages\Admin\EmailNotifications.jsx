import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';

export default function EmailNotifications({ auth }) {
    const [showSendModal, setShowSendModal] = useState(false);
    const [showBulkModal, setShowBulkModal] = useState(false);
    const [showTestModal, setShowTestModal] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm({
        email: '',
        name: '',
        subject: '',
        message: '',
        type: 'announcement',
    });

    const { data: bulkData, setData: setBulkData, post: postBulk, processing: bulkProcessing, errors: bulkErrors, reset: resetBulk } = useForm({
        subject: '',
        message: '',
        recipient_type: 'all_members',
        selected_members: [],
    });

    const { data: testData, setData: setTestData, post: postTest, processing: testProcessing, errors: testErrors, reset: resetTest } = useForm({
        email: '',
    });

    const handleSendEmail = (e) => {
        e.preventDefault();
        post('/admin/emails/send', {
            onSuccess: () => {
                reset();
                setShowSendModal(false);
            }
        });
    };

    const handleBulkEmail = (e) => {
        e.preventDefault();
        postBulk('/admin/emails/bulk', {
            onSuccess: () => {
                resetBulk();
                setShowBulkModal(false);
            }
        });
    };

    const handleTestEmail = (e) => {
        e.preventDefault();
        postTest('/admin/emails/test', {
            onSuccess: () => {
                resetTest();
                setShowTestModal(false);
            }
        });
    };

    return (
        <AdminLayout auth={auth}>
            <Head title="Email Notifications - Admin" />
            
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-7xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Email Notifications</h1>
                                <p className="mt-2 text-gray-600">Send and manage email notifications to members</p>
                            </div>
                            <div className="flex space-x-4">
                                <Link
                                    href="/dashboard/admin"
                                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    ← Back to Dashboard
                                </Link>
                                <button
                                    onClick={() => setShowTestModal(true)}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                                >
                                    🧪 Test Email
                                </button>
                                <button
                                    onClick={() => setShowBulkModal(true)}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                                >
                                    📢 Bulk Email
                                </button>
                                <button
                                    onClick={() => setShowSendModal(true)}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                                >
                                    📧 Send Email
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Email Status */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt className="text-sm font-medium text-gray-500 truncate">Email System</dt>
                                        <dd className="text-lg font-medium text-green-600">Active</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <svg className="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt className="text-sm font-medium text-gray-500 truncate">Mail Provider</dt>
                                        <dd className="text-lg font-medium text-gray-900">Gmail SMTP</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <svg className="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt className="text-sm font-medium text-gray-500 truncate">From Address</dt>
                                        <dd className="text-sm font-medium text-gray-900"><EMAIL></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <svg className="h-8 w-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt className="text-sm font-medium text-gray-500 truncate">Auto Welcome</dt>
                                        <dd className="text-lg font-medium text-green-600">Enabled</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Email Management Dashboard */}
                    <div className="bg-white rounded-lg shadow overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">Email Management</h3>
                            <p className="mt-1 text-sm text-gray-600">Send and manage email communications to cooperative members</p>
                        </div>

                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="text-center">
                                    <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-md bg-green-100 text-green-600">
                                        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">Individual Emails</h3>
                                    <p className="mt-1 text-sm text-gray-500">Send targeted emails to specific members</p>
                                </div>

                                <div className="text-center">
                                    <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-blue-600">
                                        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">Bulk Announcements</h3>
                                    <p className="mt-1 text-sm text-gray-500">Send announcements to all members</p>
                                </div>

                                <div className="text-center">
                                    <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-md bg-purple-100 text-purple-600">
                                        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">Test Configuration</h3>
                                    <p className="mt-1 text-sm text-gray-500">Verify email system is working</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Send Email Modal */}
            {showSendModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg max-w-lg w-full p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Send Individual Email</h3>
                        
                        <form onSubmit={handleSendEmail}>
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Email</label>
                                <input
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                                {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Name</label>
                                <input
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                                {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                                <input
                                    type="text"
                                    value={data.subject}
                                    onChange={(e) => setData('subject', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                                {errors.subject && <p className="mt-1 text-sm text-red-600">{errors.subject}</p>}
                            </div>

                            <div className="mb-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                                <textarea
                                    value={data.message}
                                    onChange={(e) => setData('message', e.target.value)}
                                    rows={4}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                                {errors.message && <p className="mt-1 text-sm text-red-600">{errors.message}</p>}
                            </div>

                            <div className="flex space-x-3">
                                <button
                                    type="submit"
                                    disabled={processing}
                                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                                >
                                    {processing ? 'Sending...' : 'Send Email'}
                                </button>
                                <button
                                    type="button"
                                    onClick={() => {
                                        setShowSendModal(false);
                                        reset();
                                    }}
                                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            {/* Bulk Email Modal */}
            {showBulkModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg max-w-lg w-full p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Send Bulk Email</h3>
                        
                        <form onSubmit={handleBulkEmail}>
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Recipients</label>
                                <select
                                    value={bulkData.recipient_type}
                                    onChange={(e) => setBulkData('recipient_type', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                >
                                    <option value="all_members">All Members</option>
                                    <option value="active_members">Active Members Only</option>
                                </select>
                                {bulkErrors.recipient_type && <p className="mt-1 text-sm text-red-600">{bulkErrors.recipient_type}</p>}
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                                <input
                                    type="text"
                                    value={bulkData.subject}
                                    onChange={(e) => setBulkData('subject', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                                {bulkErrors.subject && <p className="mt-1 text-sm text-red-600">{bulkErrors.subject}</p>}
                            </div>

                            <div className="mb-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                                <textarea
                                    value={bulkData.message}
                                    onChange={(e) => setBulkData('message', e.target.value)}
                                    rows={4}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                                {bulkErrors.message && <p className="mt-1 text-sm text-red-600">{bulkErrors.message}</p>}
                            </div>

                            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                                <div className="flex">
                                    <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-yellow-800">Warning</h3>
                                        <p className="mt-1 text-sm text-yellow-700">
                                            This will send emails to multiple recipients. Please review your message carefully before sending.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="flex space-x-3">
                                <button
                                    type="submit"
                                    disabled={bulkProcessing}
                                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                                >
                                    {bulkProcessing ? 'Sending...' : 'Send Bulk Email'}
                                </button>
                                <button
                                    type="button"
                                    onClick={() => {
                                        setShowBulkModal(false);
                                        resetBulk();
                                    }}
                                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            {/* Test Email Modal */}
            {showTestModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg max-w-md w-full p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Email Configuration</h3>
                        
                        <form onSubmit={handleTestEmail}>
                            <div className="mb-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Test Email Address</label>
                                <input
                                    type="email"
                                    value={testData.email}
                                    onChange={(e) => setTestData('email', e.target.value)}
                                    placeholder="<EMAIL>"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                                {testErrors.email && <p className="mt-1 text-sm text-red-600">{testErrors.email}</p>}
                                <p className="mt-1 text-sm text-gray-500">
                                    We'll send a test email to verify your MailerSend configuration is working.
                                </p>
                            </div>

                            <div className="flex space-x-3">
                                <button
                                    type="submit"
                                    disabled={testProcessing}
                                    className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50"
                                >
                                    {testProcessing ? 'Sending...' : 'Send Test Email'}
                                </button>
                                <button
                                    type="button"
                                    onClick={() => {
                                        setShowTestModal(false);
                                        resetTest();
                                    }}
                                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </AdminLayout>
    );
}
